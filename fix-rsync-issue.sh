#!/bin/bash

# Fix for rsync symlink conflicts in LA build
# This script removes the completion marker and cleans up partial state

BUILD_DIR="/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/r00002.1"
LA_SYNC_DIR="${BUILD_DIR}/snapdragon-auto-hqx-4-8-9-0_hlos_dev_la"
VENDOR_BUILD_DIR="${LA_SYNC_DIR}/VENDOR_BUILD_DIR"
COMPLETION_MARKER="${VENDOR_BUILD_DIR}/system_oss_prop_complete"

echo "Fixing rsync symlink conflicts..."

# Check if completion marker exists
if [ -f "$COMPLETION_MARKER" ]; then
    echo "Removing completion marker: $COMPLETION_MARKER"
    rm -f "$COMPLETION_MARKER"
else
    echo "Completion marker not found: $COMPLETION_MARKER"
fi

# Clean up .repo directory which is causing most symlink conflicts
if [ -d "$VENDOR_BUILD_DIR/.repo" ]; then
    echo "Removing .repo directory to avoid symlink conflicts..."
    rm -rf "$VENDOR_BUILD_DIR/.repo"
fi

echo "Cleanup completed. You can now re-run the build."
