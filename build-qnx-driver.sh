#!/bin/bash -x

# QNX Driver Build Script for <PERSON>'s team
# This script builds only the QNX drivers after changes are made

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

CHIPCODE_TAG="r00002.1"
BUILD_DIR="/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/${CHIPCODE_TAG}"

# QNX Stuff
QNX_DIR="${HOME}/qnx" # Dir which has qnxsoftwarecenter
QNX_LICENSE_DIR="${HOME}/.qnx/license" 

# Use the same prebuilt dir as main build
PREBUILT_DIR="${SCRIPT_DIR}/qnx-patches/prebuilt_sdp800"
BUNDLE_DIR="${SCRIPT_DIR}/${CHIPCODE_TAG}"

if [ ! -d "$PREBUILT_DIR" ]; then
    echo "Prebuilt not found at $PREBUILT_DIR, run qnx-patches/setup-qnx.sh to generate it"
    exit 1
fi

if [ ! -d "$BUNDLE_DIR" ]; then
    echo "Bundle not found at $BUNDLE_DIR"
    exit 1
fi

# Check if QNX sync directory exists (should be created by main build)
QNX_SYNC_DIR="${BUILD_DIR}"
if [ ! -d "$QNX_SYNC_DIR/apps/qnx_ap" ]; then
    echo "Error: QNX sync directory not found at $QNX_SYNC_DIR/apps/qnx_ap"
    echo "Please run the main build first (build-platform.sh) to create the QNX source tree"
    exit 1
fi

# Check if we have the docker container necessary for the bundle build
cd "$BUNDLE_DIR/toolchain"
if [ ! -f "./build-build.sh" ]; then
    echo "Error: build-build.sh not found in $BUNDLE_DIR/toolchain"
    exit 1
fi
source ./build-build.sh # Docker image for that bundle build

VOLUME_MAP="--volume /dev:/dev:rw \
--volume /lib/modules:/lib/modules:rw \
--volume ${BUNDLE_DIR}:/opt/qcom/qrtek/mybundle:rw \
--volume /local/mnt/rideconsole/runtime:/runtime:rw \
--volume ${QNX_DIR}:/qnx:rw \
--volume ${QNX_LICENSE_DIR}:/root/.qnx/license:rw \
--volume ${PREBUILT_DIR}:/qnx/my_prebuilt_sdp800:rw"

# Create a script to run inside the container
QNX_BUILD_SCRIPT="/tmp/qnx_driver_build.sh"
cat > "$QNX_BUILD_SCRIPT" << 'EOF'
#!/bin/bash -xe

# Set up QNX environment variables
QNX_SYNC_DIR="/runtime/build/8255_4890_hqxla/r00002.1"
QNX_PREBUILT_DIR="/qnx/my_prebuilt_sdp800"
QNX_TOOLCHAIN="qhs220"

# Navigate to QNX apps directory
cd "$QNX_SYNC_DIR/apps/qnx_ap"

# Source the QNX environment
source "setenv_${QNX_TOOLCHAIN}.sh" --external "$QNX_PREBUILT_DIR"

# Set up SECTOOLS environment
export SECTOOLS="$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools"
export SECTOOLS_V2_ROOT="$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools"

echo "=== Building QNX Driver ==="

# Step 1: Build the iosock_ethernet driver
echo "Step 1: Building iosock_ethernet driver..."
cd "$QNX_SYNC_DIR/apps/qnx_ap/AMSS/amc/data/iosock_ethernet"
echo "Current directory: $(pwd)"
echo "Running: make clean"
make clean
echo "Running: make install"
make install

# Step 2: Repackage the images
echo "Step 2: Repackaging images..."
cd "$QNX_SYNC_DIR/apps/qnx_ap/target/hypervisor/host"
echo "Current directory: $(pwd)"
echo "Running: make"
make

echo "=== QNX Driver Build Complete ==="
echo "Updated images are available in:"
echo "  $QNX_SYNC_DIR/apps/qnx_ap/target/hypervisor/host/out_lemans/"
EOF

chmod +x "$QNX_BUILD_SCRIPT"

# Run the build inside the Docker container
DOCKER_CMD="docker run \
-e PYTHONDONTWRITEBYTECODE=1 \
--ipc=host --net=host \
--volume ${QNX_BUILD_SCRIPT}:/tmp/qnx_driver_build.sh:ro \
${VOLUME_MAP} ${QRIDE_TOOLCHAIN} \
/tmp/qnx_driver_build.sh"

echo "Running QNX driver build in Docker container..."
$DOCKER_CMD

# Clean up
rm -f "$QNX_BUILD_SCRIPT"

echo ""
echo "=== Build Summary ==="
echo "QNX driver build completed successfully!"
echo "You can now flash the updated images using:"
echo "  cd ${BUNDLE_DIR}/toolkit"
echo "  ./flash_iosock.sh --buildpath ${BUILD_DIR} --numsoc 1"
