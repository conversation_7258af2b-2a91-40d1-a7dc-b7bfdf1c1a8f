#!/bin/bash
set -e

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
CHIPCODE_TAG="r00002.1"
BUILD_DIR="/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/${CHIPCODE_TAG}"
FLAT_BUILD_DIR="${BUILD_DIR}/FLAT_BUILD"

[ -f "$BUILD_DIR/contents.xml" ] || { echo "ERROR: Run ./build-platform.sh first"; exit 1; }
source "${SCRIPT_DIR}/${CHIPCODE_TAG}/toolkit/bsp_config"

PCAT_CMD=$(command -v pcat || echo "/opt/qcom/PCAT/bin/pcat.sh")
[ -x "$PCAT_CMD" ] || { echo "ERROR: pcat not found"; exit 1; }

sudo mkdir -p "$FLAT_BUILD_DIR" && sudo chown $USER:$USER "$FLAT_BUILD_DIR"

echo "Creating Flat Build via PCAT..."
mkdir -p "$FLAT_BUILD_DIR/SAIL" "$FLAT_BUILD_DIR/MD"

echo "Flattening SAIL..."
$PCAT_CMD -flatten -build "$BUILD_DIR/contents.xml" -memorytype spinor -flavor "$SAIL_TYPE" -OUT "$FLAT_BUILD_DIR/SAIL"

echo "Flattening Main Domain..."
$PCAT_CMD -flatten -build "$BUILD_DIR/contents.xml" -memorytype "$MEM_TYPE" -flavor "$PRODUCT_FLAVOR" -OUT "$FLAT_BUILD_DIR/MD"

export FLAT_BUILD_DIR
echo "FLAT_BUILD_DIR=${FLAT_BUILD_DIR}" | tee -a "$SCRIPT_DIR/runtime.vars"