# ID: SDP803EA_QC_INTERNAL_ONLY_20250718
# Author: <PERSON><PERSON><PERSON> (ran<PERSON><PERSON>@bqnx.com)
#
# DO NOT DISTRIBUTE TO CUSTOMERS!
# For Qualcomm internal use only.
#

com.qnx.qnx800.bsp.hw.raspberrypi_bcm2711_rpi4/0.2.0.00313T202507151603L
com.qnx.qnx800.bsp.hypervisor_guest_arm/0.1.0.00108T202501061711L
com.qnx.qnx800.bsp.hypervisor_guest_x86/0.1.0.00108T202501061711L
com.qnx.qnx800.eula.qdl/0.4.0.00135T202311191043L
com.qnx.qnx800.host.linux.x86_64.binutils/2.43.0.00101T202411221147L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.bison/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.build.thirdparty/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.cndfs/8.0.0.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.dumpefs/0.0.1.00007T202411221949L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.elfnote/0.0.2.00009T202411221949L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.fsutils/0.0.1.00350T202405161502L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.gcc/12.2.0.00301T202506091801L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.gdb/14.2.0.00301T202506271133L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.heartbeat/8.0.0.00072T202411221948L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.idehooks/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.liblicense/0.0.1.00005T202411221534L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.misc/0.0.2.02005T202411230033L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.mkqfs/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.mkshadow/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.mkxfs/0.0.2.00007T202411221949L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.openssl/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.qcc/8.0.0.00120T202411231804L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.qchecksec/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.qlicense/8.0.0.00114T202411221950L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.rust/0.0.1.00014T202506301151L
com.qnx.qnx800.host.linux.x86_64.secpol/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.usemsg/1.0.0.00003T202411221950L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64.utils.kdserver/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.linux.x86_64/0.0.1.02005T202411230033L/(&(osgi.arch=x86_64)(osgi.os=linux)(osgi.ws=gtk))
com.qnx.qnx800.host.mkqnximage/0.3.0.00586T202507141513L
com.qnx.qnx800.host.nto.gdb/14.2.0.00301T202506271133L
com.qnx.qnx800.host.qnxsdp_env/0.0.1.00135T202311191043L
com.qnx.qnx800.host.win.x86_64.binutils/2.43.0.00101T202411221147L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.bison/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.build.thirdparty/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.cndfs/8.0.0.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.dumpefs/0.0.1.00007T202411221949L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.elfnote/0.0.2.00009T202411221949L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.extra/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.fsutils/0.0.1.00350T202405161502L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.gcc/12.2.0.00301T202506091801L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.gdb/14.2.0.00301T202506271133L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.heartbeat/8.0.0.00072T202411221948L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.idehooks/0.0.1.00282T202404151301L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.liblicense/0.0.1.00005T202411221534L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.misc/0.0.2.02005T202411230033L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.mkqfs/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.mkshadow/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.mkxfs/0.0.2.00007T202411221949L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.net.curl/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.openssl/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.qcc/8.0.0.00120T202411231804L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.qchecksec/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.qlicense/8.0.0.00114T202411221950L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.qnxlm2/0.0.2.00056T202405150950L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.rust/0.0.1.00019T202507091119L
com.qnx.qnx800.host.win.x86_64.secpol/0.3.0.00586T202507141513L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.usemsg/1.0.0.00003T202411221950L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64.utils.kdserver/0.0.1.00135T202311191043L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.host.win.x86_64/0.0.1.02005T202411230033L/(&(osgi.arch=x86_64)(osgi.os=win32)(osgi.ws=win32))
com.qnx.qnx800.osr.bsdtar/3.7.7.00051T202503101550L
com.qnx.qnx800.osr.googletest/1.11.0.00021T202311191344L
com.qnx.qnx800.osr.less/0.0.670.00040T202501111907L
com.qnx.qnx800.osr.libffi/3.2.1.00021T202311191417L
com.qnx.qnx800.osr.sqlite3/0.0.1.00478T202409051224L
com.qnx.qnx800.osr.strongswan/5.8.2.00063T202407082031L
com.qnx.qnx800.osr.toybox/0.8.11.00003T202411230227L
com.qnx.qnx800.osr.zoneinfo/2023.3.0.00042T202311191732L
com.qnx.qnx800.target.aa64idreg/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.bootfiles/0.1.0.00021T202411230047L
com.qnx.qnx800.target.base.compression/0.3.0.00586T202507141513L
com.qnx.qnx800.target.base.cpp/18.0.0.01300T202506092154L
com.qnx.qnx800.target.base.elf/0.0.2.00009T202411221949L
com.qnx.qnx800.target.base.ipl.utils/0.1.0.00006T202411230051L
com.qnx.qnx800.target.base.ipl/0.1.0.00006T202411230051L
com.qnx.qnx800.target.base.libcache/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.libcatalog/1.0.0.00586T202507141513L
com.qnx.qnx800.target.base.libdrvr/0.0.1.00443T202311201012L
com.qnx.qnx800.target.base.libelf/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.libexpat/2.7.1.00346T202504030847L
com.qnx.qnx800.target.base.libforksafe_mutex/2.2.0.00586T202507141513L
com.qnx.qnx800.target.base.libm/0.0.2.02005T202411230033L
com.qnx.qnx800.target.base.libregex/1.0.0.00586T202507141513L
com.qnx.qnx800.target.base.libstartup/1.0.0.00586T202507141513L
com.qnx.qnx800.target.base.libstringsa64/1.0.0.00586T202507141513L
com.qnx.qnx800.target.base.libxml2/0.0.2.02554T202506201101L
com.qnx.qnx800.target.base.mkfiles/0.0.2.02005T202411230033L
com.qnx.qnx800.target.base.shutdown/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.slm/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.startup/0.0.1.00443T202311201012L
com.qnx.qnx800.target.base.text.icu/0.0.1.00135T202311191043L
com.qnx.qnx800.target.base.text.ncurses/0.0.2.00012T202411230232L
com.qnx.qnx800.target.base/0.0.1.00135T202311191043L
com.qnx.qnx800.target.binutils/2.43.0.00101T202411221147L
com.qnx.qnx800.target.bsp.i2c.utils/0.1.0.00006T202411230100L
com.qnx.qnx800.target.bsp.i2c/0.0.1.00443T202311201012L
com.qnx.qnx800.target.bsp.spi/0.1.2.00015T202501271455L
com.qnx.qnx800.target.connectivity.can.utils/0.1.0.00009T202411230055L
com.qnx.qnx800.target.connectivity.can/0.0.1.00443T202311201012L
com.qnx.qnx800.target.connectivity.char/0.1.0.00011T202411230100L
com.qnx.qnx800.target.connectivity.devc/0.2.0.00011T202411230100L
com.qnx.qnx800.target.connectivity.io_char/0.2.0.00011T202411230100L
com.qnx.qnx800.target.docs.plugins/0.0.1.00599T202412040959L
com.qnx.qnx800.target.driver.virtio.devb/0.1.0.00011T202411230104L
com.qnx.qnx800.target.driver.virtio.devc/0.1.1.00011T202411230100L
com.qnx.qnx800.target.driver.virtio.startup/0.1.0.00021T202411230047L
com.qnx.qnx800.target.driver.virtio/0.1.0.02005T202411230033L
com.qnx.qnx800.target.drivers.dma/0.0.1.00443T202311201012L
com.qnx.qnx800.target.drivers.dvfs/0.0.1.00443T202311201012L
com.qnx.qnx800.target.drivers.flash/0.1.1.00023T202507101612L
com.qnx.qnx800.target.drivers.libfdt/0.1.0.00392T202406181101L
com.qnx.qnx800.target.fs.base/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.cam/0.0.1.00443T202311201012L
com.qnx.qnx800.target.fs.cndfs/0.0.1.00135T202311191043L
com.qnx.qnx800.target.fs.dosfs/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.extfs/0.0.3.00586T202507141513L
com.qnx.qnx800.target.fs.flash3/0.0.6.00586T202507141513L
com.qnx.qnx800.target.fs.gpt/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.notify/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.qcfs/0.0.3.02005T202411230033L
com.qnx.qnx800.target.fs.qnx6fs/0.0.3.00586T202507141513L
com.qnx.qnx800.target.fs.qtdfs/0.0.3.02005T202411230033L
com.qnx.qnx800.target.fs.squashfs/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.udffs/0.0.2.02005T202411230033L
com.qnx.qnx800.target.fs.utils/0.0.2.02005T202411230033L
com.qnx.qnx800.target.graphics.group/4.0.1.00106T202312020421L
com.qnx.qnx800.target.hw/0.0.1.00443T202311201012L
com.qnx.qnx800.target.hypervisor.core/3.0.0.00612T202506262115L
com.qnx.qnx800.target.hypervisor.extras/3.0.0.00549T202504291550L
com.qnx.qnx800.target.hypervisor.libhyp/2.0.3.02513T202504291302L
com.qnx.qnx800.target.hypervisor.vdev.devel/3.1.0.00549T202504291550L
com.qnx.qnx800.target.input.devh/0.0.1.00443T202311201012L
com.qnx.qnx800.target.input.keyboard/0.1.0.00007T202411230047L
com.qnx.qnx800.target.libbacktrace/0.0.1.00093T202411221948L
com.qnx.qnx800.target.microkernel.core/2.2.0.00586T202507141513L
com.qnx.qnx800.target.microkernel.kdumper/0.0.2.02005T202411230033L
com.qnx.qnx800.target.microkernel.libslog2.extra/0.2.2.00586T202507141513L
com.qnx.qnx800.target.microkernel.libslog2/0.2.2.00586T202507141513L
com.qnx.qnx800.target.microkernel.slogger2/0.2.1.00586T202507141513L
com.qnx.qnx800.target.microkernel.support/2.2.0.00586T202507141513L
com.qnx.qnx800.target.microkernel.tools/0.0.3.00586T202507141513L
com.qnx.qnx800.target.net.curl/0.0.2.00446T202407261305L
com.qnx.qnx800.target.net.devs.dwceqos/0.3.0.00100T202507091441L
com.qnx.qnx800.target.net.devs.ffec/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.devs.genet/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.devs.marvellphy/0.3.0.00100T202507091441L
com.qnx.qnx800.target.net.devsddk/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.devsfdt/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.devspci/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.devsusb/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.devsvirtio/0.0.3.02005T202411230033L
com.qnx.qnx800.target.net.devutils/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.dhcp/0.3.0.00012T202507121955L
com.qnx.qnx800.target.net.iosock/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.iperf2/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.iperf3/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.mdnsresponder/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.modsphy/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.netmap/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.nfs/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.ntp/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.openSSH/0.1.0.02085T202503261326L
com.qnx.qnx800.target.net.pcap/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.ptpd2/0.3.0.00010T202506101707L
com.qnx.qnx800.target.net.tcpdump/0.2.0.02504T202504081900L
com.qnx.qnx800.target.net.unbound/0.0.2.00417T202407041502L
com.qnx.qnx800.target.net.utils.if_up/0.3.0.00100T202507091441L
com.qnx.qnx800.target.net.utils.ifwatchd/0.3.0.00100T202507091441L
com.qnx.qnx800.target.net.utils/0.3.0.00586T202507141513L
com.qnx.qnx800.target.net.vdevpeernet/0.2.1.02514T202505011059L
com.qnx.qnx800.target.net.wpa_supplicant_2.11.iosock/0.0.4.00004T202505280851L
com.qnx.qnx800.target.os_services.base/0.0.2.02005T202411230033L
com.qnx.qnx800.target.os_services.ham/0.0.2.02005T202411230033L
com.qnx.qnx800.target.os_services.hid/0.1.0.00008T202412121448L
com.qnx.qnx800.target.os_services.libdevice_publisher/0.0.1.00443T202311201012L
com.qnx.qnx800.target.os_services.libqh/0.0.1.00135T202311191043L
com.qnx.qnx800.target.os_services.server_monitor/1.0.0.00586T202507141513L
com.qnx.qnx800.target.os_services.smmu.api/0.0.2.02005T202411230033L
com.qnx.qnx800.target.os_services.smmu.core/0.0.2.02005T202411230033L
com.qnx.qnx800.target.pci.capabilities/3.0.1.02005T202411230033L
com.qnx.qnx800.target.pci.compat/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.core.group/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.debug/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.hw.fdt/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.hw.x86/3.1.0.02005T202411230033L
com.qnx.qnx800.target.pci.lib/3.0.3.02005T202411230033L
com.qnx.qnx800.target.pci.mux/1.0.1.03036T202507071425L
com.qnx.qnx800.target.pci.server/3.1.1.02005T202411230033L
com.qnx.qnx800.target.pci.srvrmod_error_handling/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.srvrmod_event/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.srvrmod_misc/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.srvrmod_qvm_support/3.0.2.02005T202411230033L
com.qnx.qnx800.target.pci.uisupport/3.0.0.02005T202411230033L
com.qnx.qnx800.target.pci.utils/3.0.0.02005T202411230033L
com.qnx.qnx800.target.qemuvirt/0.2.1.00083T202507100128L
com.qnx.qnx800.target.qplms.docs.html/1.0.1.00019T202504291124L
com.qnx.qnx800.target.qplms.utils/1.0.1.00019T202504291124L
com.qnx.qnx800.target.qplms/1.0.1.00019T202504291124L
com.qnx.qnx800.target.qscan/0.0.2.02005T202411230033L
com.qnx.qnx800.target.screen.base.wfd_server/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.base/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.demos_tutorials.source/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.device_input/0.1.0.00007T202411230047L
com.qnx.qnx800.target.screen.device_input_screen/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.docs.plugins/4.0.1.00117T202501221715L
com.qnx.qnx800.target.screen.fonts.engine/0.0.1.00020T202311191218L
com.qnx.qnx800.target.screen.img_codecs/0.0.2.00108T202504090902L
com.qnx.qnx800.target.screen.kalman/0.0.1.00443T202311201012L
com.qnx.qnx800.target.screen.screen_utils/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.video_capture/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.vulkan.sdk/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.vulkansc.sdk/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.vulkansc/4.0.2.00132T202501161817L
com.qnx.qnx800.target.screen.wfd_server.demos_tutorials.source/4.0.2.00132T202501161817L
com.qnx.qnx800.target.security.authentication/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.crypto.cryptodev/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.crypto.openssl3/0.3.0.00586T202507141513L
com.qnx.qnx800.target.security.crypto.pkcs11/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.crypto.qcrypto.certicom/0.0.2.02005T202411230033L
com.qnx.qnx800.target.security.crypto.qcrypto.core/0.0.2.02005T202411230033L
com.qnx.qnx800.target.security.crypto.qcrypto.openssl3/0.0.2.02029T202501171702L
com.qnx.qnx800.target.security.crypto.qcrypto.tomcrypt/0.3.0.00586T202507141513L
com.qnx.qnx800.target.security.crypto.qcrypto.tools/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.pam/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.random/0.0.1.00135T202311191043L
com.qnx.qnx800.target.security.secpol.support/0.3.0.00586T202507141513L
com.qnx.qnx800.target.security.secpol.tools/0.3.0.00586T202507141513L
com.qnx.qnx800.target.security.secpol/0.3.0.00586T202507141513L
com.qnx.qnx800.target.security.tcg/0.3.0.00586T202507141513L
com.qnx.qnx800.target.sound.alsa/1.0.0.00247T202506201141L
com.qnx.qnx800.target.sound.io_snd/1.0.0.00247T202506201141L
com.qnx.qnx800.target.sound.simulator/1.0.0.00247T202506201141L
com.qnx.qnx800.target.sound.usb/1.0.0.00247T202506201141L
com.qnx.qnx800.target.text.charconv/0.0.1.00135T202311191043L
com.qnx.qnx800.target.tools.cpp/12.2.0.00301T202506091801L
com.qnx.qnx800.target.usb.base/0.1.0.00012T202412201348L
com.qnx.qnx800.target.usb.dcd.dwc3/0.1.0.00012T202412201348L
com.qnx.qnx800.target.usb.dcd.ut/0.0.1.00443T202311201012L
com.qnx.qnx800.target.usb.dcd/0.1.0.00012T202412201348L
com.qnx.qnx800.target.utils.base.b/0.0.1.02077T202503111502L
com.qnx.qnx800.target.utils.base.c/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.coreutils/1.0.0.02005T202411230033L
com.qnx.qnx800.target.utils.base.d/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.e/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.f/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.g/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.h/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.i/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.k/0.0.1.00282T202404151301L
com.qnx.qnx800.target.utils.base.l/0.0.2.02005T202411230033L
com.qnx.qnx800.target.utils.base.m/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.n/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.p/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.r/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.s/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.t/0.0.1.00282T202404151301L
com.qnx.qnx800.target.utils.base.u/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.w/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.base.x/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.debugtools/0.0.2.02005T202411230033L
com.qnx.qnx800.target.utils.gawk/0.0.1.00282T202404151301L
com.qnx.qnx800.target.utils.hw/0.2.0.00014T202411230051L
com.qnx.qnx800.target.utils.libqnxnoexcept/0.0.5.00002T202502281449L
com.qnx.qnx800.target.utils.libunwind/1.8.2.00132T202507121912L
com.qnx.qnx800.target.utils.publishers.usblauncher.plugins/0.1.0.00012T202412201348L
com.qnx.qnx800.target.utils.publishers.usblauncher/0.1.0.00012T202412201348L
com.qnx.qnx800.target.utils.python3/3.11.7.00045T202404131337L
com.qnx.qnx800.target.utils.qnx/0.0.1.00282T202404151301L
com.qnx.qnx800.target.utils.system.dumpefs/0.0.1.00007T202411221949L
com.qnx.qnx800.target.utils.system.dumpifs/0.0.2.00007T202411221949L
com.qnx.qnx800.target.utils.system/0.0.2.02005T202411230033L
com.qnx.qnx800.target.utils.utlfd/0.0.1.00135T202311191043L
com.qnx.qnx800.target.utils.valgrind/3.17.0.00300T202506092235L
com.qnx.qnx800.target/0.0.1.00135T202311191043L
com.qnx.qnx800/8.0.0.00141T202311271501L

# <NAME_EMAIL>
com.qnx.qnx800.target.os_services.pps/0.0.1.00417T202407041502L
com.qnx.qnx800.target.net.devs.dwc/0.0.2.00417T202407041502L