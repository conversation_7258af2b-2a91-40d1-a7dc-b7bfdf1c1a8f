#!/bin/bash -xe

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
# swcenter-download-qsc downloads to $WORKSPACE/swc, not $SCRIPT_DIR/swc
WORKSPACE_DIR=$(cd "$SCRIPT_DIR/.." && pwd)
QNX_SWC_CLI="$WORKSPACE_DIR/swc/qnxsoftwarecenter_clt"
PREBUILT_DIR="$SCRIPT_DIR/prebuilt_sdp800"
echo "PREBUILT_DIR=${PREBUILT_DIR}" > "$WORKSPACE_DIR/runtime.vars"

if [ -f "$PREBUILT_DIR/prebuild_complete" ]; then
    echo "Prebuilt already exists at $PREBUILT_DIR, remove it to regenerate"
    exit 0
fi

if [ ! -f "$QNX_SWC_CLI" ]; then
    export PATH=$PATH:~/.local/bin
    python3 -m pip install --index-url https://pypi.bts.rim.net/simple swcenter_pypi -U --user
    swcenter-download-qsc
fi

rm -rf $HOME/.qnx/swupdate
rm -rf $PREBUILT_DIR
$QNX_SWC_CLI -mirrorBaseline qnx800 \
        -url http://anvil.bts.rim.net \
        -profileProperty org.eclipse.equinox.p2.director.optimizationType=ultraconservative \
        -importAndInstall $SCRIPT_DIR/SDP803EA_QC_INTERNAL_ONLY_20250718.plist -env qnx.exp=true \
        -destination $PREBUILT_DIR

# Preferred to add missing headers via packages instead of headers directly
# From com.qnx.qnx800_ea.target.bsp.spi/0.0.1.00024T202306091211E but 
cp $SCRIPT_DIR/missing_headers/spi-master.h $PREBUILT_DIR/target/qnx/usr/include/hw/spi-master.h
# From com.qnx.qnx800.target.net.devsddk/0.0.1.00135T202311191043L newer version of package is already in list, but doesn't have the necessary ptp header
cp $SCRIPT_DIR/missing_headers/ptp.h $PREBUILT_DIR/target/qnx/usr/include/devs/qnx/ptp.h

# -----------------------------------------------------------------------------
# Add legacy idle-hook macros removed from SDP 8.0.3 but still referenced by
# Qualcomm BSP source (e.g. sleep.c).
# -----------------------------------------------------------------------------
NEUTRINO_H="$PREBUILT_DIR/target/qnx/usr/include/sys/neutrino.h"
if [ -f "$NEUTRINO_H" ] && ! grep -q "_NTO_IH_CMD_SLEEP_ONLINE" "$NEUTRINO_H"; then
    echo "Adding legacy idle-hook macros to \
        $NEUTRINO_H"
    cat >> "$NEUTRINO_H" << 'EOF'

/* --------------------------------------------------------------------------
 * Legacy idle-hook macros for Qualcomm BSP compatibility
 * These were removed upstream in SDP 8.0.3 (git commit d62123e6c765)
 * -------------------------------------------------------------------------- */
#ifndef _NTO_IH_CMD_SLEEP_SETUP
#define _NTO_IH_CMD_SLEEP_SETUP          0x00000001u
#define _NTO_IH_CMD_SLEEP_BLOCK          0x00000002u
#define _NTO_IH_CMD_SLEEP_WAKEUP         0x00000004u
#define _NTO_IH_CMD_SLEEP_ONLINE         0x00000008u
#define _NTO_IH_RESP_NEEDS_BLOCK         0x00000001u
#define _NTO_IH_RESP_NEEDS_WAKEUP        0x00000002u
#define _NTO_IH_RESP_NEEDS_ONLINE        0x00000004u
#define _NTO_IH_RESP_SYNC_TIME           0x00000010u
#define _NTO_IH_RESP_SYNC_TLB            0x00000020u
#define _NTO_IH_RESP_SUGGEST_OFFLINE     0x00000100u
#define _NTO_IH_RESP_SLEEP_MODE_REACHED  0x00000200u
#define _NTO_IH_RESP_DELIVER_INTRS       0x00000400u
#endif /* legacy idle-hook macros */

EOF
fi

# symlink libxml2.so.2.12.5 to libxml2.so.2.13.8
ln -s $PREBUILT_DIR/target/qnx/usr/lib/libxml2.so.2.13.8 $PREBUILT_DIR/target/qnx/usr/lib/libxml2.so.2.12.5

# Marker file to indicate that prebuild is complete
# touch $PREBUILT_DIR/prebuild_complete