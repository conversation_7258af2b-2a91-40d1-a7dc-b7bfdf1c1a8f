# 8255 ******* HQX + LA

This script bundle is extracted from Snapdragon Ride Console from the latest version available at the time of writing this document (2025-05-22).

This will serve as a the source of truth for the scripts, which can later be sent to Qualcomm team to incoromrate major changes to the script so over time we can drop in the scripts from the Ride Console and in would work for QNX Teams.


# Current Knowledge Assumptions (Might need correction, as this is from the perspective of the QNX Infra team)

This script bundles have toolchain and toolkit dir which corrospond to the Qualcomm's internal codebase which isn't directly available to us but instead they are bundled and released as a part of the Ride Console.
- Toolchain codebase is what builds the container images which create the images usually tagged `qc-auto-build:vX.Y.Z`
- Toolkit codebase is more sort of collection of script which checkecout and build the relevant Qualcomm codebase using the container images created by the toolchain codebase.

There might be minor tweaks in each bundle to make it work for the specific target board, hence the naming of the bundle is `<target>-<chipcode version>-<what components are being built>`
For this one, we have `8255-*******-hqxla` which means
- `8255` is the target board
- `*******` is the chipcode version
- `hqxla` is the what components are being built

and the subdirectories with `r00002.1` are the different tags of the the chipcode version.
Currently we are using `r00002.1` but probably we will move to some other tag in the future.

# Requirements

This repo assumes that you have docker installed and you have access to the Qualcomm chipcode repos.

For flat build generation, we assume that the host has PCAT tools installed. PCAT can be installed using:
```bash
qpm-cli --install pcat
```
If PCAT is not available, the platform build will run successfully but flat build generation will fail.

# Setup Steps
1. Clone this repo
2. Run `qnx-patches/setup-qnx.sh` to prepare QNX environment for SDP800
3. Rename `.env.example` to `.env` and update it with your credentials
4. Run `./build-platform.sh` to build the platform
5. Run `./build-flatten.sh` to generate flat build files using pcat
6. Run `./build-cdt-packages.sh` to generate CDT packages for SX/MX configurations (optional)

# Flat Build

After successful platform build, generate flat build files:
```bash
./build-flatten.sh
```
Creates SAIL and MD flat builds in `/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/r00002.1/FLAT_BUILD/`

**Note**: Requires PCAT tools to be installed on the host. See Requirements section above.

# CDT Packages

After successful platform build, generate CDT (Configuration Data Table) packages for both SX and MX configurations:
```bash
./build-cdt-packages.sh
```

Creates CDT packages in `/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/r00002.1/FLAT_BUILD/`:
- **CDT_SX**: For single SoC configuration (LEMANSAU_QAM_1.1.0.bin)
- **CDT_MX**: For dual SoC configuration (LEMANSAU_QAM_2.1.0.bin)

Each package contains all necessary files for CDT provisioning with PCAT tools.

# Issues and Fixes History

## 2025-05-30
- **FIXED**: Android build duplicate module conflicts - Build failed with "module already defined" errors after fixing makefile violations:
```
error: vendor/qcom/proprietary/aosal: MODULE.TARGET.SHARED_LIBRARIES.libaosal already defined by vendor/qcom/aosal.
```
Found that both open source (CodeLinaro) and proprietary (chipcode) versions of the same modules were being copied to the build directory. Created `LA_resolve_duplicate_modules()` function in `la_sync_build.sh` to scan for duplicate Android.bp/Android.mk files and remove opensource versions when proprietary versions exist. Resolved 927 duplicate Android.mk files, allowing build to progress (Waiting for it to complete..).

## 2025-05-29
- **FIXED**: Android build makefile violations - Created new `LA_apply_makefile_fixes()` function in `la_sync_build.sh` to automatically add problematic Android.mk files to appropriate whitelists in `makefile_whitelist.py`:
  - Added `vendor/qcom/wlan/utils/halproxydaemon/Android.mk` to SHELL_WHITELIST (fixes $(shell) usage error)
  - Added `vendor/qcom/common/scripts/Android.mk` to RM_WHITELIST (fixes rm usage error)
  - Added `vendor/qcom/prebuilt_HY11/target/product/gen4_gvm_microdroid/Android.mk` to LOCAL_COPY_HEADERS_WHITELIST
  - Added `vendor/qcom/prebuilt_HY11/target/product/gen4_gvm/Android.mk` to LOCAL_COPY_HEADERS_WHITELIST
  - Added `vendor/qcom/mm-hab/uhab/Android.mk` to LOCAL_COPY_HEADERS_WHITELIST

### Build Flow and Fix Location
```
Main Flow (download-compile-build.sh):
  └── HLOS_sync_build() (hlos_sync_build.sh)
      └── LA_sync() + LA_build() (la_sync_build.sh)
          └── LA_build():
              1. Copy source files to build directory
              2. LA_apply_makefile_fixes() ← **FIX APPLIED HERE**
              3. source build/envsetup.sh
              4. lunch + prepare_vendor.sh
              5. bash build.sh ← **Makefile scanner runs here** (Android build script)
```

**Why this timing works**: The fix runs after source files are copied to the build directory but before the makefile violation scanner executes, ensuring the whitelist modifications are in place when needed.
```
============================================
PLATFORM_VERSION_CODENAME=REL
PLATFORM_VERSION=14
PRODUCT_INCLUDE_TAGS=com.android.mainline
TARGET_PRODUCT=gen4_gvm
TARGET_BUILD_VARIANT=userdebug
TARGET_ARCH=arm64
TARGET_ARCH_VARIANT=armv8-a
TARGET_CPU_VARIANT=generic
TARGET_2ND_ARCH=arm
TARGET_2ND_ARCH_VARIANT=armv7-a-neon
TARGET_2ND_CPU_VARIANT=cortex-a9
HOST_OS=linux
HOST_OS_EXTRA=Linux-6.8.0-52-generic-x86_64-Ubuntu-22.04.5-LTS
HOST_CROSS_OS=windows
BUILD_ID=UQ1A.240205.002
OUT_DIR=out
============================================

real    0m5.553s
user    0m5.866s
sys     0m2.013s
============================================
[build.sh]: Command: "python -B /runtime/build/8255_4890_hqxla/r00002.1/snapdragon-auto-hqx-4-8-9-0_hlos_dev_la/VENDOR_BUILD_DIR/vendor/qcom/opensource/core-utils/build/makefile-violation-scanner.py"
============================================
-----------------------------------------------------
 Checking makefile errors
      Checking dependency on kernel headers ......
      Checking $(shell) usage ......
      Checking recursive usage ......
      Checking rm usage ......
      Checking -Wno-error=date-time usage ......
      Checking local_copy_headers usage ......
      Checking TARGET_PRODUCT usage ......
      Checking is-product-in-list usage ......
      Checking ro.build.product usage ......
-----------------------------------------------------
-----------------------------------------------------
cnt_shell_error : 1
Error: Using $(shell) in below files. Please remove usage of $(shell)
    vendor/qcom/wlan/utils/halproxydaemon/Android.mk
-----------------------------------------------------
-----------------------------------------------------
cnt_rm_error : 1
Error: Using rm in below makefiles. Please remove use of rm to prevent recompilation.
    vendor/qcom/common/scripts/Android.mk
-----------------------------------------------------
-----------------------------------------------------
cnt_local_copy_headers_error : 3
Error: Using local_copy_headers in below makefiles. This will be deprecated soon, please remove.
    vendor/qcom/prebuilt_HY11/target/product/gen4_gvm_microdroid/Android.mk
    vendor/qcom/prebuilt_HY11/target/product/gen4_gvm/Android.mk
    vendor/qcom/mm-hab/uhab/Android.mk
-----------------------------------------------------

real    0m30.875s
user    0m5.573s
sys     0m0.429s
============================================
[build.sh]: FAILED: python -B /runtime/build/8255_4890_hqxla/r00002.1/snapdragon-auto-hqx-4-8-9-0_hlos_dev_la/VENDOR_BUILD_DIR/vendor/qcom/opensource/core-utils/build/makefile-violation-scanner.py
============================================
ERROR : "bash build.sh -j12 dist --target_only " failed with return="1"
 *  Terminal will be reused by tasks, press any key to close it.
```

## 2025-05-28
- Built stuck for over 20 hours on rsync of `VENDOR_BUILD_DIR`, checking further it was stuck in `D` state.
- Added progress monitoring to rsync operations in `la_sync_build.sh` and restarted the build again.
- Maybe remove the `c` flag from rsync operations to see if it helps? For now kill and restart worked but need to keep an eye on it.

## 2025-05-27
- Fixed QNX custom prebuilt directory not being used - bug in Qualcomm bundle scripts where QNX_sync_override() was overwriting the param `qnx_prebuilt_dir` passed to `download-compile-build.sh`

## 2025-05-26
- Added SDP800 packages to the toolchain so fix the dependency issue.
- new patch list method won't work as one of the packages is in an ea baseline.
- creating a simple dir in this repo with those missing headers and copying them to the prebuilt dir.

## 2025-05-23
- Working with Rikard to identidy sdp8 packages which might be able to statisfy this requirements.

## 2025-05-22
- Running using rideconsole directly and kept facing the issue of `/runtime/build/8255_4890_hqxla/r00002.1/apps/qnx_ap/AMSS/platform/qal/vm/clients/vmm_client/src/vmm_lib.c:28:10: fatal error: sys/pps.h: No such file or directory`
- [Ganesh Halthota](mailto:<EMAIL>) from Qualcomm team suggested to manually copy the PPS depecy from sdp7 toolchain

```
The r00002.1 release depends on below files from SDP7 toolchain to be available in their corresponding paths due to pps dependency -
•	cp -raf  pps.h   ${EXP_SDP80_20250108 Installation Absolute Path}/target/qnx/usr/include/sys/pps.h
•	cp -raf  spi-master.h   ${EXP_SDP80_20250108 Installation Absolute Path}/target/qnx/usr/include/hw/ spi-master.h
•	cp -raf  libpps*     ${EXP_SDP80_20250108 Installation Absolute Path}/target/qnx/aarch64le/lib/
•	cp -raf  target\qnx7\aarch64le\io-sock\lib\dll devs-dwc.so   {EXP_SDP80_20250108 Installation Absolute Path}/target/qnx/aarch64le/lib/dll/devs-dwc.so
•	cp -raf   ptp.h    {EXP_SDP80_20250108 Installation Absolute Path}/target/qnx/usr/include/devs/qnx/ptp.
You will need to manually copy these files in these locations to unblock the build.
The dependency will be removed for the next release.
```
