#!/bin/bash
#

ROOTDIR=$(dirname $0)
source $ROOTDIR/config

###
# Generate the list of targets given a name (arg $1)
# Name can 'all', full name with leading numbers, or a short name.
target_list()
{
	case "$1" in
		[0-9][0-9]-*) p="$1" ;;
		all) p="*" ;;
		*) p="*$1" ;;
	esac
	(cd $ROOTDIR/$TARGETDIR; eval ls $p) | sort
}

# List target with same number on the same line
make_parallel()
{
	p=""
	while read s; do
		if [ "${p:0:3}" = "${s:0:3}" ]; then
			p="$p $s"
		else
			[ "$p" = "" ] || echo $p
			p="$s"
		fi
	done
	[ "$p" = "" ] || echo $p
}

# Filters for list of target
filter=""
if [ "$1" = "-p" ]; then
	filter="| make_parallel"
	shift
fi

target=$1
[ "$target" = "" ] && target="all"

eval target_list "$target" $filter
