#!/bin/bash
#
# Copyright (c) 2022-2023 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

ROOTDIR=$(dirname $0)

source $ROOTDIR/config

parallel="-p"

###
show_usage()
{
	echo -e "Usage: build.sh [options] <all|target> [version]"
	echo -e "Options: --no-parallel, --login, --nocache, --nosquash"
	echo -e "Supported targets:\nall  \n$($ROOTDIR/list-targets.sh all)"
	exit $1
}

###
verbose=
init_log()
{
	rm -f build.log*
	echo "" > build.log
}
info_log()
{
	echo $*
	echo $* >> build.log
}
verb_log()
{
	[ "$verbose" = "" ] || echo $*
	echo $* >> build.log
}

build_opts="--compress"

###
# Build target
# $1 - target name (including its number)
run_build()
{
	s=$1
	tag="${REGISTRY}${s}:$version"
	info_log ">> building $tag ..."

	# Build args still (as of docker-17.05) don't seem to work with FROM keyword
	pproc_cmd="sed -e s#@TOOLCHAIN_VERSION@#$version#g -e s#@TOOLCHAIN_TARGET@#${REGISTRY}${s}#g $ROOTDIR/$TARGETDIR/$s"
	build_cmd="docker build $build_opts --network=host --build-arg TOOLCHAIN_TARGET=${s} --build-arg DOCKER_REGISTRY=$DOCKER_REGISTRY --tag $tag -f - $ROOTDIR"
	verb_log $build_cmd

	if [ "$verbose" = "" ]; then
		eval $pproc_cmd | eval $build_cmd >> build.log.$s || exit 1
	else
		eval $pproc_cmd | eval $build_cmd | tee -a build.log.$s || exit 1
	fi
}

# Parse command line args
while true; do
	case "$1" in
		--help)
			show_usage 0 # exits
			;;
		--nocache)
			build_opts="$build_opts --no-cache"
			shift
			;;
		--nosquash)
			nosquash=true
			shift
			;;
		--no-parallel)
			parallel=""
			shift
			;;
		--verbose)
			verbose=true
			shift
			;;
		*)
			break
			;;
	esac
done

target=$1
[ "$target" = "" ] && show_usage 1

version=$2
[ "$version" = "" ] && version=$($ROOTDIR/generate-version.sh)

init_log

# Check for and enable experimental features

experimental=$(docker version -f '{{.Server.Experimental}}')
if [ "$experimental" != "true" ]; then
	echo "*** Docker daemon experimental features (squash, etc) are not enabled."
	echo "*** Please enabled them in order to generate smaller images."
fi

[ "$nosquash" = "" -a "$experimental" = "true" ] && build_opts="$build_opts --squash"

# Fix permissions for all helper scripts to avoid umask issues.
# It's better to fix it upfront instead of having to add commands
# to Dockerfile.
chmod 755 scripts/*

# Build all targets
$ROOTDIR/list-targets.sh $parallel $target | while read target_list; do
	echo "processing $target_list ..."
	for s in $target_list; do
		run_build $s &
	done
	wait || exit
done
