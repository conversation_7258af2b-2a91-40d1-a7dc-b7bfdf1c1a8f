diff --git a/3rdparty/libjasper/jasper/jas_stream.h b/3rdparty/libjasper/jasper/jas_stream.h
index 6862329921..a3cfac504b 100644
--- a/3rdparty/libjasper/jasper/jas_stream.h
+++ b/3rdparty/libjasper/jasper/jas_stream.h
@@ -254,6 +254,8 @@ typedef struct {
     int flags;
 #if defined _WIN32 && !defined __MINGW__ && !defined __MINGW32__
     char pathname[MAX_PATH + 1];
+#elif defined __QNXNTO__
+    char pathname[_POSIX_PATH_MAX + 1];
 #else
     char pathname[PATH_MAX + 1];
 #endif
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4c0b3880fc..999b9127fd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -635,6 +635,9 @@ if(UNIX)
       set(OPENCV_LINKER_LIBS ${OPENCV_LINKER_LIBS} m pthread)
     elseif(EMSCRIPTEN)
       # no need to link to system libs with emscripten
+    elseif(${CMAKE_SYSTEM_NAME} MATCHES "QNX")
+        set(OPENCV_LINKER_LIBS ${OPENCV_LINKER_LIBS} m)
+        add_definitions( -D_POSIX_C_SOURCE=200112L -D_XOPEN_SOURCE=600 )
     else()
       set(OPENCV_LINKER_LIBS ${OPENCV_LINKER_LIBS} dl m pthread rt)
     endif()
diff --git a/modules/core/src/parallel.cpp b/modules/core/src/parallel.cpp
index 09ee3bb6de..af87a69116 100644
--- a/modules/core/src/parallel.cpp
+++ b/modules/core/src/parallel.cpp
@@ -946,7 +946,7 @@ int getNumberOfCPUs_()
 
 #endif
 
-#if !defined(_WIN32) && !defined(__APPLE__)
+#if !defined(_WIN32) && !defined(__APPLE__) && !defined(__QNXNTO__)
 
     static unsigned cpu_count_sysconf = (unsigned)sysconf( _SC_NPROCESSORS_ONLN );
     ncpus = minNonZero(ncpus, cpu_count_sysconf);
diff --git a/modules/core/src/system.cpp b/modules/core/src/system.cpp
index 872019dd9e..06dc32ae82 100644
--- a/modules/core/src/system.cpp
+++ b/modules/core/src/system.cpp
@@ -787,7 +787,7 @@ int64 getTickCount(void)
     LARGE_INTEGER counter;
     QueryPerformanceCounter( &counter );
     return (int64)counter.QuadPart;
-#elif defined __linux || defined __linux__
+#elif defined __linux || defined __linux__ || defined __QNXNTO__
     struct timespec tp;
     clock_gettime(CLOCK_MONOTONIC, &tp);
     return (int64)tp.tv_sec*1000000000 + tp.tv_nsec;
@@ -806,7 +806,7 @@ double getTickFrequency(void)
     LARGE_INTEGER freq;
     QueryPerformanceFrequency(&freq);
     return (double)freq.QuadPart;
-#elif defined __linux || defined __linux__
+#elif defined __linux || defined __linux__ || defined __QNXNTO__
     return 1e9;
 #elif defined __MACH__ && defined __APPLE__
     static double freq = 0;
diff --git a/modules/core/src/system.cpp b/modules/core/src/system.cpp
index a939f11..6f24cb4 100644
--- a/modules/core/src/system.cpp
+++ b/modules/core/src/system.cpp
@@ -44,6 +44,7 @@
 #include "precomp.hpp"
 #include <iostream>
 #include <ostream>
+#include <unistd.h>
 
 #include <opencv2/core/utils/configuration.private.hpp>
 #include <opencv2/core/utils/trace.private.hpp>
diff --git a/3rdparty/ittnotify/src/ittnotify/ittnotify_static.c b/3rdparty/ittnotify/src/ittnotify/ittnotify_static.c
index c3b16f9..2a3349c 100644
--- a/3rdparty/ittnotify/src/ittnotify/ittnotify_static.c
+++ b/3rdparty/ittnotify/src/ittnotify/ittnotify_static.c
@@ -266,7 +266,7 @@ static __itt_api_info api_list[] = {
 #pragma warning(pop)
 #endif /* ITT_PLATFORM==ITT_PLATFORM_WIN */
 
-static const char dll_path[PATH_MAX] = { 0 };
+static const char dll_path[_POSIX_PATH_MAX] = { 0 };
 
 /* static part descriptor which handles. all notification api attributes. */
 __itt_global _N_(_ittapi_global) = {

