diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8a9cb02..2920753 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -23,6 +23,10 @@ if(PROJECT_SOURCE_DIR)
     set(IS_TOP_LEVEL FALSE)
 endif()
 
+set( CMAKE_SYSROOT $ENV{QNX_TARGET}/aarch64le/ )
+add_compile_options("-I${CMAKE_SYSROOT}/usr/include/io-sock")
+add_link_options("-L${CMAKE_SYSROOT}/io-sock/lib")
+
 # Set CMAKE_BUILD_TYPE to Release by default.
 if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
     message(STATUS "Setting build type to 'Release' as none was specified.")
@@ -71,6 +75,7 @@ string(REGEX REPLACE "m4_define\\(\\[?version_micro\\]?, *\\[?([0-9]+)\\]?\\)"
 
 set(LIB_VERSION_STR "${LIB_MAJOR_VERSION}.${LIB_MINOR_VERSION}.${LIB_PATCH_VERSION}")
 
+
 ###############################################################################
 # Project                                                                     #
 ###############################################################################
diff --git a/cmake/modules/FindThirdpartyBoost.cmake b/cmake/modules/FindThirdpartyBoost.cmake
index 151457a..847cede 100644
--- a/cmake/modules/FindThirdpartyBoost.cmake
+++ b/cmake/modules/FindThirdpartyBoost.cmake
@@ -13,8 +13,10 @@ set(THIRDPARTY_BOOST_INCLUDE_DIR
 
 find_package(Threads REQUIRED)
 
-if(WIN32 OR APPLE OR ANDROID OR QNXNTO)
+if(WIN32 OR APPLE OR ANDROID)
     set(THIRDPARTY_BOOST_LINK_LIBS ${CMAKE_THREAD_LIBS_INIT})
+elseif(QNXNTO)
+    set(THIRDPARTY_BOOST_LINK_LIBS ${CMAKE_THREAD_LIBS_INIT} socket)
 else() # Posix
     set(THIRDPARTY_BOOST_LINK_LIBS ${CMAKE_THREAD_LIBS_INIT} rt)
 endif()
