diff --git a/CMakeLists.txt b/CMakeLists.txt
index f11bbb5..df5fb5c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -11,13 +11,10 @@ project(googletest-distribution)
 set(GOOGLETEST_VERSION 1.10.0)
 
 if (CMAKE_VERSION VERSION_LESS "3.1")
-  add_definitions(-std=c++11)
+  add_definitions(-std=gnu++14)
 else()
-  set(CMAKE_CXX_STANDARD 11)
+  set(CMAKE_CXX_STANDARD 14)
   set(CMAKE_CXX_STANDARD_REQUIRED ON)
-  if(NOT CYGWIN)
-    set(CMAKE_CXX_EXTENSIONS OFF)
-  endif()
 endif()
 
 enable_testing()
diff --git a/googletest/cmake/internal_utils.cmake b/googletest/cmake/internal_utils.cmake
index 2f70f0b..c1402a9 100644
--- a/googletest/cmake/internal_utils.cmake
+++ b/googletest/cmake/internal_utils.cmake
@@ -188,6 +188,7 @@ function(cxx_library_with_type name type cxx_flags)
     endif()
     target_link_libraries(${name} PUBLIC ${threads_spec})
   endif()
+  target_link_libraries(${name} PUBLIC regex)
 endfunction()
 
 ########################################################################
diff --git a/googletest/cmake/gtest.pc.in b/googletest/cmake/gtest.pc.in
index 9aae29e..db1b4c1 100644
--- a/googletest/cmake/gtest.pc.in
+++ b/googletest/cmake/gtest.pc.in
@@ -6,5 +6,5 @@ Name: gtest
 Description: GoogleTest (without main() function)
 Version: @PROJECT_VERSION@
 URL: https://github.com/google/googletest
-Libs: -L${libdir} -lgtest @CMAKE_THREAD_LIBS_INIT@
+Libs: -L${libdir} -lregex -lgtest @CMAKE_THREAD_LIBS_INIT@
 Cflags: -I${includedir} @GTEST_HAS_PTHREAD_MACRO@ @CMAKE_THREAD_LIBS_INIT@
