--- /usr/share/cmake-3.20/Modules/FindGTest.cmake.orig	2021-06-02 12:36:18.817137694 -0700
+++ /usr/share/cmake-3.20/Modules/FindGTest.cmake	2021-06-02 12:41:47.984954656 -0700
@@ -268,6 +268,7 @@
             set_target_properties(GTest::gtest PROPERTIES
                 INTERFACE_LINK_LIBRARIES Threads::Threads)
         endif()
+        set_target_properties(GTest::GTest PROPERTIES INTERFACE_LINK_LIBRARIES regex)
         if(GTEST_LIBRARY_TYPE STREQUAL "SHARED")
             set_target_properties(GTest::gtest PROPERTIES
                 INTERFACE_COMPILE_DEFINITIONS "GTEST_LINKED_AS_SHARED_LIBRARY=1")
