# x86_64 Ubuntu 22.04 based toolchain
ARG DOCKER_REGISTRY
FROM ${DOCKER_REGISTRY}ubuntu:22.04
ARG DEBIAN_FRONTEND=noninteractive

# Add helper scripts
COPY scripts/fetch-and-untar /bin
COPY scripts/fetch-and-unzip /bin
COPY scripts/untar           /bin
COPY scripts/image-cleanup   /bin
COPY scripts/bundle-runtime  /bin
COPY scripts/sources_aliyun.list /etc/apt
RUN cat /etc/apt/sources_aliyun.list >> /etc/apt/sources.list

# Force bash for everything
RUN ln -fs /bin/bash /bin/sh
ENV SHELL=/bin/bash
ENV TOOLCHAIN_ROOT=/

# Install basic tools
RUN apt-get update && apt-get install -y -q --no-install-recommends \
    rsync wget curl vim cpio unzip file libxmlsec1-openssl libsocket++1 zip lsof bc pixz pigz tree gawk  \
    tzdata locales chrpath texinfo binutils \
    apt-transport-https ca-certificates gnupg software-properties-common \
    protobuf-compiler libtinfo5 net-tools \
    openssh-client git git-lfs diffstat fonts-freefont-ttf shellcheck dotnet-sdk-8.0 \
    aspnetcore-runtime-8.0 libperl4-corelibs-perl pv zlib1g dotnet-runtime-8.0 \
    liblocal-lib-perl libxml-simple-perl python && \
    apt-get autoremove -y && apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Install basic python3 packages
RUN apt-get update && apt-get install -y -q --no-install-recommends \
    python3-pip python3-requests python3-yaml python3-jinja2 python3-xmltodict

# creating symlink to python
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3 1 && \
    update-alternatives --install /usr/bin/pip pip /usr/bin/pip3 1

# Install dev tools
RUN apt-get update && apt-get install -y -q --no-install-recommends \
    build-essential pkg-config libtool autoconf automake debhelper bison flex \
    gcc-8 g++-8 \
    libxi6 libxtst6 clang-tidy \
    libcurl4-gnutls-dev \
    && update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-8 8 \
    && update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-8 8

# Install CMake
RUN wget -O - https://apt.kitware.com/keys/kitware-archive-latest.asc 2>/dev/null | gpg --dearmor - | tee /etc/apt/trusted.gpg.d/kitware.gpg >/dev/null \
    && apt-add-repository 'deb https://apt.kitware.com/ubuntu/ focal main' \
    && apt-get update \
    && apt-get install -y cmake-data=3.23\* \
    && apt-get install -y cmake=3.23\*

# Install libfdt, libyaml, dtc as per QNX tool requirements
RUN wget https://launchpadlibrarian.net/578783489/libfdt1_1.6.1-1_amd64.deb && \
    wget https://launchpadlibrarian.net/592806200/libyaml-0-2_0.2.2-1build2_amd64.deb && \
    wget https://launchpadlibrarian.net/578783488/device-tree-compiler_1.6.1-1_amd64.deb && \
    wget http://ftp.us.debian.org/debian/pool/main/f/filepp/filepp_1.8.0-5_all.deb && \
    wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1-1ubuntu2.1~18.04.23_amd64.deb && \
    dpkg -i libfdt1_1.6.1-1_amd64.deb libyaml-0-2_0.2.2-1build2_amd64.deb \
        device-tree-compiler_1.6.1-1_amd64.deb filepp_1.8.0-5_all.deb \
        libssl1.1_1.1.1-1ubuntu2.1~18.04.23_amd64.deb && \
    rm libfdt1_1.6.1-1_amd64.deb libyaml-0-2_0.2.2-1build2_amd64.deb \
        device-tree-compiler_1.6.1-1_amd64.deb filepp_1.8.0-5_all.deb \
        libssl1.1_1.1.1-1ubuntu2.1~18.04.23_amd64.deb

# Install Clang Format
RUN wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key | apt-key add - \
    && apt-add-repository 'deb http://apt.llvm.org/focal/ llvm-toolchain-focal-12 main' \
    && apt-get update && apt-get install -y -q --no-install-recommends clang-format-12

# Final config
RUN dpkg-reconfigure locales && \
    locale-gen en_US.UTF-8 && \
    update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

ENV LC_ALL=en_US.UTF-8
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US.UTF-8
ENV TZ=America/Los_Angeles

# Setup entry point
COPY scripts/toolchain-entry /bin
ENTRYPOINT [ "/bin/toolchain-entry" ]
CMD [ "/bin/bash" ]


## QRTEK BEGIN ##

RUN apt-get update && apt-get install -y -q --no-install-recommends \
    nfs-kernel-server fastboot aptitude \
    libssl-dev \
    csh sudo xterm tcsh gtk-sharp2 lrzsz
RUN apt-get install -y lftp vainfo libexpat1-dev

# (IVI) Install basic tools
RUN apt-get update && apt-get install -y -q --no-install-recommends \
    rsync wget curl vim cpio unzip zip lsof bc pixz pigz tree gawk  \
    tzdata locales chrpath texinfo binutils \
    apt-transport-https ca-certificates gnupg software-properties-common \
    protobuf-compiler libtinfo5 net-tools \
    openssh-client git diffstat fonts-freefont-ttf git-core \
    diffstat texinfo gcc-multilib socat xz-utils debianutils \
    iputils-ping xmlstarlet clang

# (IVI) Install LA build tools
RUN apt-get update && apt-get install -y -q --no-install-recommends \
    libsdl1.2-dev xterm \
    xsltproc docbook-utils fop dblatex xmlto \
    liblz4-tool zstd


# (IVI) Additional LA Build Tools
RUN apt-get install -y -q --no-install-recommends \
    zlib1g-dev libc6-dev-i386 libncurses5 lib32ncurses5-dev \
    x11proto-core-dev libx11-dev lib32z1-dev libgl1-mesa-dev \
    libxml2-utils fontconfig

# (HGY/LV) Build Tools
RUN apt-get install -y -q --no-install-recommends \
    lz4 libselinux1-dev fakechroot qemu-user-static \
    libwayland-dev fakeroot libpam0g-dev binutils-dev \
    python3-pexpect python3-git util-linux uuid-dev \
    gettext python2 python2-dev bzip2 cgpt coreutils cvs \
    desktop-file-utils doxygen emacs execstack g++-multilib \
    genisoimage help2man htop kmod lib32stdc++6 libbz2-dev \
    libcgi-pm-perl libcurl4-openssl-dev libfdt-dev libffi7 \
    libffi-dev libgmp3-dev libmpfr-dev libncurses5-dev \
    libperl4-corelibs-perl libreadline6-dev libxml2-dev libxml2-utils \
    libxml-parser-perl libxml-simple-perl lzop m4 make mingw-w64 \
    mysqltcl ncdu ocaml ocaml-findlib ocaml-nox openjdk-8-jre:amd64 \
    openjdk-8-jdk p7zip-full pkg-config python3 python3-pip \
    python-markdown python-pycurl python-pysqlite2 python-setuptools \
    python-yaml python-git-doc quilt ruby sed sharutils subversion \
    texi2html tofrodos u-boot-tools unixodbc unixodbc-dev unzip \
    vim-common wget xaw3dg xterm zip zlib1g-dev bc \
    rsyslog xsltproc fop dblatex xmlto node-fs.realpath libacl1 \
    docbook2x libarchive-dev ssh libiberty-dev libegl1-mesa \
    libwayland-dev binutils-dev file openjdk-8-jdk-headless \
    python3-subunit mesa-common-dev python-is-python3 jq

RUN apt-get install -y -q gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# (Non-hlos) Build Tools
# Install dependencies
RUN apt-get update && apt-get install -y wget libncursesw5-dev libreadline-dev libsqlite3-dev libgdbm-dev libdb5.3-dev liblzma-dev tk-dev libxmlsec1-dev git swig python2-dev python3-setuptools libyaml-dev && rm -rf /var/lib/apt/lists/*

# Install Python 3.11.2
RUN wget https://www.python.org/ftp/python/3.11.2/Python-3.11.2.tgz && tar -xzf Python-3.11.2.tgz && cd Python-3.11.2 && rm -rf Lib/test Lib/*/test Lib/*/tests && ./configure --enable-optimizations && make -j$(nproc) && make altinstall && cd .. && rm -rf Python-3.11.2 Python-3.11.2.tgz

#Install Python 3.6.9
RUN wget https://www.python.org/ftp/python/3.6.9/Python-3.6.9.tgz && tar -xzf Python-3.6.9.tgz && cd Python-3.6.9 && rm -rf Lib/test Lib/*/test Lib/*/tests && ./configure --enable-optimizations && make -j$(nproc) && make altinstall && cd .. && rm -rf Python-3.6.9 Python-3.6.9.tgz

# Install Python 3.8.2
RUN wget https://www.python.org/ftp/python/3.8.2/Python-3.8.2.tgz && tar -xzf Python-3.8.2.tgz && cd Python-3.8.2 && rm -rf Lib/test Lib/*/test Lib/*/tests && ./configure --enable-optimizations && make -j$(nproc) && make altinstall && cd .. && rm -rf Python-3.8.2 Python-3.8.2.tgz

# Install Python 2.7.18
RUN wget https://www.python.org/ftp/python/2.7.18/Python-2.7.18.tgz && tar -xzf Python-2.7.18.tgz && cd Python-2.7.18 && rm -rf Lib/test Lib/*/test Lib/*/tests && ./configure --enable-optimizations && make -j$(nproc) && make altinstall && cd .. && rm -rf Python-2.7.18 Python-2.7.18.tgz

# Install DTC
RUN cd ~/ && \
    git clone --branch v1.6.0 https://git.kernel.org/pub/scm/utils/dtc/dtc.git && \
    cd dtc && make install && \
    cp ~/dtc/dtc /bin && rm -rf ~/dtc

# Install pip for Python 2 and virtualenv
RUN curl https://bootstrap.pypa.io/pip/2.7/get-pip.py -o get-pip.py && \
    python2 get-pip.py && \
    pip2 install virtualenv

# Qualcomm Profiler
#RUN apt update
#RUN apt install -y ftp libfuse2 libatk-bridge2.0-0 libgtk-3-dev
RUN mkdir -p /opt/qcom/qrtek && \
    mkdir -p /var/lib/qcom/data/ && \
    chmod -R 666 /var/lib/qcom/data/

WORKDIR /opt/qcom/qrtek
ENV LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu

## QRTEK END ##

# Clean up
RUN /bin/image-cleanup

# Save THIS image info
ENV TOOLCHAIN_TARGET=@TOOLCHAIN_TARGET@
ENV TOOLCHAIN_VERSION=@TOOLCHAIN_VERSION@

