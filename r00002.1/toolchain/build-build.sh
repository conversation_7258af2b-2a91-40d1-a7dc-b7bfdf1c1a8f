#!/bin/bash -e
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ./config

build_opts="--verbose"
START_TIME=`date`

# Mimick build.sh parameters for pass thru
# Main one of interest is probably --nocache
# Parse command line args
while true; do
        case "$1" in
                --*)
                        build_opts="$build_opts $1"
                        shift
                        ;;
                *)
                        break
                        ;;
        esac
done

# check if not set in outer env
if [ -z ${DOCKER_REGISTRY+x} ]
then
    # option to set if cannot directly access docker hub
    export DOCKER_REGISTRY=
fi

# Have seen cases were it rebuilds from scratch for no obvious reason, unclear why cache bypased.
# So check if image version exists before trying to build.
if [[ "$(docker images -q ${QRIDE_TOOLCHAIN} 2> /dev/null)" == "" ]]; then
    ./build.sh $build_opts ${ARCH} ${VERSION}
fi

#docker tag ${QRIDE_TOOLCHAIN} ${REGISTRY}${ARCH}:latest
END_TIME=`date`
printf "Started:  $START_TIME\n"
printf "Finished: $END_TIME\n"
