// The following section was autogenerated by nodesize_dbg
//=== BEGIN AUTOGENERATED SECTION ===//

namespace detail
{
    template <std::size_t Alignment>
    struct forward_list_node_size;

    template <>
    struct forward_list_node_size<1>
    : std::integral_constant<std::size_t, 15>
    {};

    template <>
    struct forward_list_node_size<2>
    : std::integral_constant<std::size_t, 14>
    {};

    template <>
    struct forward_list_node_size<4>
    : std::integral_constant<std::size_t, 12>
    {};

    template <>
    struct forward_list_node_size<8>
    : std::integral_constant<std::size_t, 8>
    {};

    template <>
    struct forward_list_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct forward_list_node_size
: std::integral_constant<std::size_t,
       detail::forward_list_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct list_node_size;

    template <>
    struct list_node_size<1>
    : std::integral_constant<std::size_t, 23>
    {};

    template <>
    struct list_node_size<2>
    : std::integral_constant<std::size_t, 22>
    {};

    template <>
    struct list_node_size<4>
    : std::integral_constant<std::size_t, 20>
    {};

    template <>
    struct list_node_size<8>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct list_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct list_node_size
: std::integral_constant<std::size_t,
       detail::list_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct set_node_size;

    template <>
    struct set_node_size<1>
    : std::integral_constant<std::size_t, 31>
    {};

    template <>
    struct set_node_size<2>
    : std::integral_constant<std::size_t, 30>
    {};

    template <>
    struct set_node_size<4>
    : std::integral_constant<std::size_t, 28>
    {};

    template <>
    struct set_node_size<8>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct set_node_size<16>
    : std::integral_constant<std::size_t, 32>
    {};
} // namespace detail

template <typename T>
struct set_node_size
: std::integral_constant<std::size_t,
       detail::set_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct multiset_node_size;

    template <>
    struct multiset_node_size<1>
    : std::integral_constant<std::size_t, 31>
    {};

    template <>
    struct multiset_node_size<2>
    : std::integral_constant<std::size_t, 30>
    {};

    template <>
    struct multiset_node_size<4>
    : std::integral_constant<std::size_t, 28>
    {};

    template <>
    struct multiset_node_size<8>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct multiset_node_size<16>
    : std::integral_constant<std::size_t, 32>
    {};
} // namespace detail

template <typename T>
struct multiset_node_size
: std::integral_constant<std::size_t,
       detail::multiset_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct unordered_set_node_size;

    template <>
    struct unordered_set_node_size<1>
    : std::integral_constant<std::size_t, 23>
    {};

    template <>
    struct unordered_set_node_size<2>
    : std::integral_constant<std::size_t, 22>
    {};

    template <>
    struct unordered_set_node_size<4>
    : std::integral_constant<std::size_t, 20>
    {};

    template <>
    struct unordered_set_node_size<8>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_set_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct unordered_set_node_size
: std::integral_constant<std::size_t,
       detail::unordered_set_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct unordered_multiset_node_size;

    template <>
    struct unordered_multiset_node_size<1>
    : std::integral_constant<std::size_t, 23>
    {};

    template <>
    struct unordered_multiset_node_size<2>
    : std::integral_constant<std::size_t, 22>
    {};

    template <>
    struct unordered_multiset_node_size<4>
    : std::integral_constant<std::size_t, 20>
    {};

    template <>
    struct unordered_multiset_node_size<8>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_multiset_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct unordered_multiset_node_size
: std::integral_constant<std::size_t,
       detail::unordered_multiset_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct map_node_size;

    template <>
    struct map_node_size<1>
    : std::integral_constant<std::size_t, 30>
    {};

    template <>
    struct map_node_size<2>
    : std::integral_constant<std::size_t, 28>
    {};

    template <>
    struct map_node_size<4>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct map_node_size<8>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct map_node_size<16>
    : std::integral_constant<std::size_t, 32>
    {};
} // namespace detail

template <typename T>
struct map_node_size
: std::integral_constant<std::size_t,
       detail::map_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct multimap_node_size;

    template <>
    struct multimap_node_size<1>
    : std::integral_constant<std::size_t, 30>
    {};

    template <>
    struct multimap_node_size<2>
    : std::integral_constant<std::size_t, 28>
    {};

    template <>
    struct multimap_node_size<4>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct multimap_node_size<8>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct multimap_node_size<16>
    : std::integral_constant<std::size_t, 32>
    {};
} // namespace detail

template <typename T>
struct multimap_node_size
: std::integral_constant<std::size_t,
       detail::multimap_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct unordered_map_node_size;

    template <>
    struct unordered_map_node_size<1>
    : std::integral_constant<std::size_t, 22>
    {};

    template <>
    struct unordered_map_node_size<2>
    : std::integral_constant<std::size_t, 20>
    {};

    template <>
    struct unordered_map_node_size<4>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_map_node_size<8>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_map_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct unordered_map_node_size
: std::integral_constant<std::size_t,
       detail::unordered_map_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct unordered_multimap_node_size;

    template <>
    struct unordered_multimap_node_size<1>
    : std::integral_constant<std::size_t, 22>
    {};

    template <>
    struct unordered_multimap_node_size<2>
    : std::integral_constant<std::size_t, 20>
    {};

    template <>
    struct unordered_multimap_node_size<4>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_multimap_node_size<8>
    : std::integral_constant<std::size_t, 16>
    {};

    template <>
    struct unordered_multimap_node_size<16>
    : std::integral_constant<std::size_t, 16>
    {};
} // namespace detail

template <typename T>
struct unordered_multimap_node_size
: std::integral_constant<std::size_t,
       detail::unordered_multimap_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct shared_ptr_stateless_node_size;

    template <>
    struct shared_ptr_stateless_node_size<1>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct shared_ptr_stateless_node_size<2>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct shared_ptr_stateless_node_size<4>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct shared_ptr_stateless_node_size<8>
    : std::integral_constant<std::size_t, 32>
    {};

    template <>
    struct shared_ptr_stateless_node_size<16>
    : std::integral_constant<std::size_t, 48>
    {};
} // namespace detail

template <typename T>
struct shared_ptr_stateless_node_size
: std::integral_constant<std::size_t,
       detail::shared_ptr_stateless_node_size<alignof(T)>::value + sizeof(T)>
{};

namespace detail
{
    template <std::size_t Alignment>
    struct shared_ptr_stateful_node_size;

    template <>
    struct shared_ptr_stateful_node_size<1>
    : std::integral_constant<std::size_t, 40>
    {};

    template <>
    struct shared_ptr_stateful_node_size<2>
    : std::integral_constant<std::size_t, 40>
    {};

    template <>
    struct shared_ptr_stateful_node_size<4>
    : std::integral_constant<std::size_t, 40>
    {};

    template <>
    struct shared_ptr_stateful_node_size<8>
    : std::integral_constant<std::size_t, 40>
    {};

    template <>
    struct shared_ptr_stateful_node_size<16>
    : std::integral_constant<std::size_t, 64>
    {};
} // namespace detail

template <typename T>
struct shared_ptr_stateful_node_size
: std::integral_constant<std::size_t,
       detail::shared_ptr_stateful_node_size<alignof(T)>::value + sizeof(T)>
{};

//=== END AUTOGENERATED SECTION ===//
