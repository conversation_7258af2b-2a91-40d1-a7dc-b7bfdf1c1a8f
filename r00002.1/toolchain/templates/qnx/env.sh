#/bin/bash
#

QNX_HOME=/opt/${ROOT_DIR}

export QNX_CONFIGURATION_EXCLUSIVE=$$QNX_HOME
export QNX_HOST=$$QNX_HOME/host/linux/x86_64
export QNX_TARGET=$$QNX_HOME/target/qnx7

export PATH=$$QNX_HOST/usr/bin:$$PATH

export CC=aarch64-unknown-nto-qnx${SDP_VERSION}.0-gcc
export CXX=aarch64-unknown-nto-qnx${SDP_VERSION}.0-g++
export LD=aarch64-unknown-nto-qnx${SDP_VERSION}.0-ld
export AR=aarch64-unknown-nto-qnx${SDP_VERSION}.0-ar
export AS=aarch64-unknown-nto-qnx${SDP_VERSION}.0-as
export NM=aarch64-unknown-nto-qnx${SDP_VERSION}.0-nm
export RANLIB=aarch64-unknown-nto-qnx${SDP_VERSION}.0-ranlib
export STRIP=aarch64-unknown-nto-qnx${SDP_VERSION}.0-strip

SYSROOT=$$QNX_TARGET
export PKG_CONFIG_SYSROOT_DIR=$$SYSROOT
export PKG_CONFIG_LIBDIR=$$SYSROOT/aarch64le/usr/lib/pkgconfig:$$SYSROOT/usr/lib/pkgconfig:$$SYSROOT/usr/share/pkgconfig
export CMAKE_TOOLCHAIN_FILE=$$QNX_HOME/toolchain.cmake
export TOOLCHAIN_SYSROOT=$$QNX_TARGET/aarch64le
