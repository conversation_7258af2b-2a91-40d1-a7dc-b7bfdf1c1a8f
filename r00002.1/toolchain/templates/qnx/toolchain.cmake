#  Copyright 2020 Qualcomm Technologies, Inc. All rights reserved.
#  Confidential & Proprietary - Qualcomm Technologies, Inc. ("QTI")

set( CMAKE_SYSTEM_NAME QNX )
set( CMAKE_SYSTEM_PROCESSOR aarch64 )

set( arch gcc_ntoaarch64le )

set( CMAKE_C_COMPILER aarch64-unknown-nto-qnx${SDP_VERSION}.0-gcc )
set( CMAKE_C_COMPILER_TARGET $${arch} )
set( CMAKE_CXX_COMPILER aarch64-unknown-nto-qnx${SDP_VERSION}.0-g++ )
set( CMAKE_CXX_COMPILER_TARGET $${arch} )

set( CMAKE_SYSROOT $$ENV{QNX_TARGET}/aarch64le/ )
