if(TARGET ${lib})
  return()
endif()

string(REPLACE "/aarch64le/usr/lib/cmake/${lib}" "" _${lib}_prefix $${CMAKE_CURRENT_LIST_DIR})
set(_${lib}_include_dir $${_${lib}_prefix}/usr/include)
set(_${lib}_library_dir $${_${lib}_prefix}/aarch64le/usr/lib)

add_library(${lib} INTERFACE IMPORTED)
target_include_directories(${lib} INTERFACE $${_${lib}_include_dir})
${libLines}
set(${capsLib}_VERSION "${version}")
set(${capsLib}_LIBRARIES ${lib})

message(STATUS "${lib} version: $${${capsLib}_VERSION}   : $${_${lib}_prefix}")
