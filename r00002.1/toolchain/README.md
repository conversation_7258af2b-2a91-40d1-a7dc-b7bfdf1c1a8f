## Qualcomm Snapdragon Ride Toolchain tools

### Prerequisites

- The following packages must be installed on the host machine:
    
    - docker.io

- By default docker install images in /var/lib/docker  This needs to be large enough otherwise configure docker to use a different partition.  This image is about 23GB.
 
- `/target/check-linux-dependency.sh` and `/target/check-python-dependency.sh` come from QNN SDK 1.14.0

### Build

The following script is used for building the Ubuntu toolchain docker image

    $ ./build-x86_64-ubuntu2004.sh
    
The resulting docker image can be used to
- build Controller
- run Controller
- build x86_64 SDK

Ride SDK and Ride Vision SDK currently share the same toolchain.
Ride Vision SDK needs a few extra packages and does not need about 10GB of packages.
However not yet a significant gain or difference to merrit maintaining separate versions of x86_64-ubuntu2004

The aarch64-qos222 toolchain is currently used to build the platform side of Stack SDK.
The following script is used for building

    $ ./build-aarch64-qos.sh
