#!/bin/bash
#

[ -r $TOOLCHAIN_ROOT/env.sh ] && source $TOOLCHAIN_ROOT/env.sh

# Expand vars in the arguments after loading the env
eval name=$1
eval url=$2
eval dst=$3

echo "Fetching $name from $url into $dst ..."

tmp_dir=/tmp/$name-fetch

mkdir -p $tmp_dir/out

# Fetch and untar into the 'out' directory
wget --directory-prefix=$tmp_dir $url
tar -xavf $tmp_dir/*.* -C $tmp_dir/out

# Sanitize untared dir
chown -R root.root $tmp_dir/out
chmod -R a=rX $tmp_dir/out

# Move contents into destination
mkdir -p $dst && cp -rf $tmp_dir/out/* $dst

rm -rf $tmp_dir
