#!/usr/bin/env python3

import sys
import os
import argparse
import traceback
import tarfile, tempfile
import distutils.dir_util
from shutil import copytree, rmtree, move, copy2
from pathlib import Path
from string import Template

def print_traceback_and_exit():
    print( '-'*45 )
    print( "Encountered an unexpected error: " )
    traceback.print_exc(file=sys.stdout)
    print( '-'*45 )
    sys.exit( "Exiting!" )

def printHeader( message ):
    print( '-'*65 )
    print( message )
    print( '-'*65 )

def get_ais_version( dir ):
    versionFound = False
    try:
        for currDir, dirs, files in os.walk( dir ):
            for currFile in files:
                if( currFile == "qcarcam.h" ):
                    with open( os.path.join( currDir, currFile ) ) as inputFile:
                        fileContent = inputFile.read()
                        versionMajor = fileContent.split( "#define QCARCAM_VERSION_MAJOR" )[1].split("\n")[0].strip()
                        versionMinor = fileContent.split( "#define QCARCAM_VERSION_MINOR" )[1].split("\n")[0].strip()
                        version = versionMajor + "." + versionMinor
                        return version
    except:
        print_traceback_and_exit()
    sys.exit( "Could not parse ais version!" )

def get_omx_version( dir ):
    try:
        for currDir, dirs, files in os.walk( dir ):
            for currFile in files:
                if( currFile == "OMX_Types.h" ):
                    with open( os.path.join( currDir, currFile ) ) as inputFile:
                        fileContent = inputFile.read()
                        versionMajor = fileContent.split( "#define OMX_VERSION_MAJOR" )[1].split("\n")[0].strip()
                        versionMinor = fileContent.split( "#define OMX_VERSION_MINOR" )[1].split("\n")[0].strip()
                        versionRevision = fileContent.split( "#define OMX_VERSION_REVISION" )[1].split("\n")[0].strip()
                        version = versionMajor + "." + versionMinor + "." + versionRevision
                        return version
    except:
        print_traceback_and_exit()
    sys.exit( "Could not parse omx version!" )

def get_fastcv_version( dir ):
    try:
        for currDir, dirs, files in os.walk( dir ):
            for currFile in files:
                if( currFile == "fastcv.h" ):
                    with open( os.path.join( currDir, currFile ), "rb" ) as inputFile:
                        fileContent = inputFile.read().decode(errors='replace')
                        version = fileContent.split( "@version " )[1].split("\n")[0].strip()
                        return version
    except:
        print_traceback_and_exit()
    sys.exit( "Could not parse fastcv version!" )

def get_fadas_version():
    # can't seem to find anything in the headers
    return "1.0"

def get_vidc_version():
    # unfortunately there's no known way to determine the vidc version
    return "1.0"

def get_c2d_version():
    # unfortunately there's no known way to determine the c2d version
    return "1.0"

def get_pmem_version():
    # unfortunately there's no known way to determine the pmem version
    return "1.0"

def get_qgptp_version():
    # unfortunately there's no known way to determine the qGPTP version
    return "1.0"


def make_tarfile( outFile, srcDir ):
    outFile = outFile + ".tar"
    with tarfile.open( outFile, "w" ) as tar:
        tar.add( srcDir, arcname = os.path.basename(srcDir) )
    os.system('xz -f -T0 ' + outFile)

def make_symlink( src, dst):
    try:
        os.symlink( src, dst )
    except:
        print_traceback_and_exit()

def make_directory( dir ):
    try:
        os.makedirs( dir )
    except:
        print_traceback_and_exit()

def remove_tree( dir ):
    try:
        rmtree( dir )
    except:
        print_traceback_and_exit()

def remove_file( filePath ):
    try:
        os.remove( filePath )
    except:
        print_traceback_and_exit()

def remove_file_type( dir, ext ):
    try:
        for currDir, dirs, files in os.walk( dir ):
            for currFile in files:
                if( currFile.endswith( ext ) ):
                    os.remove( os.path.join( currDir, currFile ) )
    except:
        print_traceback_and_exit()

def copy_file( srcFile, dstFile ):
    try:
        print( "Copying " + srcFile + " to " + dstFile )
        copy2( srcFile, dstFile, follow_symlinks=False)
    except FileExistsError:
        # Same library can be listed multiple times
        return
    except:
        print_traceback_and_exit()

def copy_directory( srcDir, dstDir ):
    try:
        copytree( srcDir, dstDir, symlinks=True, ignore=None, copy_function=copy2, ignore_dangling_symlinks=True )
    except:
        print_traceback_and_exit()

# with higher python version we could use copytree( ..., dirs_exist_ok=True)
def merge_directories( srcDir, dstDir ):
    try:
        for currSrcDir, dirs, files in os.walk( srcDir ):
            currDstDir = currSrcDir.replace( srcDir, dstDir, 1 )
            if not os.path.exists( currDstDir ):
                os.makedirs( currDstDir )
            for currFile in files:
                srcFile = os.path.join( currSrcDir, currFile )
                dstFile = os.path.join( currDstDir, currFile )
                if os.path.exists( dstFile ):
                    os.remove( dstFile )
                copy2( srcFile, dstFile )
    except:
        print_traceback_and_exit()

def generate_env_file( tmpl, envFilePath, rootDir, sdpVersion ):
    try:
        with open( tmpl ) as inputFile:
            configTemplate = Template( inputFile.read() )

        result = configTemplate.substitute( {"ROOT_DIR":rootDir, "SDP_VERSION":sdpVersion} )

        with open( envFilePath, "wt") as textFile:
            textFile.write( result )
    except:
        print_traceback_and_exit()

def generate_toolchain_file( tmpl, toolchainFilePath, sdpVersion ):
    try:
        with open( tmpl ) as inputFile:
            configTemplate = Template( inputFile.read() )

        result = configTemplate.substitute( {"SDP_VERSION":sdpVersion} )

        with open( toolchainFilePath, "wt") as textFile:
            textFile.write( result )
    except:
        print_traceback_and_exit()

def create_cmake_config( libName, libVersion, libList, outputDir ):
    try:
        fileSuffix = "config.cmake"
        with open( "templates/qnx/" + fileSuffix ) as inputFile:
            configTemplate = Template( inputFile.read() )

        libLineTemplate = Template( "target_link_libraries(${lib} INTERFACE $${_${lib}_library_dir}/${libFile})" )
        libLines = ""
        for libFile in libList:
            libLines = libLines + libLineTemplate.substitute( {"lib":libName, "libFile": libFile } ) + "\n"
        result = configTemplate.substitute( {"lib":libName, "capsLib":libName.upper(), "version":libVersion, "libLines":libLines} )
        outputFile = outputDir + '/' + libName + '-' + fileSuffix

        with open( outputFile, "wt") as textFile:
            textFile.write( result )
    except:
        print_traceback_and_exit()

def create_cmake_config_version( libName, libVersion, outputDir ):
    try:
        fileSuffix = "config-version.cmake"
        with open( "templates/qnx/" + fileSuffix ) as inputFile:
            versionTemplate = Template( inputFile.read() )

        result = versionTemplate.substitute( version=libVersion )
        outputFile = outputDir + '/' + libName + '-' + fileSuffix
        with open( outputFile, "wt") as textFile:
            textFile.write( result )
    except:
        print_traceback_and_exit()

def create_cmake( libName, libVersion, libList, outputDir ):
    cmakeLibDir = outputDir + '/' + libName
    make_directory( cmakeLibDir )
    create_cmake_config( libName, libVersion, libList, cmakeLibDir )
    create_cmake_config_version( libName, libVersion, cmakeLibDir )

##################################
# Main

if sys.version_info[0] < 3:
    sys.exit( "This script requires Python 3!" )

parser = argparse.ArgumentParser( description="Creates QNX toolchain for docker image from locally built QNX perforce tree." )
parser.add_argument( "-i", "--input",   help="directory that contains the locally built QNX perforce tree (parent dir of qnx_ap)" )
parser.add_argument( "-o", "--output",  default="prebuilt/qos222", help="basename of output file" )
args = parser.parse_args()

if not args.input:
    parser.print_help(sys.stdout)
    sys.exit( "\nNo input directory provided!" )

if not os.path.exists( args.input ) or not os.path.isdir( args.input ):
    sys.exit( "The provided input directory: " + args.input + " does not exist!" )

inputDir = args.input

tcRootDirName = "qos222"
sdpString     = "QOS222"
sdpVersion    = "7.1"

outputDir = tempfile.mkdtemp()

print( "Using input directory: "  + inputDir )
print( "Using output directory: " + outputDir )
print( "Version: {} {}".format(sdpString, sdpVersion) )

tcRootPath = outputDir  + "/" + tcRootDirName
tcLibDir   = tcRootPath + "/target/qnx7/aarch64le/usr/lib"
tcIncDir   = tcRootPath + "/target/qnx7/aarch64le/usr/include"
srcLibDir  = inputDir   + "/qnx_ap/install/aarch64le/lib"
srcIncDir  = inputDir   + "/qnx_ap/install/usr/include"

print( "Creating toolchain root directory: " + tcRootDirName + "\n" )
make_directory( tcRootPath )

print( "Updating environment file\n")
generate_env_file( "templates/qnx/env.sh", tcRootPath + "/env.sh", tcRootDirName, sdpVersion )

print( "Updating toolchain file\n")
generate_toolchain_file( "templates/qnx/toolchain.cmake", tcRootPath + "/toolchain.cmake", sdpVersion )

printHeader( "Copying essential toolchain files..." )
make_directory( tcRootPath + "/license" ) # workaround to prevent having to chmod (which needs sudo)
copyList = [ { "desc":"host files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/host", "dst":"host", "op":"copy" },
             { "desc":"target (aarch64le) files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/target/qnx7/aarch64le", "dst":"target/qnx7/aarch64le", "op":"copy" },
             { "desc":"target (etc) files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/target/qnx7/etc", "dst":"target/qnx7/etc", "op":"copy" },
             { "desc":"target (sbin) files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/target/qnx7/sbin", "dst":"target/qnx7/sbin", "op":"copy" },
             { "desc":"target (usr) files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/target/qnx7/usr", "dst":"target/qnx7/usr", "op":"copy" },
             { "desc":"license files", "src":"qnx_ap/qnx_bins/prebuilt_" + sdpString + "/target/qnx7/license", "dst":"license", "op":"merge" } ]

for item in copyList:
    srcDir = inputDir + '/' + item["src"]
    dstDir = tcRootPath + '/' + item["dst"]
    print( "Copying " + item["desc"] + "\n  source: " + srcDir + "\n  destination: " + dstDir + "\n" )
    if item["op"] == "copy":
        copy_directory( srcDir, dstDir )
    elif item["op"] == "merge":
        merge_directories( srcDir, dstDir )
    else:
        sys.exit( "Unsupported operation" )

remove_file_type( tcRootPath + "/host/linux/x86_64/usr/lib/python2.7", ".pyc" )

printHeader( "Creating symlinks and reorganizing structure..." )

make_directory( tcRootPath + "/target/qnx7/aarch64le/etc" )

moveList = [ { "desc":"etc files", "src":"target/qnx7/etc", "dst":"target/qnx7/aarch64le/etc", "symlink":"aarch64le/etc" },
             { "desc":"sbin files", "src":"target/qnx7/sbin", "dst":"target/qnx7/aarch64le/sbin", "symlink":"aarch64le/sbin" },
             { "desc":"usr files", "src":"target/qnx7/usr", "dst":"target/qnx7/aarch64le/usr", "symlink":"aarch64le/usr" } ]

for item in moveList:
    srcDir = tcRootPath + '/' + item["src"]
    dstDir = tcRootPath + '/' + item["dst"]
    print( "Moving " + item["desc"] + " from : " + srcDir + " to: " + dstDir )
    merge_directories( srcDir, dstDir )
    remove_tree( srcDir )
    print( "Creating symlink from: " + srcDir + " to: " + dstDir )
    make_symlink( item["symlink"], srcDir )

printHeader( "Removing existing prebuilt gtest..." )
gtestLibList = [ "libgtest.a", "libgtestS.a", "libgtest.so", "libgtest.so.1.7" ]
gtestIncDir = tcIncDir + "/gtest"
remove_tree( gtestIncDir )
for gtestLib in gtestLibList:
    remove_file( tcLibDir + '/' + gtestLib )

printHeader( "Copying additional libraries and headers..." )

make_directory( tcIncDir + "/amss" )
copy_file( srcIncDir + "/amss/pmem_id.h", tcIncDir + "/amss" )

make_directory( tcIncDir + "/fastcv" )
merge_directories( srcIncDir + "/amss/multimedia/fastcv", tcIncDir + "/fastcv" )
merge_directories( srcIncDir + "/amss/multimedia/openmax", tcIncDir )

print( "Creating FastCV -> fastcv symlink" )
make_symlink( "fastcv", tcIncDir + "/FastCV" )

merge_directories( srcIncDir + "/amss/multimedia/fadas", tcIncDir )

print( "Creating rt symlink (hack)\n" )
make_symlink( "libshutdown.a",  tcRootPath + "/target/qnx7/aarch64le/lib/librt.a" )

camArch = os.environ.get('CAM_ARCH')
if camArch == "QCD" :
    incList = [ "protected/pmem.h", "camera_safe/qcd/src/API/inc/qcarcam.h", "camera_safe/qcd/src/API/inc/qcarcam_types.h" ]
    print("creating QCD Docker")
elif  camArch == "AIS" :
    incList = [ "protected/pmem.h", "camera_adas/ais/API/inc/qcarcam.h", "camera_adas/ais/API/inc/qcarcam_types.h" ]
    print("creating AIS docker")
elif  camArch == "QCX" :
    incList = [ "protected/pmem.h", "qcamera/camera_qcx/cdk_qcx/api/qcarcam/qcarcam.h", "qcamera/camera_qcx/cdk_qcx/api/qcarcam/qcarcam_types.h","qcamera/camera_qcx/cdk_qcx/api/qcarcam/qcarcam_diag_types.h" ]
    print("creating QCX docker")

for inc in incList:
    print( "Copying header file: " + inc + " to: " + tcIncDir )
    copied = False
    for srcFile in Path( inputDir ).rglob( inc ):
        copy_file( str( srcFile ), tcIncDir)
        copied = True
    if not copied:
        sys.exit("Failed to copy: " + inc )


# copy specific headers (search in tree might find wrong version if multiple files are present)
incList = [ srcIncDir + "/amss/core/comdef.h",
            srcIncDir + "/amss/core/com_dtypes.h",
            srcIncDir + "/amss/core/alltypes.h",
            srcIncDir + "/amss/core/Base.h",
            srcIncDir + "/amss/core/ProcessorBind.h",
            srcIncDir + "/amss/core/qnx_err.h",
            srcIncDir + "/amss/core/logger_utils.h",
            srcIncDir + "/amss/core/logger_codes.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/inc/ioctlClient.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/video/source/common/drivers/inc/vidc_types.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/video/source/common/drivers/codec/vidc/inc/vidc_api.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/graphics/include/private/C2D/c2d2.h",
            inputDir  + "/qnx_ap/AMSS/inc/qgptp_helper.hpp",
            inputDir  + "/qnx_ap/AMSS/inc/AEEstd.h",
            inputDir  + "/qnx_ap/AMSS/inc/AEEVaList.h",
            inputDir  + "/qnx_ap/AMSS/inc/AEEStdDef.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMFile.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMDebugMsg.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMThread.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMSignal.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMCriticalSection.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMTime.h",
            inputDir  + "/qnx_ap/AMSS/inc/MMTimer.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_mem.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_of.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_session.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_types.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_utils.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/eva/epl/public/amss/multimedia/eva/eva_dfs.h",
            inputDir  + "/qnx_ap/AMSS/multimedia/video/source/common/drivers/inc/vidc_ioctl.h" ]

for inc in incList:
    print( "Copying header file: " + inc + " to: " + tcIncDir )
    copy_file( inc, tcIncDir)

libListAIS   = [ "libais_client.so", "libais_log.so", "liblibstd.so", "libgpio_client.so", "libOSAbstraction.so", "libmmap_peer.so" ]
libListQCD   = [ "libqcd_client.so", "libqcd.so", "libaosal.so", "libqcdplatform.so", "libqcdconfig.so", "libtime_sync_client.so", "libtime_sync_client.so.1", "libtime_sync_util.so", "libsm_client.so", "libnpa_client.so" ]
libListOMX   = [ "libOmxBase.so", "libOmxCore.so", "libmmiVenc.so", "libmmiVdec.so" ]
libListVidc  = [ "libioctlClient.so", "libOSAbstraction.so", "libopenwfd.so" ]
libListC2d   = [ "libc2d30.so" ]
libListPmem  = [ "libpmem_client.so", "libpmemext.so" ]
libListQgptp = [ "libqgptp.so", "libdal.so", "libdalconfig.so.1" ]
libListFastADAS = [ "libfadas.so", "libfastrpc.so", "libfastrpc_pmem.so" ]
libListFastCV = [ "libfastcvopt.so", "libOSUser.so", "libGSLUser.so", "libOpenCL.so", "libcdsprpc.so", "libfastrpc.so", "libfastrpc_pmem.so", "libsmmu_client.so" ]
libListEva = [ "libclock_client.so", "libdevioClient.so", "libevaEpl.so", "libevaPlatform.so" ]
libListQcx = [ "libqcxclient.so", "libqcxosal.so" ]

libList = libListAIS + libListQCD + libListOMX + libListVidc + libListFastCV + libListC2d + libListPmem + libListQgptp + libListFastADAS + libListEva + libListQcx + [
        "libcommonUtils.so", "libioctlClient_shim.so", "libplanedef.so", "libfdt_utils.so.1", "libcdsprpcS.a", "libcdsprpc.so",
        "libfastrpc_pmem.so.1", "libfastcvopt.so.1",
        "libsmmu_clientS.a", "libfastcvopt.a", "libfastcvoptS.a"
    ]

targetLibDirs = [
        inputDir + '/qnx_ap/install/aarch64le',
        inputDir + '/qnx_ap/AMSS/qaic/prebuilt/lib64'
    ]

for lib in libList:
    print( "Copying library/symbol file: " + lib + " to: " + tcLibDir )
    copied = False
    for libDir in targetLibDirs:
        if copied: break
        for srcFile in Path(libDir).rglob(lib):
            copy_file(str(srcFile), tcLibDir)
            copied = True
            break
    if not copied:
        sys.exit("Failed to copy: " + lib )

print( "\nFinished copying additional libraries and headers!\n" )

printHeader( "Creating cmake config files... ")
create_cmake( "fastcv", get_fastcv_version( tcIncDir ), libListFastCV, tcLibDir + "/cmake" )
create_cmake( "fadas", get_fadas_version(), libListFastADAS, tcLibDir + "/cmake" )
create_cmake( "ais", get_ais_version( tcIncDir ), libListAIS, tcLibDir + "/cmake" )
# use get_ais_version api for QCD also since the header files names are not changed
create_cmake( "qcd", get_ais_version( tcIncDir ), libListQCD, tcLibDir + "/cmake" )
create_cmake( "omx", get_omx_version( tcIncDir ), libListOMX, tcLibDir + "/cmake" )
create_cmake( "vidc", get_vidc_version(), libListVidc, tcLibDir + "/cmake" )
create_cmake( "c2d", get_c2d_version(), libListC2d, tcLibDir + "/cmake" )
create_cmake( "pmem", get_pmem_version(), libListPmem, tcLibDir + "/cmake" )
create_cmake( "qgptp", get_qgptp_version(), libListQgptp, tcLibDir + "/cmake" )

printHeader( "Creating tar.xz file... (this may take a while)")
make_tarfile( args.output, tcRootPath )

printHeader( "Cleaning up temporary files... ")
remove_tree( outputDir )

print( "Successfully created " + args.output )
