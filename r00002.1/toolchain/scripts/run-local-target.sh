#!/bin/bash
#

# This is a sample script for running locally built x86_64 targets with controller-toolchain.
# Run it from the root source directory like this:
#   ./scripts/run-local-target <target>
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

die() {
    echo $1
    exit 1
}

# Target name
target=$1

# Runtime directory
rundir=$(pwd)

[ "$target" = "" ] && die "please specify docker name to run"

[ -d $rundir ] || die "$rundir does not exist"

docker run -it -e DISPLAY=$DISPLAY \
    --volume /tmp/.X11-unix:/tmp/.X11-unix \
    --volume $HOME/.Xauthority:/root/.Xauthority \
    --volume $PWD:/opt/qcom/qrtek \
    --volume /local/mnt/rideconsole/runtime:/runtime \
    --ipc=host --net=host \
    $target
