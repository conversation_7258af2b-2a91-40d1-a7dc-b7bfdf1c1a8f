ENABLED_BUILDS="QNX LA"

# QNX Software Center toolname
QNX_LICENSE="$HOME/.qnx/license"
QNX_SWC=/qnx/qnxsoftwarecenter

# Build info
PRODUCT="snapdragon-auto-hqx-4-8-9-0"
CHIPCODE_TAG="r00002.1"
PRODUCT_FLAVOR="8255_la"
SAIL_TYPE=sail_nor_lemans
FLAVOR="$PRODUCT_FLAVOR,$SAIL_TYPE"
VARIANT="8255_LAGVM_SafeRTOS"
TARGET="8255"
PATCH_SCRIPT=""
KERNEL_PARAMETER="consolidate"
min_required_space_gb=860

QNX_TOOLCHAIN="SDP800"
# Flashing Parameters
AU_TYPE=LeMans
MEM_TYPE=ufs

# Customer Info
CUSTOMER_ID=1020

CHIPCODE="chipcode.qti.qualcomm.com"
CHIPCODE_UTILITIES="https://${CHIPCODE}/revision-history/platform/vendor/qcom-proprietary/grease/utilities.git"

CODELINARO="https://git.codelinaro.org"
CODELINARO_REPO="${CODELINARO}/clo/tools/repo.git"

QSSI_MANIFEST="clo/la/la/system/manifest"
VENDOR_MANIFEST="clo/la/la/vendor/manifest"
KERNEL_MANIFEST="clo/la/kernelplatform/manifest"

QSSI_HISTORY="N/A"
VENDOR_HISTORY="N/A"
KERNEL_HISTORY="N/A"

AU_LINUX_LA_QSSI="AU_LINUX_ANDROID_LA_AU.QSSI.*********.00.00.1037.402"
AU_LINUX_LA_VENDOR="AU_LINUX_ANDROID_LA_AU.VENDOR.*********.00.00.1157.077"
AU_LINUX_KERNEL="AU_LINUX_KERNEL.PLATFORM.*********.00.083.031"

MANIFEST_BRANCH="release"
GREASE_UTILITIES="${CUSTOMER_ID}-grease-utilities.lnx.4.0.r149-rel"

QSSI_LUNCH_CMD="qssi_au"
LUNCH_CMD="gen4_gvm"
LA_DISABLE_OUTPUT_OTA="True"
LA_ENABLE_TMP="True"

QC_USER=""
QC_PASSWORD=""
MAIL_ID=""
CODELINARO_ACCESS_TOKEN=""

# MyQNX account info
MYQNX_USERNAME=""
MYQNX_PASSWORD=""

EXTRA_PARAMETER_FILE=/tmp/.build-parameters

TOOLCHAIN_TAG='build-v3.4.1'
UTILITIES_TAG='v3.6.3-1'

