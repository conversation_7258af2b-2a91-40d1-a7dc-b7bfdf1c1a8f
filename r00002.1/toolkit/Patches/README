The 'Patches' folder is utilized in the RideSDK BSP build process that executes a "patch.sh" script after the BSP files are pulled from chipcode.  

The structure of the patch folder should be:

toolkit
└── Patches
    └── [PATCH_DIRECTORY_NAME]
        └── patch.sh
        └── [MISC FILES FOR PATCHING]

The process of the build script is:
1) Running the build script and specify a patch function:

./download-compile-build.sh --tag [CHIPCODE_TAG] --patch /patch-directory"

2) In the download-compile-build.sh script, after chipcode pull, it will enter the patch directory, run 'patch.sh' and pass the directory of the META directory as a commandline parameter.

# Patch build if needed here
if [ ! -z "$PATCH_DIR" ]; then
    chmod 755 $script_directory/Patches/$PATCH_DIR/patch.sh
    echo "PATCH directory specified, applying patch $PATCH_DIR/patch.sh"
    $script_directory/Patches/$PATCH_DIR/patch.sh $build_dir || die "Patch process failed, check the logs"
fi

------

The structure of "patch.sh" at minimum is:

#!/bin/bash
meta_dir=$(echo $1 | sed 's:/*$::')

-----

*** Example of processing a QPM service ticket:

qpm3_build_tag="Snapdragon_Auto.QX.4.4.0.r1-00011-STD.PROD-1.50845.1"
qpm3_service_ticket="r00011.1.50845.1"
new_build_tag="Snapdragon_Auto.QX.4.4.0.r1-00011-STD.PROD-1.620545.3.624346.1"

cd $meta_dir
echo "Processing $qpm3_service_ticket..."
/opt/qcom/QPM3/bin/qpm3-cli build-download -sp Snapdragon_Auto.QX.4.4.0.r1 -bb $qpm3_build_tag -dn "Snapdragon_Auto.QX.4.4.0|HLOS|DEV|QNX" --download-location /tmp --folder-type "Type2"
cd /tmp/$qpm3_service_ticket/apps
echo "Extracting $qpm3_service_ticket Apps..."
tar xzf apps.tar.gz
echo "Copying $qpm3_service_ticket contents to META build"
cd buildpath0
cp -rf qnx_ap $meta_dir/apps
rm -rf /tmp/$qpm3_service_ticket
echo "Git committing"
cd $meta_dir/apps/qnx_ap
git commit -a -m "$qpm3_service_ticket"

-----
*** Example of processing a QPM service ticket:


echo "Applying AVP5 Boole EK4 R1 Patches from ${patch_dir%/}"
git am ${patch_dir%/}/0*.patch
echo "Replacing contents.xml"
sed -i 's/Snapdragon_Auto.QX.4.4.0.r1-00011-STD.PROD-1/Snapdragon_Auto.QX.4.4.0.r1-00011-STD.PROD-1.620545.3.624346.1/g' $meta_dir/contents.xml
