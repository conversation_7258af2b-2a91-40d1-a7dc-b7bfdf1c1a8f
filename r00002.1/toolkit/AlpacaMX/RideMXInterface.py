###################################################################################################
# This scripts helps to put the board in EDL mode
#
# NOTE: This script is for RIDE MX Gen4 Platforms
#
###################################################################################################
import platform
import sys

FTDI_EDL_MGR_VER = "1.0.1.mx"


def byte_seq_string(byte_val):
    """
    Method to print the byte in hex and binary format
    """
    return 'Mask Bits: ' + str(byte_val) + " [" + hex(byte_val) + ", " + bin(byte_val) + "]"


class FTDIInterface:
    """
    Base class for FTDI Interface
    """
    def __init__(self, verbose):
        """
        Constructor for the class
        :param verbose: True if command to be run with verbose mode, False otherwise
        """
        self._verbose = verbose
        self._serial_dev_data = {}

    def write(self, serial_port, value):
        """
        Method to change EDL pins state
        :param serial_port: Serial port label, supported values are 'A', 'B', 'C' and 'D'
        :param value: value to be written
        """
        print("WARN: This change_pins should not be called")

    def read(self, serial_port):
        """
        Method to read the current pin value
        """
        print("WARN: This read_pins should not be called")
        return 0


class FTDILinux(FTDIInterface):
    """
    Class to handle the pin changes using Linux FTDI APIs
    """
    def __init__(self, verbose, ftdi_api, gpio_api, ftdi_base_url, tac_device_identifier):
        """
        Constructor for the class
        :param verbose: True if command to be run with verbose mode, False otherwise
        :param ftdi_api: FTDI APIs to be used
        :param gpio_api: GPIO APIs to be used
        """
        super().__init__(verbose)
        self._ftdi_api = ftdi_api
        self._gpio_api = gpio_api
        self._ftdi_base_url = ftdi_base_url
        self._tac_device_identifier = tac_device_identifier

    def init_ports(self):
        """
        Method to initialize the serial ports
        """
        if self._verbose:
            self._ftdi_api.Ftdi.show_devices()  # List all available FTDI Devices
            
        dev_list = self._ftdi_api.Ftdi.list_devices()
        if dev_list is None:
            print('Error: Dev list is None')
            return False

        serial_devices = 0
        serial_dev_url = ""
        for desc, ifcount in sorted(dev_list):
            # print("Desc " + str(desc.sn))
            if desc.sn.startswith(self._tac_device_identifier):
                serial_devices = ifcount
                serial_dev_url = desc.sn
                break

        if serial_devices != 4:
            if not self._verbose:
                self._ftdi_api.Ftdi.show_devices()
            print('ERROR: Expected 4 Serial ports, got:', serial_devices)
            return False
            
        # Create handles for the GPIO FTDI channels
        self._serial_dev_data['A'] = self._gpio_api.GpioAsyncController()
        self._serial_dev_data['B'] = self._gpio_api.GpioAsyncController()
        self._serial_dev_data['C'] = self._gpio_api.GpioAsyncController()
        self._serial_dev_data['D'] = self._gpio_api.GpioAsyncController()

        url_suffix = self._ftdi_base_url + serial_dev_url
        self._serial_dev_data['A'].open_from_url(f"{url_suffix}/1", direction=255)
        self._serial_dev_data['B'].open_from_url(f"{url_suffix}/2", direction=255)
        self._serial_dev_data['C'].open_from_url(f"{url_suffix}/3", direction=255)
        self._serial_dev_data['D'].open_from_url(f"{url_suffix}/4", direction=255)

        return True

    def wrapup_ports(self):
        """
        Method to clear the serial ports data
        """
        for port_label, ftd_handle in self._serial_dev_data.items():
            if self._verbose:
                print("Wrapping up " + port_label)
            # ftd_handle.setBitMode(0x0, 0x00)
            ftd_handle.close(True)

        self._serial_dev_data.clear()
        print("Serial ports cleaned up")

    def write(self, serial_port, value):
        """
        Method to change EDL pins state
        :param serial_port: Serial port label, supported values are 'A', 'B', 'C' and 'D'
        :param value: Value to be written
        """
        channel = self._serial_dev_data[serial_port]
        channel.write(value)

    def read(self, serial_port):
        """
        Method to read the current pin value
        """
        channel = self._serial_dev_data[serial_port]
        return channel.read()


class FTDIWindows(FTDIInterface):
    """
    Class to handle the pin changes using Windows FTDI APIs
    """
    def __init__(self, verbose, ftdi_api, tac_device_identifier):
        """
        Constructor for the class
        :param verbose: True if command to be run with verbose mode, False otherwise
        :param ftdi_api: FTDI APIs to be used
        :param tac_device_identifier: tac device identifier
        """
        super().__init__(verbose)
        self._ftdi_api = ftdi_api
        self._tac_device_identifier = tac_device_identifier

    def init_ports(self):
        """
        Method to initialize the serial ports
        """
        dev_list = self._ftdi_api.listDevices()
        if dev_list is None:
            print('Error: Dev list is None')
            return False

        for index, bt_name in enumerate(dev_list):
            if bt_name is None:
                print("Skipping Device: Not valid", index)
                continue

            serial_dev_name = bt_name.decode("utf-8")
            if not serial_dev_name.startswith(self._tac_device_identifier):
                print("Skipping device: Name not starting with", self._tac_device_identifier)
                continue

            port_label = serial_dev_name[-1]
            if port_label != 'A' and port_label != 'B' and port_label != 'C' and port_label != 'D':
                print("Skipping device: Name not ending with [A,B,C,D]")
                continue

            self._serial_dev_data[port_label] = bt_name
            # print('device found: ' + serial_dev_name)

        if len(self._serial_dev_data) != 4:
            print('dev_list', dev_list)
            print('ERROR: Expected 4 Serial ports, got:', len(self._serial_dev_data))
            return False

        # print("Devices found: ", len(self._serial_dev_data))

        for port_label in self._serial_dev_data:
            bt_name = self._serial_dev_data[port_label]
            self._serial_dev_data[port_label] = self._ftdi_api.openEx(bt_name)
            bit_mode = self._serial_dev_data[port_label].getBitMode()
            if self._verbose:
                print('Device initialized: ' + str(bt_name) + ', ' + byte_seq_string(bit_mode))

        self._serial_dev_data['A'].setBitMode(255, 0x01)
        self._serial_dev_data['B'].setBitMode(255, 0x01)
        self._serial_dev_data['C'].setBitMode(255, 0x01)
        self._serial_dev_data['D'].setBitMode(255, 0x01)

        for port_label in self._serial_dev_data:
            bit_mode = self._serial_dev_data[port_label].getBitMode()
            if self._verbose:
                print('New Bit mode: ' + str(bit_mode) + " => " + hex(bit_mode) + " => " + bin(bit_mode))

        return True

    def wrapup_ports(self):
        """
        Method to clear the serial ports data
        """
        for port_label, ftd_handle in self._serial_dev_data.items():
            if self._verbose:
                print("Wrapping up " + port_label)
            # ftd_handle.setBitMode(0x0, 0x00)
            ftd_handle.close()

        self._serial_dev_data.clear()
        print("Serial ports cleaned up")

    def write(self, serial_port, value):
        """
        Method to change EDL pins state
        :param serial_port: Serial port label, supported values are 'A', 'B', 'C' and 'D'
        :param value: value to be written
        """
        serial_device = self._serial_dev_data[serial_port]
        serial_device.write(bytes([value]))

    def read(self, serial_port):
        """
        Method to read the current pin value
        """
        serial_device = self._serial_dev_data[serial_port]
        return serial_device.getBitMode()


def get_ftdi_interface(ftdi_base_url, tac_device_identifier):
    """
    Method to get the FTDI interface object based on platform (Currently supporting: Linux and windows)
    :param ftdi_base_url: ftdi base url like ftdi"\\::
    :param tac_device_identifier: tac device identifier like AT, GPIO0
    :return: FTDI Interface object
    """
    os_label = platform.system()
    if os_label == 'Windows':
        print("Running on Windows")

        try:
            import ftd2xx as win_ftdi
        except ImportError or ModuleNotFoundError:
            print('ERROR: FTDI module "ftd2xx" not installed (Try: pip install ftd2xx)')
            return None

        return FTDIWindows(False, win_ftdi, tac_device_identifier)

    elif os_label == 'Linux':
        print("Running on Linux")
        
        try:
            import pyftdi.ftdi as lx_ftdi
            import pyftdi.gpio as lx_gpio
        except ImportError or ModuleNotFoundError:
            print('ERROR: FTDI module "pyftdi" not installed (Try: pip install pyftdi)')
            return None

        return FTDILinux(False, lx_ftdi, lx_gpio, ftdi_base_url, tac_device_identifier)

    print("ERROR: Running on Unknown OS: " + platform.system())
    return None

