## Scripts to interface with Ride MX SOCs 
This scripts helps to execute different commands like EDL, Reset etc to change the board states.
 
__NOTE__: 
* These scripts are tested against Python 3.x
* These scripts are for RIDE MX Gen4 Platforms

### Usage
Please find the usage below:<br />
__python <Command_python_file>__ 

Examples are:
```
python SystemWidePowerOn.py
python SystemWidePowerOff.py
```

### RideMXInterface.py
This component/python-code makes the code independent of host platform (Windows or linux).
* For Windows python package __ftd2xx__ is used to interact with board.
* For Linux python package __pyftdi__ is used to interact with board.
