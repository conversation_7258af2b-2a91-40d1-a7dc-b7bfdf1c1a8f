'''
This script will only works for Ride MX platform as all the parameters are hardcoded here.
==========================================================
To use this script from another module - Do the following:
from RIDE-MX-Control import FTDIDevice

FTDIObject = FTDIDevice()
FTDIObject.powerCycle() # To power Cycle
FTDIObject.ControlDevice("<Sequence>") # To do everything else

<Sequence> can be chosen from the following list:
----------------
ON, OFF, RESET,
S1_MD_SAIL_EDL, S1_FASTBOOT,
S2_MD_SAIL_EDL, S2_FASTBOOT,
S1_S2_MD_SAIL_EDL
----------------
==================================================================
'''

import time
from collections import namedtuple
import logging
import sys
import RideMXInterface

UsbDeviceDescriptor = namedtuple("UsbDeviceDescriptor",["vid", "pid", "bus", "address","sn", "index", "description"])
log = logging.getLogger(__name__)

tac_seq =[
'ON',
'OFF',
'RESET',
'S1_MD_SAIL_EDL',
'S1_FASTBOOT',
'S2_MD_SAIL_EDL',
'S2_FASTBOOT',
'S1_S2_MD_SAIL_EDL',
]

class FTDIDevice():
    # ftdi_base_url = "ftdi://::"
    # tac_device_identifier = "AT"


    def __init__(self,ftdi_base_url = "ftdi://::",tac_device_identifier = "AT"):
        self.ftdi_base_url = ftdi_base_url
        self.tac_device_identifier = tac_device_identifier

    def ControlDevice(self, selectedOption):
        log.info(f"Running {selectedOption} on the device")
        self.sequenceHandler(seq_name=selectedOption)
        time.sleep(4)

    def sequenceHandler(self,seq_name=""):
        log.info(f"Sequence Handler input: Sequence= {seq_name}")
        
        ftdi_interface = RideMXInterface.get_ftdi_interface(self.ftdi_base_url, self.tac_device_identifier)
        if ftdi_interface is None:
            return

        status = ftdi_interface.init_ports()
        if not status:
            return
        
        print(ftdi_interface.read('A'))
        print(ftdi_interface.read('B'))
        print(ftdi_interface.read('C'))
        print(ftdi_interface.read('D'))
        
        if seq_name == "ON":
            print("Setting target ON, writing 0x40 to port D")
            ftdi_interface.write('B', 0x40)
            ftdi_interface.write('D', 0x40)
            print(ftdi_interface.read('D'))
            print("Done")
            
        if seq_name == "OFF":
            print("Setting target ON, writing 0x42 to port D")
            ftdi_interface.write('B', 0x40)
            ftdi_interface.write('D', 0x42)
            print(ftdi_interface.read('D'))
            print("Done")

        if seq_name == "RESET":
            ftdi_interface.write('D', 0x42)
            ftdi_interface.write('B', 0x40)
            time.sleep(2)
            ftdi_interface.write('D', 0x40)

        if seq_name == "S1_MD_SAIL_EDL":
            print("Setting S1_MD_SAIL_EDL")
            ftdi_interface.write('D', 0x62)
            ftdi_interface.write('C', 0x10)
            ftdi_interface.write('B', 0x40)
            time.sleep(1.5)
            ftdi_interface.write('D', 0x60)
            time.sleep(5)
            ftdi_interface.write('D', 0x40)
            ftdi_interface.write('C', 0x00)
            print("Done")

        if seq_name == "S2_MD_SAIL_EDL":
            print("S2_MD_SAIL_EDL")
            ftdi_interface.write('D', 0x42)
            ftdi_interface.write('B', 0x62)
            ftdi_interface.write('A', 0x10)
            time.sleep(1.5)
            ftdi_interface.write('D', 0x40)
            time.sleep(20)
            ftdi_interface.write('B', 0x42)
            ftdi_interface.write('A', 0x00)

        if seq_name == "S1_FASTBOOT":
            ftdi_interface.write('D', 0x46)
            ftdi_interface.write('B', 0x40)
            time.sleep(2)
            ftdi_interface.write('D', 0x44)
            time.sleep(8)
            ftdi_interface.write('D', 0x40)

        if seq_name == "S2_FASTBOOT":
            ftdi_interface.write('D', 0x42)
            ftdi_interface.write('B', 0x44)
            time.sleep(2)
            ftdi_interface.write('D', 0x40)
            time.sleep(10)
            ftdi_interface.write('B', 0x40)

        if seq_name == "S1_S2_MD_SAIL_EDL":
            ftdi_interface.write('D', 0x62)
            ftdi_interface.write('C', 0x10)
            ftdi_interface.write('B', 0x62)
            ftdi_interface.write('A', 0x10)
            time.sleep(1.5)
            ftdi_interface.write('D', 0x60)
            time.sleep(10)
            ftdi_interface.write('D', 0x40)
            ftdi_interface.write('C', 0x00)
            ftdi_interface.write('B', 0x42)
            ftdi_interface.write('A', 0x00)
        
        ftdi_interface.wrapup_ports()
        

def main():
    print("Running From Main")
    logging.basicConfig(
		format = '%(asctime)s.%(msecs)03d %(levelname)-8s %(message)s',
		level = logging.INFO,
		datefmt = '%Y-%m-%d %H%M:%S',
		stream=sys.stdout,
	)
    logging.info("Doing nothing though")

if __name__ == '__main__':
    main()
else:
    logging.basicConfig(
		format = '%(asctime)s.%(msecs)03d %(levelname)-8s %(message)s',
		level = logging.INFO,
		datefmt = '%Y-%m-%d %H%M:%S',
		stream=sys.stdout,
	)
    logging.info("Importing RideMXControl")
