Python 3 based TAC (FTDI Support on RIDE-MX) Scripts

TAC Script Version: 1.0 for RIDE-MX

Prerequisites:
- Install Python3+ (ex: 3.7 @ https://www.python.org/downloads/release/python-370/ )
- Install libusb (sudo apt-get install libusb-1.0)
- Install Python3 FTDI package (python3 -m pip install pyftdi)
- Add the following contents to the new file created : /etc/udev/rules.d/11-ftdi.rules
# /etc/udev/rules.d/11-ftdi.rules
# FTDI Chips
SUBSYSTEM=="usb", ATTR{idVendor}=="0403", GROUP="plugdev", MODE="0666"

- Run the following commands
sudo udevadm control --reload-rules
sudo udevadm trigger
sudo adduser $USER plugdev



How to use:
- Clone the github repo (git clone https://github.qualcomm.com/ARMOR/QUIVER)
- Change directory to local ride_tac/<platform>
- Install Python Prerequisites from aboce

The downloaded code supports many operations - 
1. System Power Off (example in SystemWidePowerOff.py)
2. System Power On (example in SystemWidePowerOn.py)
3. Boot to Fastboot SOCx(examples :BootToFastBootSOC1.py, BootToFastBootSOC2.py)
4. Boot to Emergency Download Mode (EDL) (examples : BootToEDLSOC1.py , BootToEDLSOC1.py)

OnGoing Conditions - 

Please feel free to combine the functionality based on examples above for your needs.








