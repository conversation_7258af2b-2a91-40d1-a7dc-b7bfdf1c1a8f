#!/bin/bash -e

# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

# Exit with error
die() {
    echo $1
    exit 1
}

usage() {
    echo "$0 --buildpath [BUILD_DIRECTORY] --numsoc [1 (SX)| 2 (MX)]"
    exit 1
}

# Get the directory of the script
script_directory="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Print the script's directory to the terminal
echo "The script is located in: $script_directory"

[ -r $script_directory/bsp_config ] || die "Config file ($script_directory/bsp_config) not found."
source $script_directory/bsp_config || echo "source of confifg file : bsp_config failed."

while [[ $# -gt 0 ]]; do
  key="$1"

  case $key in
    --help)
      usage
      shift
      ;;
    --buildpath)
      build_dir=$2
      shift 
      shift 
      ;;
    --numsoc)
      PLATFORM_TYPE=$2
      shift 
      shift 
      ;;
    *)
      POSITIONAL+=("$1") 
      shift 
      ;;
  esac
done

fastboot_flash_flag=false

if [ -z "$build_dir" ]; then 
   [ "$build_dir" = "" ] && echo "Build Path not entered" && usage
fi

echo "Platform type is $AU_TYPE - $PLATFORM_TYPE"

# PCAT Devices and store IDs
echo "Looking for devices in Fastboot Mode..."
# Execute fastboot devices command and capture the output
fastboot_devices=$(fastboot devices 2>/dev/null)

# Check if there are any devices connected
if [ -z "$fastboot_devices" ]; then
    echo "No fastboot devices found."
else
    echo "Fastboot devices found:"
    echo "$fastboot_devices"
fi

if [ ! -z "$fastboot_devices" ]
then
    echo "Device detected in Fastboot mode!"
    cd $build_dir/apps/qnx_ap
    fastboot flash abl_a target/hypervisor/host/abl-image-lemans/abl_fastboot.elf
    fastboot flash boot_a target/hypervisor/host/out_lemans/boot_qdrive_iosock.img
    fastboot flash system_a target/hypervisor/host/out_lemans/system_qdrive_iosock.img.sparse
    fastboot flash ifs2_a  target/hypervisor/host/out_lemans/ifs2_qdrive_iosock.img
    fastboot flash persist target/hypervisor/host/out_lemans/persist_qnx.img.sparse
    fastboot flash hyp_a target/hypervisor/host/out_lemans/signed/default/qhee/mifs_hyp_qdrive_iosock.img
    fastboot_flash_flag=true
else
    die "No devices found in EDL, please put device in EDL mode before flashing."
fi

if [ "$fastboot_flash_flag" = true ]; then
    # Controller/Console looks for this to determine success.
    echo "Flash build is successful"
else
    die "Flash build unsuccessful, check logs..."
fi
