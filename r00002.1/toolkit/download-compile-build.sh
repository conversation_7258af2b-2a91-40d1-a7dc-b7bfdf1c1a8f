#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

Meta_setup_env_post(){

    #setting up build dir as qnx since test repo has been removed due to redundancy for this release
    log_msg "DEBUG" "Meta_setup_env_post: start"
    TEST_DEVICE_SYNC_REPO=${PRODUCT}_hlos_dev_qnx
    CHIPCODE_TEST_DEVICE_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${TEST_DEVICE_SYNC_REPO}.git"
    log_msg "DEBUG" "Meta_setup_env_post: end"

}

Meta_build_pre(){

    log_msg "DEBUG" "Meta_build_pre: start"
    #setting QNX_ENABLED to empty string because test dir is not synced
    QNX_ENABLED=""
    log_msg "DEBUG" "Meta_build_pre: end"

}

QNX_sync_override(){

    log_msg "DEBUG" "QNX_sync_override: start"
    QNX_SYNC_DIR="${BUILD_DIR}"
    QNX_BINS_DIR=$QNX_SYNC_DIR/apps/qnx_ap/qnx_bins

    # Respect custom prebuilt directory if set, otherwise use default
    if [ -n "$QNX_CUSTOM_PREBUILT_DIR" ]; then
        echo "QNX_sync_override: Using custom prebuilt directory: $QNX_CUSTOM_PREBUILT_DIR"
        QNX_PREBUILT_DIR=$QNX_CUSTOM_PREBUILT_DIR
    else
        QNX_PREBUILT_DIR=$QNX_BINS_DIR/$QNX_PREBUILT
    fi

    QNX_BUILD_MARKER="$QNX_SYNC_DIR/QNX_build_complete"
    target_root=$QNX_SYNC_DIR

    # Check if the Makefile exists before moving it and move it as per RN
    if [ -f "${target_root}/apps/qnx_ap/AMSS/multimedia/fastcv/Makefile" ]; then
        mv "${target_root}/apps/qnx_ap/AMSS/multimedia/fastcv/Makefile" "${target_root}/apps/qnx_ap/AMSS/multimedia/fastcv/Makefile.dnm"
        echo "Makefile moved successfully."
    else
        echo "Makefile not found. Skipping move operation."
    fi

    # Check if the root top Makefile exists before removing the line as per RN
    if [ -f "${target_root}/apps/qnx_ap/Makefile" ]; then
        sed -i '/cp -rf ${BSP_ROOT}\/AMSS\/platform\/hwdrivers\/wired_peripherals\/storage\/ufs_bsp\/public\/amss\/\* install\/usr\/include/d' "${target_root}/apps/qnx_ap/Makefile"
        echo "Line removed successfully from root top Makefile."
    else
        echo "Root top Makefile not found. Skipping line removal."
    fi
    log_msg "DEBUG" "QNX_sync_override: end"

}

LA_sync_override() {
    log_msg "DEBUG" "LA_sync_override: start"
    log_msg "INFO" "Syncing LA..."

    # Download the hlos_dev_la distro
    log_msg "INFO" "Downloading the HLOS DEV LA Distro..."
    if [[ "$LA_SYNC_DIR" == *sa8797p* ]]; then
        echo "Skipping git_clone_chipcode as SP contains sa8797p"
    else
        git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_LA_REPO" "apps_kernel" "${LA_SYNC_DIR}"
    fi

    # QSSI STANDALONE TREE
    if [[ -e "${LA_QSSI_SYNC_MARKER}" ]]; then
        log_msg "INFO" "System OSS Prop process previously completed, skipping..."
    else
        tst "rm -rf $QSSI_SYNC_DIR"
        tst "mkdir -p $QSSI_SYNC_DIR"

        repo_sync "${QSSI_SYNC_DIR}" "${CODELINARO}/${QSSI_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_LA_QSSI}.xml"
        tst "touch ${LA_QSSI_SYNC_MARKER}"
    fi

    # VENDOR STANDALONE TREE
    if [[ -e "${LA_VENDOR_SYNC_MARKER}" ]]; then
        log_msg "INFO" "Vendor OSS Prop process previously completed, skipping..."
    else
        tst "rm -rf $VENDOR_SYNC_DIR"
        tst "mkdir -p $VENDOR_SYNC_DIR"

        repo_sync "${VENDOR_SYNC_DIR}" "${CODELINARO}/${VENDOR_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_LA_VENDOR}.xml"
        tst "touch ${LA_VENDOR_SYNC_MARKER}"
    fi

    # KERNEL.PLATFORM STANDALONE TREE
    if [[ -e "${LA_KERNEL_SYNC_MARKER}" ]]; then
        log_msg "INFO" "Kernel OSS Prop process previously completed, skipping..."
    else
	tst "rm -rf $KERNEL_SYNC_DIR"
        tst "mkdir -p $KERNEL_SYNC_DIR"

        repo_sync "${KERNEL_SYNC_DIR}" "${CODELINARO}/${KERNEL_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_KERNEL}.xml"
        tst "touch ${LA_KERNEL_SYNC_MARKER}"
    fi
    log_msg "DEBUG" "LA_sync_override: end"

}

LA_sync_post() {
    log_msg "DEBUG" "LA_sync_post: start"

    log_msg "INFO" "Moving folder to SYSTEM_BUILD_DIR AND VENDOR_BUILD_DIR as required"

    #SYSTEM_BUILD_DIR changes
    tst "mkdir -p ${LA_QSSI_BUILD_DIR}/vendor/qcom"
    tst "cp -r $LA_SYNC_DIR/lagvm_qssi/LINUX/android/vendor/qcom/proprietary $LA_QSSI_BUILD_DIR/vendor/qcom/"
    tst "chmod -R 555 $LA_QSSI_BUILD_DIR/vendor/qcom/"

    #VENDOR_BUILD_DIR changes
    tst "mkdir -p ${LA_VENDOR_BUILD_DIR}/kernel_platform"
    tst "mkdir -p ${LA_VENDOR_BUILD_DIR}/vendor/qcom"
    #vendor file changes
    tst "cp -r $LA_SYNC_DIR/apps_kernel/kernel_platform/qcom/ $LA_VENDOR_BUILD_DIR/kernel_platform/"
    tst "cp -r $LA_SYNC_DIR/lagvm_qssi/LINUX/android/vendor/qcom/proprietary/ $LA_VENDOR_BUILD_DIR/vendor/qcom/"
    tst "cp -r $LA_SYNC_DIR/lagvm/LINUX/android/vendor/qcom/proprietary/ $LA_VENDOR_BUILD_DIR/vendor/qcom/"
    tst "chmod -R 555 $LA_VENDOR_BUILD_DIR/kernel_platform/"
    tst "chmod -R 555 $LA_VENDOR_BUILD_DIR/vendor/qcom/"

    # Note: Makefile violation fixes are applied in la_sync_build.sh during the build phase

    log_msg "DEBUG" "LA_sync_post: end"
}

# Get the directory of the script
script_directory="$( cd "$( dirname "${BASH_SOURCE[0]}")"  && pwd)"

# Setting up common scripts
UTILITIES_DIR="${script_directory}/utilities/scripts/"

source "${UTILITIES_DIR}/logging.sh"
source "${UTILITIES_DIR}/utilities.sh"
source "${UTILITIES_DIR}/meta_build.sh"
source "${UTILITIES_DIR}/hlos_sync_build.sh"
source "${UTILITIES_DIR}/nhlos_sync_build.sh"

# Print the script's directory to the terminal
log_msg "INFO" "The script is located in: $script_directory"

# =================== Setup script variables and environment ======================================

[ -r $script_directory/bsp_config ] || die "Config file ($script_directory/bsp_config) not found."
source $script_directory/bsp_config || log_msg "WARN" "source of confifg file : bsp_config failed."

# Controller passes parameters via file instead of commandline
if [ -f "$EXTRA_PARAMETER_FILE" ]; then
    source "$EXTRA_PARAMETER_FILE" || echo "source of confifg file : $EXTRA_PARAMETER_FILE failed."
    # It contains passwords so remove for security
    rm "$EXTRA_PARAMETER_FILE"
    export QNXLM_LICENSE_FILE="$QNX_LICENSE_SERVER"
fi

parse_options "$@"

setup_ride_sdk

Meta_validate_inputs

call_function Meta_setup_env

Meta_sync

HLOS_validate_inputs

HLOS_setup_env

HLOS_sync_build

NHLOS_validate_inputs

NHLOS_setup_env

NHLOS_sync_build

call_function Meta_build
