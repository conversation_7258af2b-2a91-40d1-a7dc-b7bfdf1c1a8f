#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.
#

# Script setup
declare -A log_levels=([DEBUG]=0 [INFO]=1 [WARN]=2 [ERROR]=3)

function log_msg() {
    if [ -z "$SCRIPT_LOGGING_LEVEL" ]; then
        SCRIPT_LOGGING_LEVEL="INFO"
    fi
    local log_priority=$1
    local log_msg=$2
    # check if level exists
    [[ ${log_levels[$log_priority]} ]] || return 1
    #check if level is enough
    ((${log_levels[$log_priority]} < ${log_levels[$SCRIPT_LOGGING_LEVEL]})) && return 2
    #log here
    echo "${log_priority} : ${log_msg}"
}

function log_cmd() {
    log_msg "DEBUG" "$@"
    if [ -n "$CMDS_LOG_FILE" ]; then
        if [ ! -e "$CMDS_LOG_FILE" ]; then
            touch "$CMDS_LOG_FILE"
        fi
        echo "$@" >> "$CMDS_LOG_FILE"
    fi
}
