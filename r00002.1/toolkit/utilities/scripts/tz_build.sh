#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/logging.sh

function TZ_validate_inputs() {
    log_msg "DEBUG" "TZ_validate_inputs: start"

    if [ -z "$BUILD_DIR" ]; then
        log_msg "ERROR" "BUILD_DIR is not set"
        usage
    fi

    if [ -z "$CHIPCODE_TAG" ]; then
        log_msg "ERROR" "CHIPCODE_TAG is not set"
        usage
    fi

    log_msg "DEBUG" "TZ_validate_inputs: end"
}

function TZ_setup_env() {
    log_msg "DEBUG" "TZ_setup_env: start"

    TZ_BUILD_MARKER="$BUILD_DIR/TZ_build_complete"

    log_msg "DEBUG" "TZ_setup_env: end"
}

function TZ_build() {
    log_msg "DEBUG" "TZ_build: start"

    if [ -e "$TZ_BUILD_MARKER" ]; then
        log_msg "INFO" "TZ Build already completed. Skipping build."
    else
        tst cd "$BUILD_DIR/tz/trustzone_images/build/ms"
        tst export SECTOOLS=/runtime/build/${CHIPCODE_TAG}/common/sectoolsv2/ext/Linux/sectools

        #Backup the current python link and create python3 link
        mv /usr/bin/python /usr/bin/python_backup
        mv /usr/bin/python3 /usr/bin/python3_backup
        ln -s /usr/local/bin/python3.11 /usr/bin/python
        ln -s /usr/local/bin/python3.11 /usr/bin/python3

        # Lemans
        #clean any prev built lemans
        tst python3 build_all.py -b TZ.XF.5.0 CHIPSET=lemans_qh --config=build_config_deploy_lemans_qh.xml --clean
        tst python3 build_all.py -b TZ.XF.5.0 CHIPSET=lemans_qh --config=build_config_deploy_lemans_qh.xml

        # Monaco
        #clean any prev built monaco
        tst python3 build_all.py -b TZ.XF.5.0 CHIPSET=monaco_qh --config=build_config_deploy_monaco_qh.xml --clean
        tst python3 build_all.py -b TZ.XF.5.0 CHIPSET=monaco_qh --config=build_config_deploy_monaco_qh.xml

        # Create the build file to indicate the build is complete
        tst touch "$TZ_BUILD_MARKER"

        #Restore the original python link
        rm /usr/bin/python
        rm /usr/bin/python3
        mv /usr/bin/python_backup /usr/bin/python
        mv /usr/bin/python3_backup /usr/bin/python3
    fi

    log_msg "DEBUG" "TZ_build: end"
}
