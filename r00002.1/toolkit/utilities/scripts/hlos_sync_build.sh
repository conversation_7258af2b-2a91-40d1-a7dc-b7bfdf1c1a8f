#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source "${UTILITIES_DIR}/logging.sh"

function HLOS_validate_inputs() {
    log_msg "DEBUG" "HLOS_validate_inputs: start"

    if [ -n "$QNX_ENABLED" ]; then
        source "${UTILITIES_DIR}/qnx_sync_build.sh"
        call_function QNX_validate_inputs
    fi

    if [ -n "$HGY_ENABLED" ]; then
        source "${UTILITIES_DIR}/hgy_sync_build.sh"
        call_function HGY_validate_inputs
    fi

    if [ -n "$LA_ENABLED" ]; then
        source "${UTILITIES_DIR}/la_sync_build.sh"
        call_function LA_validate_inputs
    fi

    if [ -n "$LV_ENABLED" ]; then
        source "${UTILITIES_DIR}/lv_sync_build.sh"
        call_function LV_validate_inputs
    fi

    log_msg "DEBUG" "HLOS_validate_inputs: end"
}

function HLOS_setup_env() {
    if [ -n "$QNX_ENABLED" ]; then
        call_function QNX_setup_env
    fi

    if [ -n "$HGY_ENABLED" ]; then
        call_function HGY_setup_env
    fi

    if [ -n "$LA_ENABLED" ]; then
        call_function LA_setup_env
    fi

    if [ -n "$LV_ENABLED" ]; then
        call_function LV_setup_env
    fi

}

function HLOS_sync_build() {
    # =================== Building QNX NOW ============================================================
    if [ -n "$QNX_ENABLED" ]; then
        call_function QNX_sync
        call_function QNX_build
    fi

    # =================== Building HGY NOW =============================================================
    if [ -n "$HGY_ENABLED" ]; then
        call_function HGY_sync
        call_function HGY_build
    fi

    # =================== Building LA NOW =============================================================
    if [ -n "$LA_ENABLED" ]; then
        call_function LA_sync
        call_function LA_build
    fi

    # =================== Building LV NOW =============================================================
    if [ -n "$LV_ENABLED" ]; then
        call_function LV_sync
        call_function LV_build
    fi

}
