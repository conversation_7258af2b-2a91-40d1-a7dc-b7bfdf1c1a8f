#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.
#

source "${UTILITIES_DIR}/logging.sh"
source "${UTILITIES_DIR}/git_utilities.sh"

function parse_options() {
    log_msg "DEBUG" "parse_options: start"

    while [[ $# -gt 0 ]]; do
        key="$1"

        case $key in
            --help)
                usage
                shift
                ;;
            --tag)
                CHIPCODE_TAG=$2
                shift
                shift
                ;;
            --buildpath)
                BUILD_DIR=${2%/}/$CHIPCODE_TAG
                shift
                shift
                ;;
            --qcuser)
                QC_USER=$2
                shift
                shift
                ;;
            --qcpassword)
                QC_PASSWORD=$2
                shift
                shift
                ;;
            --qnxuser)
                MYQNX_USERNAME=$2
                shift
                shift
                ;;
            --qnxpassword)
                MYQNX_PASSWORD=$2
                shift
                shift
                ;;
            --codelinarotoken)
                CODELINARO_ACCESS_TOKEN=$2
                shift
                shift
                ;;
            --qpkg_plist_path)
                QPKG_CUSTOM_PLIST=$2
                shift
                shift
                ;;
            --qnx_prebuilt_dir)
                QNX_CUSTOM_PREBUILT_DIR=$2
                shift
                shift
                ;;
            --qnx_swc_url)
                QNX_SWC_URL=$2
                shift
                shift
                ;;
            --gitorg)
                GITORG=$2
                shift
                shift
                ;;
            --gitproxy)
                GIT_PROXY=$2
                shift
                shift
                ;;
            --customerid)
                CUSTOMER_ID=$2
                shift
                shift
                ;;
            --qnxlicenseserver)
                export QNXLM_LICENSE_FILE=$2
                shift
                shift
                ;;
            --patch)
                PATCH_DIR=$2
                shift
                shift
                ;;
            --flavor)
                FLAVOR=$2
                shift
                shift
                ;;
            --hexagon_path)
                HEXAGON_ROOT=$2
                shift
                shift
                ;;
            --command_log_file)
                CMDS_LOG_FILE=$2
                shift
                shift
                ;;
            --debug)
                SCRIPT_LOGGING_LEVEL='DEBUG'
                shift
                ;;
            --bsp_config)
                BSP_CONFIG=$2
                shift
                shift
                ;;
            *)
                POSITIONAL+=("$1")
                shift
                ;;
        esac
    done

    if [ -z "$GITORG" ]; then
        GITORG=qualcomm
    fi

    # Controller passes parameters via file instead of commandline
    if [ -f "$EXTRA_PARAMETER_FILE" ]; then
        source "$EXTRA_PARAMETER_FILE" || echo "source of confifg file : $EXTRA_PARAMETER_FILE failed."
        # It contains passwords so remove for security
        rm "$EXTRA_PARAMETER_FILE"
        export QNXLM_LICENSE_FILE="$QNX_LICENSE_SERVER"
    fi

    # log all read parameters under DEBUG
    log_msg "DEBUG" "CHIPCODE_TAG=\"$CHIPCODE_TAG\""
    log_msg "DEBUG" "BUILD_DIR=\"$BUILD_DIR\""
    # Enable for local debugging only
    # log_msg "DEBUG" "QC_USER=\"$QC_USER\""
    # log_msg "DEBUG" "QC_PASSWORD=\"$QC_PASSWORD\""
    # log_msg "DEBUG" "MYQNX_USERNAME=\"$MYQNX_USERNAME\""
    # log_msg "DEBUG" "MYQNX_PASSWORD=\"$MYQNX_PASSWORD\""
    # log_msg "DEBUG" "CODELINARO_ACCESS_TOKEN=\"$CODELINARO_ACCESS_TOKEN\""
    log_msg "DEBUG" "GITORG=\"$GITORG\""
    log_msg "DEBUG" "GIT_PROXY=\"$GIT_PROXY\""
    log_msg "DEBUG" "CUSTOMER_ID=\"$CUSTOMER_ID\""
    log_msg "DEBUG" "QNXLM_LICENSE_FILE=\"$QNXLM_LICENSE_FILE\""
    log_msg "DEBUG" "PATCH_DIR=\"$PATCH_DIR\""
    log_msg "DEBUG" "FLAVOR=\"$FLAVOR\""
    log_msg "DEBUG" "CMDS_LOG_FILE=\"$CMDS_LOG_FILE\""
    log_msg "DEBUG" "SCRIPT_LOGGING_LEVEL=\"$SCRIPT_LOGGING_LEVEL\""
    log_msg "DEBUG" "POSITIONAL=\"${POSITIONAL[@]}\""

    log_msg "DEBUG" "parse_options: end"

}

function usage() {
    log_msg "INFO" "download-compile-build.sh"
    log_msg "INFO" " "
    log_msg "INFO" "options:"
    log_msg "INFO" "--help                  Shows this menu"
    log_msg "INFO" "--tag                   Chipcode SA8650P tag"
    log_msg "INFO" "--buildpath             Base directory for the build"
    log_msg "INFO" "--qcuser                Qualcomm Chipcode Username"
    log_msg "INFO" "--qcpassword            Qualcomm Chipcode Password"
    log_msg "INFO" "--qnxuser               QNX Username"
    log_msg "INFO" "--qnxpassword           QNX Password"
    log_msg "INFO" "--gitorg                GIT Organization (default - qualcomm)"
    log_msg "INFO" "--codelinarotoken       Code Linaro Token"
    log_msg "INFO" "--qnxlicenseserver      Specify QNX shared license server"
    log_msg "INFO" "--gitproxy              GIT proxy info used for GIT/Chipcode"
    log_msg "INFO" "--customerid            customerid_value"
    log_msg "INFO" "--patch                 Execute any patch script if needed, uses whole directory, not just relative"
    log_msg "INFO" "--qpkg_plist_path       Path to custom QPKG plist file"
    log_msg "INFO" "--qnx_prebuilt_dir      Path to QNX custom prebuilt directory"
    log_msg "INFO" "--qnx_swc_url           URL for QNX SWC"
    log_msg "INFO" " "
    log_msg "INFO" "EXAMPLES: "
    log_msg "INFO" "./download-compile-build.sh --qcuser [USERNAME] --qcpassword [PASSWORD] --qnxuser [USER] --qnxpassword [PASS] --tag [CHIPCODE_TAG]"
    log_msg "INFO" "./download-compile-build.sh --tag [CHIPCODE_TAG] --patch /patch-directory"
    log_msg "INFO" "./download-compile-build.sh --qpkg_plist_path /path/to/custom.plist --qnx_prebuilt_dir /path/to/prebuilt_dir --qnx_swc_url http://example.qnx.net"
    exit 1
}

# Calls function_name_pre and function_name_post in order of
# 1. function_name_pre
# 2. function_name
# 3. function_name_post
function call_function() {
    log_msg "DEBUG" "call_function: start"
    local func_name="$1"

    # Check if function_pre exists and call it
    if declare -f "${func_name}_pre" > /dev/null; then
        log_msg "DEBUG" "Invoking ${func_name}_pre"
        ${func_name}_pre
    fi

    # Check if the passed function name exists and call it
    if declare -f "${func_name}_override" > /dev/null; then
        log_msg "DEBUG" "Invoking ${func_name}_override"
        ${func_name}_override
    else
        if declare -f "$func_name" > /dev/null; then
            log_msg "DEBUG" "Invoking ${func_name}"
            $func_name
        else
            echo "Function $func_name does not exist"
        fi
    fi

    # Check if function_pre exists and call it
    if declare -f "${func_name}_post" > /dev/null; then
        log_msg "DEBUG" "Invoking ${func_name}_post"
        ${func_name}_post
    fi
    log_msg "DEBUG" "call_function: end"
}

function die() {
    log_msg "ERROR" "$1"
    exit 1
}

function tst() {
    local cmmd="$@"
    log_cmd "$cmmd"
    eval $cmmd
    local ret=$?
    if [ $ret -ne 0 ]; then
        die "\"$cmmd\" failed with return=\"$ret\""
    fi
}

function trc() {
    local cmmd="$@"
    log_cmd "$cmmd"
    eval $cmmd
    local ret=$?
    if [ $ret -ne 0 ]; then
        log_msg "WARN" "\"$cmmd\" failed with return=\"$ret\""
    fi
}

function check_available_space() {
    log_msg "DEBUG" "check_available_space: start"
    available_space_kb=$(df --output=avail -k "$1" | tail -n 1)
    available_space_gb=$(awk "BEGIN {printf \"%.2f\n\", $available_space_kb / 1024 / 1024}")

    # Setting minimum space required to 300 GB if it is undefined in bsp_config
    if [ -z "$min_required_space_gb" ]; then
        min_required_space_gb=300
    fi

    # Check if available space is greater than or equal to the minimum required space
    if [ "$(echo "$available_space_gb >= $min_required_space_gb" | bc -l)" -eq 1 ]; then
        log_msg "INFO" "There is enough space in $1."
    else
        die "Not enough space in $1. Minimum required space is $min_required_space_gb GB, but only $available_space_gb GB is available."
    fi
    sleep 20
    log_msg "DEBUG" "check_available_space: end"
}

function set_user_group_for_ride_sdk() {
    log_msg "INFO" "Setting up user group settings for Ride SDK"
    # If ride_sdk_grp and ride_sdk_user are not present, create them
    # Create the group 'ride_sdk_grp' with GID 1000
    if ! grep -q "^ride_sdk_grp:" /etc/group; then
        groupadd -g 1000 ride_sdk_grp
    fi

    # Create the user 'ride_sdk_user' with UID 1000
    if ! id -u ride_sdk_user > /dev/null 2>&1; then
        useradd -u 1000 -G root ride_sdk_user
    fi

    # create home directory and add required files
    HOME_DIR=/home/<USER>
    if [ ! -d "$HOME_DIR" ]; then
        cp -rp /root $HOME_DIR
        chown -R ride_sdk_user:ride_sdk_grp $HOME_DIR
    fi
    log_msg "DEBUG" "set_user_group_for_ride_sdk: end"
}

function setup_ride_sdk() {
    log_msg "DEBUG" "setup_ride_sdk: start"
    META_ENABLED=${META_ENABLED:-TRUE}

    # Controller passes parameters via file instead of commandline
    if [ -f "$EXTRA_PARAMETER_FILE" ]; then
        source "$EXTRA_PARAMETER_FILE" || echo "source of confifg file : $EXTRA_PARAMETER_FILE failed."
        # It contains passwords so remove for security
        rm "$EXTRA_PARAMETER_FILE"
        export QNXLM_LICENSE_FILE="$QNX_LICENSE_SERVER"
    fi

    git_setup

    log_msg "DEBUG" "GITORG=\"$GITORG\""

    if [ -z "$BUILD_DIR" ]; then
        BUILD_DIR="$BUILD_PATH/$CHIPCODE_TAG"
    fi
    log_msg "DEBUG" "BUILD_DIR=\"$BUILD_DIR\""

    # enable HLOS builds based on options
    setup_hlos

    # enable HLOS builds based on options
    setup_nhlos

    log_msg "DEBUG" "setup_ride_sdk: end"
}

function setup_hlos() {
    log_msg "DEBUG" "setup_hlos: start"

    local enabled_hlos=" $ENABLED_BUILDS "

    # check for QNX
    if [[ "$enabled_hlos" == *" QNX "* ]]; then
        QNX_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "QNX_ENABLED=\"$QNX_ENABLED\""

    # check for LA
    if [[ "$enabled_hlos" == *" LA "* ]]; then
        LA_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "LA_ENABLED=\"$LA_ENABLED\""

    # check for LV
    if [[ "$enabled_hlos" == *" LV "* ]]; then
        LV_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "LV_ENABLED=\"$LV_ENABLED\""

    # check for HGY
    if [[ "$enabled_hlos" == *" HGY_5 "* ]]; then
        HGY_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "HGY_ENABLED=\"$HGY_ENABLED\""

    log_msg "DEBUG" "setup_hlos: end"
}

function setup_nhlos() {
    log_msg "DEBUG" "setup_hlos: start"

    local enabled_nhlos=" $ENABLED_NHLOS_BUILDS "

    # check for AOP
    if [[ "$enabled_nhlos" == *" AOP "* ]]; then
        AOP_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "AOP_ENABLED=\"$AOP_ENABLED\""

    # check for BOOT
    if [[ "$enabled_nhlos" == *" BOOT "* ]]; then
        BOOT_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "BOOT_ENABLED=\"$BOOT_ENABLED\""

    # check for DSP
    if [[ "$enabled_nhlos" == *" DSP "* ]]; then
        DSP_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "DSP_ENABLED=\"$DSP_ENABLED\""

    # check for TZ
    if [[ "$enabled_nhlos" == *" TZ "* ]]; then
        TZ_ENABLED='TRUE'
    fi
    log_msg "DEBUG" "TZ_ENABLED=\"$TZ_ENABLED\""

    log_msg "DEBUG" "setup_hlos: end"
}
