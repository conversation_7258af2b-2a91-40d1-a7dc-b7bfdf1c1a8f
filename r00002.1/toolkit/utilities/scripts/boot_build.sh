#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/logging.sh

function BOOT_validate_inputs() {
    log_msg "DEBUG" "BOOT_validate_inputs: start"

    if [ -z "$BUILD_DIR" ]; then
        log_msg "ERROR" "BUILD_DIR is not set"
        usage
    fi

    log_msg "DEBUG" "BOOT_validate_inputs: end"
}

function BOOT_setup_env() {
    log_msg "DEBUG" "BOOT_setup_env: start"

    BOOT_BUILD_MARKER="$BUILD_DIR/BOOT_build_complete"

    log_msg "DEBUG" "BOOT_setup_env: end"
}

function BOOT_build() {
    log_msg "DEBUG" "BOOT_build: start"

    if [ -e "$BOOT_BUILD_MARKER" ]; then
        log_msg "INFO" "BOOT Build already completed. Skipping build."
    else

        #Backup the current python link and create python3 link
        mv /usr/bin/python /usr/bin/python_backup
        mv /usr/bin/python3 /usr/bin/python3_backup
        ln -s /usr/local/bin/python3.8 /usr/bin/python
        ln -s /usr/local/bin/python3.8 /usr/bin/python3

        # Lemans
        tst cd "$BUILD_DIR/boot/SRC_LeMans_AU"
        #clean any prev built lemansau
        tst python3 boot_images/boot_tools/buildex.py --variant AU -r RELEASE -t LeMans,QcomToolsPkg --build_flags=cleanall
        tst python3 boot_images/boot_tools/buildex.py --variant AU -r RELEASE -t LeMans,QcomToolsPkg

        # Monaco
        tst cd "$BUILD_DIR/boot/SRC_Monaco_AU"
        #clean any prev built monacoau
        python3 boot_images/boot_tools/buildex.py --variant AU -r RELEASE -t Monaco,QcomToolsPkg --build_flags=cleanall
        tst python3 boot_images/boot_tools/buildex.py --variant AU -r RELEASE -t Monaco,QcomToolsPkg

        # Create the build file to indicate the build is complete
        tst touch "$BOOT_BUILD_MARKER"

        #Restore the original python link
        rm /usr/bin/python
        rm /usr/bin/python3
        mv /usr/bin/python_backup /usr/bin/python
        mv /usr/bin/python3_backup /usr/bin/python3
    fi

    log_msg "DEBUG" "BOOT_build: end"
}
