#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/git_utilities.sh
source ${UTILITIES_DIR}/logging.sh

function QNX_validate_inputs() {
    log_msg "DEBUG" "QNX_validate_inputs: start"

    if [ -z "$CHIPCODE_TAG" ]; then
        [ "$CHIPCODE_TAG" = "" ] && log_msg "ERROR" "Chipcode TAG is not set" && usage
    fi

    if [ -z "$BUILD_DIR" ]; then
        [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "Build directory is not set" && usage
    fi

    if [ -z "$QC_USER" ]; then
        [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [ -z "$QC_PASSWORD" ]; then
        [ "$QC_PASSWORD" = "" ] && log_msg "ERROR" "Chipcode Password is not set" && usage
    fi

    if [ -z "$MYQNX_USERNAME" ]; then
        [ "$MYQNX_USERNAME" = "" ] && log_msg "ERROR" "QNX Username is not set" && usage
    fi

    if [ -z "$MYQNX_PASSWORD" ]; then
        [ "$MYQNX_PASSWORD" = "" ] && log_msg "ERROR" "QNX Password is not set" && usage
    fi

    if [ -z "$QNX_SWC" ]; then
        [ "$QNX_SWC" = "" ] && log_msg "ERROR" "Path to QNX Software Center is not set" && usage
    fi

    if [ -z "$QNX_LICENSE" ]; then
        [ "$QNX_LICENSE" = "" ] && log_msg "ERROR" "QNX License is not set" && usage
    fi

    if [ -z "$BUILD_DIR" ]; then
        [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "Build directory is not set" && usage
    fi

    if [ -z "$PRODUCT" ]; then
        [ "$PRODUCT" = "" ] && log_msg "ERROR" "Product is not set" && usage
    fi

    if [ -z "$GITORG" ]; then
        [ "$GITORG" = "" ] && log_msg "ERROR" "GITORG is not set" && usage
    fi

    if [[ "$PRODUCT" == *"sa8797"* ]]; then
        echo "Skipping TARGET check as PRODUCT contains sa8797"
    else
        if [ -z "$TARGET" ]; then
            [ "$TARGET" = "" ] && log_msg "ERROR" "Target is not set" && usage
        fi
    fi

    if [ -z "$AU_TYPE" ]; then
        [ "$AU_TYPE" = "" ] && log_msg "ERROR" "AU Type is not set" && usage
    fi

    log_msg "DEBUG" "QNX_validate_inputs: end"
}

function QNX_setup_env() {
    log_msg "DEBUG" "QNX_setup_env: start"

    # Setting the umask is set to 0002 for preventing QNX random boot failures
    umask 0002

    QNX_validate_swc_access

    QNX_SYNC_REPO="${PRODUCT}_hlos_dev_qnx"
    QNX_SYNC_DIR="${BUILD_DIR}/${QNX_SYNC_REPO}"

    CHIPCODE_QNX_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${QNX_SYNC_REPO}.git"

    # TODO: Add support for QOS220
    QNX_TOOLCHAIN=${QNX_TOOLCHAIN:-QHS220}
    QNX_PREBUILT="prebuilt_${QNX_TOOLCHAIN}"
    QNX_BINS_DIR=$QNX_SYNC_DIR/apps/qnx_ap/qnx_bins

    if [ -n "$QNX_CUSTOM_PREBUILT_DIR" ]; then
        echo "QNX_PREBUILT_DIR is set to: $QNX_CUSTOM_PREBUILT_DIR"
        QNX_PREBUILT_DIR=$QNX_CUSTOM_PREBUILT_DIR
    else
        echo "QNX_CUSTOM_PREBUILT_DIR is not set "
        QNX_PREBUILT_DIR=$QNX_BINS_DIR/$QNX_PREBUILT
    fi

    QNX_BUILD_MARKER="$QNX_SYNC_DIR/QNX_build_complete"

    log_msg "DEBUG" "QNX_setup_env: end"
}

function QNX_validate_swc_access() {
    log_msg "DEBUG" "QNX_validate_environment: start"
    # Before starting build process, check following
    # 1 - Tools installed (QNX, git)
    # 2 - Licenses are valid for QNX

    [ -d "$QNX_LICENSE" ] || die "*** QNX license folder ($QNX_LICENSE) not found"

    # Find location of qnxsoftwarecenter tool
    log_msg "INFO" "Finding location of QNX SWC..."
    QNX_SWC_CLI="$QNX_SWC/qnxsoftwarecenter_clt"

    [ ! -f "$QNX_SWC_CLI" ] && die "QNX-Software-Center $QNX_SWC_CLI not found."

    log_msg "INFO" "Checking QNX License Keys"
    tst "$QNX_SWC_CLI -listLicenseKeys"

    log_msg "DEBUG" "QNX_validate_environment: end"
}

function QNX_setup_toolchain() {
    log_msg "INFO" "Removing cached qnx files..."
    rm -rf /root/.qnx/swupdate
    tst "$QNX_SWC_CLI -mirrorBaseline qnx710 -myqnx.user $MYQNX_USERNAME -myqnx.password $MYQNX_PASSWORD"
    tst "$QNX_SWC_CLI \
			-mirrorBaseline qnx710 \
			-profileProperty org.eclipse.equinox.p2.director.optimizationType=ultraconservative \
			-myqnx.user $MYQNX_USERNAME -myqnx.password $MYQNX_PASSWORD \
			-importAndInstall $QPKG_PLIST -env qnx.exp=true \
			-destination $QNX_PREBUILT_DIR ${QNX_SWC_URL:+-url $QNX_SWC_URL}"
    log_msg "DEBUG" "QNX_setup_toolchain: end"
}

function QNX_sync() {
    log_msg "DEBUG" "QNX_sync: start"

    # Fetch the build from Chipcode
    git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_QNX_REPO" "apps" "${QNX_SYNC_DIR}"

    log_msg "DEBUG" "QNX_sync: end"
}

function QNX_apply_patch() {
    log_msg "INFO" "Executing extra patches to BSP if specified."

    if [ "$QPKG_PLIST" != "" ]; then
        log_msg "INFO" "qpkg : $QPKG_PLIST"
        chmod a+w "$QPKG_PLIST"
        echo -e "\ncom.qnx.qnx710.target.qavf.shared_input/2.0.2.00371T202303021608L" >> "$QPKG_PLIST"
    else
        log_msg "WARN" ".plist file not found. Unable to patch VP package"
    fi

    local chipset
    chipset="$(echo "$AU_TYPE" | tr '[:upper:]' '[:lower:]')"

    tst "cp -f ${QNX_SYNC_DIR}/common/sectoolsv2/${TARGET}${chipset}_security_profile.xml ${QNX_SYNC_DIR}/apps/qnx_ap/target/sectools/security/${chipset}_boot_security_profile.xml"

    # Fix For Infereno compilation issues
    NLOHMANN_DIR="$QNX_SYNC_DIR/apps/qnx_ap/AMSS/inferno/utils/nlohmann"
    [ -d "$NLOHMANN_DIR" ] || tst "mkdir -p $NLOHMANN_DIR" && tst "cd $NLOHMANN_DIR"
    tst "wget -q https://github.com/nlohmann/json/releases/download/v3.7.3/json.hpp"
    # tst "cp -raf $1/apps/qnx_ap/AMSS/ride_cloud/include/* $1/apps/qnx_ap/AMSS/inferno/src/inputEngines/"
    tst "cp -raf ${QNX_SYNC_DIR}/apps/qnx_ap/AMSS/ride_cloud/DDS/include/* ${QNX_SYNC_DIR}/apps/qnx_ap/AMSS/inferno/src/inputEngines/"

    log_msg "DEBUG" "QNX_patch: end"
}

# Use cached prebuilts (if available)
function QNX_build() {
    log_msg "DEBUG" "QNX_build: start"

    if [ -e "$QNX_BUILD_MARKER" ]; then
        log_msg "INFO" "QNX make process completed, skipping QNX build ..."
    else
        log_msg "INFO" "Starting Build..."

        tst "cd $QNX_SYNC_DIR"
        local cached_prebuilt
        cached_prebuilt=$(find . -name ${QNX_PREBUILT}.tar.* -print -quit 2> /dev/null)

        if [ "$cached_prebuilt" != "" ]; then
            log_msg "INFO" "Using cached QNX bins/prebuilt $cached_prebuilt"
            tst "tar -C $QNX_BINS_DIR -x -a -f $cached_prebuilt"
        else
            # Import QPKG
            log_msg "INFO" "Creating QNX via QPKG"
            # TODO: Add support for using multiple plist files
            if [ -n "$QPKG_CUSTOM_PLIST" ]; then
                echo "QPKG_CUSTOM_PLIST is set to: $QPKG_CUSTOM_PLIST"
                QPKG_PLIST=$QPKG_CUSTOM_PLIST

            else
                echo "QPKG_CUSTOM_PLIST is not set or is empty"
                # If SDP plist is present, it will not check for QHS. This helps reduce multiple plist availability issue.
                QPKG_PLIST=$(find "$QNX_SYNC_DIR/apps/qnx_ap/qnx_bins" -type f -name "*.plist" | grep -E "SDP" || grep -E "QHS" | grep -v "_vp")
            fi

            [ "$QPKG_PLIST" = "" ] && die ".plist file not found."

            if [[ "$PRODUCT" == *"5-7-7-0"* || "$PRODUCT" == *"4-8-9-0"* ]]; then
                echo "Skipping QNX_apply_patch as PRODUCT name contains 5-7-7-0 or 4-8-9-0"
            else
                QNX_apply_patch
            fi

            if [ -d "$QNX_PREBUILT_DIR" ]; then
                if [ "$(ls -A $QNX_PREBUILT_DIR)" ]; then
                    log_msg "INFO" "QNX prebuilt directory is not empty, proceeding with build process"
                else
                    log_msg "INFO" "QNX prebuilt directory is empty, continuing QNX build process..."
                    QNX_setup_toolchain
                fi
            else # If directory does not exist, make directory and proceed
                tst "mkdir -p $QNX_PREBUILT_DIR"
                # Fetch the build from Chipcode
                QNX_setup_toolchain
            fi
        fi

        # Copy QNX license into the target folder (used by toolchain docker)
        [ -d "$QNX_LICENSE" ] || die "*** QNX license folder ($QNX_LICENSE) not found"
        tst "cp -r $QNX_LICENSE $QNX_BINS_DIR"

        if [ -d "$QNX_PREBUILT_DIR" ]; then
            if [ "$(ls -A $QNX_PREBUILT_DIR)" ]; then
                # Condition if prebuilt_QOS222/QNX generation successful
                tst "cd $QNX_SYNC_DIR/apps/qnx_ap"
                # TODO: Add support for QOS
                tst "source setenv_${QNX_TOOLCHAIN,,}.sh --external $QNX_PREBUILT_DIR"
                log_cmd "export SECTOOLS=$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools"
                export SECTOOLS=$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools
                log_cmd "export SECTOOLS_V2_ROOT=$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools"
                export SECTOOLS_V2_ROOT=$QNX_SYNC_DIR/common/sectoolsv2/ext/Linux/sectools
                tst make clean
                tst make
                tst "touch $QNX_BUILD_MARKER"
            else
                die "$QNX_PREBUILT_DIR is empty, QNX compiliation failed! Check previous errors, resolve and restart build process."
            fi
        fi
    fi
    log_msg "DEBUG" "QNX_build: end"
}
