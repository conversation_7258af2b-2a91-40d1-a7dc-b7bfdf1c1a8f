#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source "${UTILITIES_DIR}/logging.sh"

function git_setup() {
    log_msg "DEBUG" "git_setup: start"

    log_msg "INFO" "GIT Organization = $GITORG"
    log_msg "INFO" "Customer ID = $CUSTOMER_ID"

    [ -e ~/.gitconfig ] && rm ~/.gitconfig
    #rm ~/.gitconfig 2> /dev/null
    tst "git config --global credential.helper 'cache --timeout 200'"
    tst "git config --global credential.helper store"
    # Chipcode requires redirects
    tst "git config --global http.'https://qpm-git.qualcomm.com'.followRedirects \"true\""
    tst "git config --global http.'https://chipmaster2.qti.qualcomm.com'.followRedirects \"true\""
    tst "git config --global user.name $QC_USER"
    trc "git config --global user.email $MAIL_ID"
    tst "git config --global color.ui false"
    tst "git config --global http.postBuffer 524288000"

    if [ -n "$GIT_PROXY" ]; then
        log_msg "INFO" "GIT PROXY DETECTED"
        tst git config --global http.proxy "$GIT_PROXY"
    else
        trc 'git config --global --unset http.proxy'
    fi

    # defining global REPO_BRANCH for common branch usage
    REPO_BRANCH='qc-stable'

    log_msg "DEBUG" "git_setup: end"
}

function set_netrc() {
    log_msg "DEBUG" "set_netrc: start"
    cat >> ~/.netrc << EOF
machine $1
login $2
password $3

EOF
    chmod 600 ~/.netrc
    log_msg "DEBUG" "set_netrc: end"
}

function git_clone_chipcode() {
    log_msg "DEBUG" "git_clone_chipcode: start"
    # Check if all arguments are provided
    if [ $# -lt 4 ]; then
        log_cmd "INFO" "Usage: git_clone_chipcode <tag/branch> <repository> <check_folder> <clone_dir>"
        die "All mandatory parameters are not passed!"
    fi

    local branch="$1"
    local repository="$2"
    local check_folder="$3"
    local clone_dir="$4"

    if [ -d "$clone_dir" ]; then
        if [ -d "$clone_dir/$check_folder" ]; then
            log_msg "INFO" "Build directory exists & contains $check_folder folder, proceeding with build process"
        else
            log_msg "INFO" "Directory is present but $check_folder folder is absent, proceeding with git clone..."
            # delete folder since git clone requires empty folder
            tst "rm -rf $clone_dir"
            git_clone "$branch" "$repository" "${clone_dir%/}"
        fi
    else
        tst "mkdir -p $clone_dir"
        git_clone "$branch" "$repository" "${clone_dir%/}"
    fi

    log_msg "DEBUG" "git_clone_chipcode: end"
}

function git_clone() {
    log_msg "DEBUG" "git_clone: start"
    # Check if all arguments are provided
    if [ $# -lt 3 ]; then
        log_cmd "INFO" "Usage: git_clone <tag/branch> <repository> <clone_dir>"
        die "All mandatory parameters are not passed!"
    fi

    if [ -z "$QC_USER" ]; then
        [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [ -z "$QC_USER" ]; then
        [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [ -z "$CHIPCODE" ]; then
        [ "$CHIPCODE" = "" ] && log_msg "ERROR" "Chipcode URL is not set" && usage
    fi

    trc "rm -f ~/.netrc"
    # Used for accessing repositories on ChipCode
    set_netrc 'chipmaster2.qti.qualcomm.com' "$QC_USER" "$QC_PASSWORD"
    set_netrc "$CHIPCODE" "$QC_USER" "$QC_PASSWORD"
    set_netrc 'code.qualcomm.com' "$QC_USER" "$QC_PASSWORD"
    # Used for git clone of repo tool
    if [ -z "$CODELINARO_ACCESS_TOKEN" ]; then
        set_netrc 'git.codelinaro.org' "$QC_USER" "$CODELINARO_ACCESS_TOKEN"
    fi
    tst "git clone --verbose --depth 1 --branch $1 $2 $3"
    tst "rm -f ~/.netrc"

    log_msg "DEBUG" "git_clone_chipcode: end"
}

function activate_venv() {
    log_msg "INFO" "Activating venv"
    GREASE_UTILITIES_VENV_DIR="$BUILD_DIR/BUILD_ROOT_DIR/grease_utilities_venv"
    python2 -m virtualenv $GREASE_UTILITIES_VENV_DIR
    source $GREASE_UTILITIES_VENV_DIR/bin/activate
}

function deactivate_venv() {
    log_msg "INFO" "Deactivating virtual env"
    deactivate
}

function repo_install() {
    log_msg "INFO" "Checking for existing repo..."
    if ! which repo > /dev/null 2>&1; then
        log_msg "INFO" "Could not find repo installed. Installing repo..."
        tst "pushd $BUILD_DIR"
        [ ! -d repo ] || rm -rf repo
        git_clone "${REPO_BRANCH}" "${CODELINARO_REPO}" "repo"
        tst "cp repo/repo /usr/bin/repo"
        tst "chmod a+rx /usr/bin/repo"
        tst "rm -rf repo"
        tst popd
    else
        log_msg "INFO" "repo is already installed"
    fi
}

function repo_sync() {
    log_msg "DEBUG" "repo_sync: start"

    if [ $# -lt 3 ]; then
        log_cmd "INFO" "Usage: repo_sync <path> <manifest_url> <manifest_branch> <manifest_xml> [repo_tool_branch]"
        die "All mandatory parameters are not available!"
    fi

    local path="$1"
    local manifest_url="$2"
    local manifest_branch="$3"
    local manifest_xml="$4"
    local repo_tool_branch="$5"

    # Set default values if not provided

    if [ -z "$repo_tool_branch" ]; then
        repo_tool_branch="${REPO_BRANCH}"
    fi

    activate_venv

    repo_install

    tst pushd "$path"
    tst export GIT_SSH_VARIANT=ssh
    tst "repo init -u $manifest_url -b $manifest_branch -m $manifest_xml --repo-url=$CODELINARO_REPO --repo-branch=$repo_tool_branch --no-clone-bundle"
    tst "repo sync -j$(nproc) -c --no-tags --no-clone-bundle"

    deactivate_venv

    tst popd
    log_msg "DEBUG" "repo_sync: end"
}

function setup_grease_utils() {
    log_msg "DEBUG" "setup_grease_utils: start"

    # Instructions to download sync_snap_v2.sh for chipcode history
    GREASE_UTILITIES_DIR="$BUILD_DIR/BUILD_ROOT_DIR/grease_utilities"
    local grease_utilities_clone_complete="$GREASE_UTILITIES_DIR/grease_utilities_clone_complete"

    if [ -e "$grease_utilities_clone_complete" ]; then
        log_msg "INFO" "GREASE_UTILITIES clone previously completed, skipping..."
    else
        tst "cd $BUILD_DIR"
        [ -d BUILD_ROOT_DIR ] || tst "mkdir BUILD_ROOT_DIR" && tst "cd BUILD_ROOT_DIR"
        [ ! -d grease_utilities ] || tst "rm -rf grease_utilities"

        git_clone "$GREASE_UTILITIES" "$CHIPCODE_UTILITIES" grease_utilities

        tst "touch $grease_utilities_clone_complete"
    fi

    log_msg "DEBUG" "setup_grease_utils: end"
}

function sync_snap_v2() {

    activate_venv

    log_msg "DEBUG" "sync_snap_v2: start"

    if [ $# -lt 7 ]; then
        log_cmd "INFO" "Usage: sync_snap_v2 <image_name> <image_type> <tree_type> <au_tag> <manifest_repo> <history_repo> <sync_dir>"
        die "All mandatory parameters are not passed!"
    fi

    local image_name="$1"
    local image_type="$2"
    local tree_type="$3"
    local au_tag="$4"
    local manifest_repo="$5"
    local history_repo="$6"
    local sync_dir="$7"

    repo_install

    local sync_successful_message='repo sync successful'
    local sync_log_file="${sync_dir}/${image_name}_sync_snap_v2.log"

    set_netrc 'chipmaster2.qti.qualcomm.com' "$QC_USER" "$QC_PASSWORD"
    if [ -z "$CODELINARO_ACCESS_TOKEN" ]; then
        set_netrc 'git.codelinaro.org' "$QC_USER" "$CODELINARO_ACCESS_TOKEN"
    fi
    set_netrc "$CHIPCODE" "$QC_USER" "$QC_PASSWORD"
    set_netrc 'code.qualcomm.com' "$QC_USER" "$QC_PASSWORD"
    log_cmd "export GIT_SSH_VARIANT=ssh"
    export GIT_SSH_VARIANT=ssh

    # error message check
    repo_encoding_error_msg='TypeError: a bytes-like object is required'
    # delete previously sync since it interferes with current sync
    [[ -d $sync_dir ]] && tst "rm -rf $sync_dir"

    tst "mkdir -p $sync_dir"

    log_cmd "${GREASE_UTILITIES_DIR}/sync_snap_v2.sh --shallow_clone=true --image_type=${image_type} --tree_type=${tree_type} --prop_opt=chipcode_hf --${image_name}_au_tag=${au_tag} --workspace_path=${sync_dir} --common_oss_url=${CODELINARO} --${image_name}_oss_manifest_git=${manifest_repo} --${image_name}_chipcode_hf_server=${CHIPCODE} --${image_name}_chipcode_hf_manifest_git=${history_repo} --${image_name}_chipcode_hf_manifest_branch=$CUSTOMER_ID-SRC_History --repo_url=$CODELINARO_REPO --repo_branch=${REPO_BRANCH}"

    bash "${GREASE_UTILITIES_DIR}/sync_snap_v2.sh" "--shallow_clone=true" "--image_type=${image_type}" "--tree_type=${tree_type}" "--prop_opt=chipcode_hf" "--${image_name}_au_tag=${au_tag}" "--workspace_path=${sync_dir}" "--common_oss_url=${CODELINARO}" "--${image_name}_oss_manifest_git=${manifest_repo}" "--${image_name}_chipcode_hf_server=${CHIPCODE}" "--${image_name}_chipcode_hf_manifest_git=${history_repo}" "--${image_name}_chipcode_hf_manifest_branch=$CUSTOMER_ID-SRC_History" "--repo_url=$CODELINARO_REPO" "--repo_branch=${REPO_BRANCH}" 2>&1 | tee "$sync_log_file"

    if grep "$repo_encoding_error_msg" "${sync_log_file}"; then
        sed -i "s/os.write(fd, p.stdout)/os.write(fd, p.stdout.encode('utf-8'))/g" ${sync_dir}/.repo/repo/project.py
        bash "${GREASE_UTILITIES_DIR}/sync_snap_v2.sh" "--shallow_clone=true" "--image_type=${image_type}" "--tree_type=${tree_type}" "--prop_opt=chipcode_hf" "--${image_name}_au_tag=${au_tag}" "--workspace_path=${sync_dir}" "--common_oss_url=${CODELINARO}" "--${image_name}_oss_manifest_git=${manifest_repo}" "--${image_name}_chipcode_hf_server=${CHIPCODE}" "--${image_name}_chipcode_hf_manifest_git=${history_repo}" "--${image_name}_chipcode_hf_manifest_branch=$CUSTOMER_ID-SRC_History" "--repo_url=$CODELINARO_REPO" "--repo_branch=${REPO_BRANCH}" 2>&1 | tee -a "$sync_log_file"

    fi

    tst rm ~/.netrc

    grep "$sync_successful_message" "${sync_log_file}" || die "Sync of $au_tag failed"

    log_msg "DEBUG" "sync_snap_v2: end"

    deactivate_venv

}
