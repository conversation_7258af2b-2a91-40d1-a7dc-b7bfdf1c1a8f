#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/logging.sh

function DSP_validate_inputs() {
    log_msg "DEBUG" "DSP_validate_inputs: start"

    if [ -z "$BUILD_DIR" ]; then
        log_msg "ERROR" "BUILD_DIR is not set"
        usage
    fi

    if [ -z "$CHIPCODE_TAG" ]; then
        log_msg "ERROR" "CHIPCODE_TAG is not set"
        usage
    fi

    log_msg "DEBUG" "DSP_validate_inputs: end"
}

function DSP_setup_env() {
    log_msg "DEBUG" "DSP_setup_env: start"

    DSP_BUILD_MARKER="$BUILD_DIR/DSP_build_complete"

    echo "hexagon root is $HEXAGON_ROOT"

    if [ -z "$HEXAGON_ROOT" ]; then
        HEXAGON_ROOT=/pkg/qct/software/hexagon/releases/tools
    fi

    export HEXAGON_ROOT=$HEXAGON_ROOT

    log_msg "DEBUG" "DSP_setup_env: end"
}

function DSP_build() {
    log_msg "DEBUG" "DSP_build: start"

    if [ -e "$DSP_BUILD_MARKER" ]; then
        log_msg "INFO" "DSP Build already completed. Skipping build."
    else

        #Backup the current python link and create python3 link
        mv /usr/bin/python /usr/bin/python_backup
        mv /usr/bin/python3 /usr/bin/python3_backup
        ln -s /usr/local/bin/python3.11 /usr/bin/python
        ln -s /usr/local/bin/python3.11 /usr/bin/python3

        pip3 install ruamel.yaml==0.17.21
        pip3 install dtschema==2021.10
        pip3 install jsonschema==4.17.1

        tst export SECTOOLS=$BUILD_DIR/common/sectoolsv2/ext/Linux/sectools
        tst export DTC_PATH=/usr/bin/

        tst cd "$BUILD_DIR/dsp/dsp_proc/build/ms/"

        # Lemans
        # Clean any previously built CDSP0 and build CDSP0"
        tst python3 ./build_variant.py lemans.fusa_cdsp0.prod --clean
        tst python3 ./build_variant.py lemans.fusa_cdsp0.prod
        # Clean any previously built CDSP1 and build CDSP1"
        tst python3 ./build_variant.py lemans.fusa_cdsp1.prod --clean
        tst python3 ./build_variant.py lemans.fusa_cdsp1.prod

        tst cd "$BUILD_DIR/dsp_ivi/dsp_proc/build/ms/"

        # Clean any previously built adsp and build adsp"
        tst python3 ./build_variant.py lemans.adsp.prod --clean
        tst python3 ./build_variant.py lemans.adsp.prod
        # Clean any previously built gpdsp0 and build gpdsp0"
        tst python3 ./build_variant.py lemans.gpdsp0.prod --clean
        tst python3 ./build_variant.py lemans.gpdsp0.prod
        # Clean any previously built gpdsp1 and build gpdsp1"
        tst python3 ./build_variant.py lemans.gpdsp1.prod --clean
        tst python3 ./build_variant.py lemans.gpdsp1.prod

        # Monaco
        # Clean any previously built CDSP0 and build CDSP0"
        tst python3 ./build_variant.py monaco.cdsp0.prod --clean
        tst python3 ./build_variant.py monaco.cdsp0.prod
        # Create the build file to indicate the build is complete
        tst touch "$DSP_BUILD_MARKER"

        #Restore the original python link
        rm /usr/bin/python
        rm /usr/bin/python3
        mv /usr/bin/python_backup /usr/bin/python
        mv /usr/bin/python3_backup /usr/bin/python3
    fi

    log_msg "DEBUG" "DSP_build: end"
}
