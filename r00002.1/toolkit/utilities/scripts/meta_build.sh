#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/logging.sh

function Meta_validate_inputs() {
    if [[ "${META_ENABLED,,}" == "true" ]]; then
        log_msg "DEBUG" "Meta_validate_inputs: start"

        if [ -z "$CHIPCODE_TAG" ]; then
            [ "$CHIPCODE_TAG" = "" ] && log_msg "ERROR" "Chipcode TAG is not set" && usage
        fi

        if [ -z "$PRODUCT" ]; then
            [ "$PRODUCT" = "" ] && log_msg "ERROR" "PRODUCT is not set" && usage
        fi

        if [ -z "$GITORG" ]; then
            [ "$GITORG" = "" ] && log_msg "ERROR" "GITORG is not set" && usage
        fi

        if [ -z "$BUILD_DIR" ]; then
            [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "Build directory is not set" && usage
        fi

        if [ -z "$QC_USER" ]; then
            [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
        fi

        if [ -z "$QC_PASSWORD" ]; then
            [ "$QC_PASSWORD" = "" ] && log_msg "ERROR" "Chipcode Password is not set" && usage
        fi

        if [ -z "$PRODUCT" ]; then
            [ "$PRODUCT" = "" ] && log_msg "ERROR" "Product is not set" && usage
        fi

        if [ -n "$FLAVOR" ]; then
            [ "$FLAVOR" = "" ] && log_msg "ERROR" "Flavor for generating META is not set" && usage
        else
            log_msg "INFO" "FLAVOR is not required for this build"
        fi

        if [ -n "$VARIANT" ]; then
            [ "$VARIANT" = "" ] && log_msg "ERROR" "Variant for generating META is not set" && usage
        else
            log_msg "INFO" "VARIANT is not required for this build"
        fi

        log_msg "DEBUG" "Meta_validate_inputs: end"
    fi
}

function Meta_setup_env() {
    log_msg "DEBUG" "Meta_setup_env: start"

    AMSS_STD_OEM_SYNC_REPO="${PRODUCT}_amss_standard_oem"
    CHIPCODE_AMSS_STD_OEM_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${AMSS_STD_OEM_SYNC_REPO}.git"
    TEST_DEVICE_AUTOSAR_SYNC_REPO="${PRODUCT}_test_device_autosar"
    CHIPCODE_TEST_DEVICE_AUTOSAR_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${TEST_DEVICE_AUTOSAR_SYNC_REPO}.git"
    TEST_DEVICE_SYNC_REPO="${PRODUCT}_test_device"
    CHIPCODE_TEST_DEVICE_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${TEST_DEVICE_SYNC_REPO}.git"
    AUTOSAR_DIR="${BUILD_DIR}/${TEST_DEVICE_AUTOSAR_SYNC_REPO}"
    META_BUILD_MARKER="${BUILD_DIR}/Meta_build_complete"

    log_msg "DEBUG" "Meta_setup_env: end"
}

function Meta_sync() {
    if [[ "${META_ENABLED,,}" == "true" ]]; then
        log_msg "DEBUG" "Meta_sync: start"

        #setup the top level dir under BUILD_DIR
        if [ -z "${ENABLED_NHLOS_BUILDS}" ]; then
            log_msg "INFO" "ENABLED_NHLOS_BUILDS is not defined"
            log_msg "INFO" "Downloading the Test Device distro..."

            git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_TEST_DEVICE_REPO" "common" "${BUILD_DIR}"

        else
            log_msg "INFO" "ENABLED_NHLOS_BUILDS is defined"
            log_msg "INFO" "Downloading the AMSS STANDARD OEM distro..."

            git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_AMSS_STD_OEM_REPO" "common" "${BUILD_DIR}"
        fi

        # tst "cd $BUILD_DIR"

        # Download the autosar test dev distro if enabled
        if [ -n "$TEST_DEVICE_AUTOSAR_ENABLED" ]; then
            log_msg "INFO" "Downloading the autosar test distro..."

            tst "cd $BUILD_DIR"

            git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_TEST_DEVICE_AUTOSAR_REPO" "common" "${AUTOSAR_DIR}"
        fi

        log_msg "DEBUG" "Meta_sync: end"
    fi
}

function Meta_build() {

    if [[ "${META_ENABLED,,}" == "true" ]]; then
        if [[ -e "${META_BUILD_MARKER}" ]]; then
            log_msg "INFO" "Meta build completed previously. Skipping build."
        else
            log_msg "DEBUG" "Meta_build: start"
            log_msg "INFO" "Setting SYMLINKS for NON HLOS directory..."
            tst "cd $BUILD_DIR"

            # Adding Autosar links
            if [ -n "$TEST_DEVICE_AUTOSAR_ENABLED" ]; then
                tst "rm -rf sail_autosar"
                tst "ln -sf ${AUTOSAR_DIR}/sail_autosar sail_autosar"
            fi

            # Adding HGY links
            if [ -n "$HGY_ENABLED" ]; then
                # HGY symlinks
                tst "rm -rf apps"
                tst "ln -sf ${HGY_SYNC_REPO}/Auto/apps ."
                mkdir -p "${HGY_SYNC_DIR}/Auto/apps/apps_proc/poky/build/tmp-glibc" || die "mkdir -p ${HGY_BUILD_DIR}/apps/apps_proc/poky/build/tmp-glibc failed!"
                pushd "${HGY_SYNC_DIR}/Auto/apps/apps_proc/poky/build/tmp-glibc" || die "pushd ${HGY_BUILD_DIR}/apps/apps_proc/poky/build/tmp-glibc failed!"
                ln -sf ../../../build-auto/tmp-glibc/deploy/ . || die "ln -sf ../../../build-auto/tmp-glibc/deploy/ . failed!"
                popd || die "popd failed!"

                # HGY patch
                hgy_images_dir="${HGY_SYNC_DIR}/Auto/apps/apps_proc/poky/build/tmp-glibc/deploy/images"
                if [ -d "$hgy_images_dir/sa8797" ]; then
                    mv "$hgy_images_dir/sa8797" "$hgy_images_dir/sa8797-automotive" || die "mv $hgy_images_dir/sa8797 $hgy_images_dir/sa8797-automotive failed!"
                fi

                if [[ "$PRODUCT" == *sa8797p* ]]; then
                    # contents.xml patch
                    sed -i 's/<image_dir>cpucp<\/image_dir>/<image_dir>cpucp_proc<\/image_dir>/' $BUILD_DIR/contents.xml
                    sed -i 's/<file_path\ flavor="pvm_gvm_ivi">cpucp\/cpucp_proc\/nord\/cpucp<\/file_path>/<file_path flavor="pvm_gvm_ivi">cpucp_proc\/nord\/cpucp<\/file_path>/g' $BUILD_DIR/contents.xml
                fi

            fi

            if [[ "$PRODUCT" == *sa8797p* ]]; then
                # contents.xml patch
                sed -i 's/<image_dir>cpucp<\/image_dir>/<image_dir>cpucp_proc<\/image_dir>/' $BUILD_DIR/contents.xml
                sed -i 's/<file_path\ flavor="pvm_gvm_ivi">cpucp\/cpucp_proc\/nord\/cpucp<\/file_path>/<file_path flavor="pvm_gvm_ivi">cpucp_proc\/nord\/cpucp<\/file_path>/g' $BUILD_DIR/contents.xml
            fi

            # Adding QNX links
            if [ -n "$QNX_ENABLED" ]; then
                tst "rm -rf apps"
                tst "ln -sf ${QNX_SYNC_REPO}/apps apps"
                tst "rm -rf qxa_wp"
                tst "ln -sf ${QNX_SYNC_REPO}/apps qxa_wp"
            fi

            # Adding LA links
            if [ -n "$LA_ENABLED" ]; then
                tst "mkdir -p lagvm/LINUX/android/"
                tst "mkdir -p lagvm_qssi/LINUX/android"

                if [ -z "$HGY_ENABLED" ]; then
                    tst "rm -rf apps_kernel"
                    tst "ln -sf ${LA_SYNC_REPO}/apps_kernel apps_kernel"
                fi

                tst "pushd lagvm/LINUX/android/"
                tst "rm -rf out"
                tst "ln -sf ../../../${LA_SYNC_REPO}/VENDOR_BUILD_DIR/out out"
                tst "popd"

                tst "pushd lagvm_qssi/LINUX/android"
                tst "rm -rf out"
                tst "ln -sf ../../../${LA_SYNC_REPO}/SYSTEM_BUILD_DIR/out out"
                tst "popd"

                if [[ -n "${AU_LINUX_LA_QSSI15}" ]]; then
                    tst "mkdir -p lagvm_qssi14/LINUX/android"
                    tst "mkdir -p lagvm_qssi15/LINUX/android"

                    tst "pushd lagvm_qssi14/LINUX/android"
                    trc "rm -rf out"
                    tst "ln -sf ../../../${LA_SYNC_REPO}/SYSTEM_BUILD_DIR/out out"
                    tst "popd"

                    tst "pushd lagvm_qssi15/LINUX/android"
                    trc "rm -rf out"
                    tst "ln -sf ../../../${LA_SYNC_REPO}/SYSTEM_BUILD_DIR/out out"
                    tst "popd"
                fi
            fi

            # Adding LV
            if [ -n "$LV_ENABLED" ]; then
                tst "rm -rf lvgvm"
                tst "ln -sf ${LV_SYNC_REPO}/lvgvm lvgvm"
            fi

            # Meta Generation
            log_msg "INFO" "@@@@ Building META Binaries @@@@@"
            tst "cd $BUILD_DIR/common/build"

            if [ -n "$FLAVOR" ]; then
                log_msg "INFO" "flavor specified, building for flavor $FLAVOR"
                if [ -n "$VARIANT" ]; then
                    log_msg "INFO" "variant specified, building for variant $VARIANT"
                    if [ -n "$ST" ]; then
                        tst "python build.py --flavors=$FLAVOR --st=$ST --variant=$VARIANT"
                    else
                        tst "python build.py --flavors=$FLAVOR --variant=$VARIANT"
                    fi
                else
                    log_msg "INFO" "variant not specified, building all variants"
                    tst "python build.py --flavors=$FLAVOR"
                fi
            else
                log_msg "INFO" "flavor not specified, building all flavors"
                tst "python build.py"
            fi

            log_msg "INFO" "@@@@@@  Build generation is successful! @@@@@@"
            tst "touch ${META_BUILD_MARKER}"
        fi
    fi
}
