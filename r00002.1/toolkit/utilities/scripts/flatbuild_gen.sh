#!/bin/bash -e

# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

# Exit with error
die() {
    echo $1
    exit 1
}

usage() {
    echo "$0 --buildpath [BUILD_DIRECTORY]"
    exit 1
}

# Get the directory of the script
script_directory="$(dirname "$(dirname "$(dirname "$(realpath "$0")")")")"
 # Print the script's directory to the terminal
echo "The script is located in: $script_directory"

[ -r $script_directory/bsp_config ] || die "Config file ($script_directory/bsp_config) not found."
source $script_directory/bsp_config || echo "source of confifg file : bsp_config failed."

while [[ $# -gt 0 ]]; do
    key="$1"

    case $key in
        --help)
            usage
            shift
            ;;
        --buildpath)
            build_dir=$2
            shift
            shift
            ;;
        *)
            POSITIONAL+=("$1")
            shift
            ;;
    esac
done

pcat_flat_flag=false

if [ -z "$build_dir" ]; then
    [ "$build_dir" = "" ] && echo "Build Path not entered" && usage
fi

check_quts_service() {
    # Check if the process is running
    if pgrep -x "QUTSService" > /dev/null; then
        echo "The process QUTSService is already running."
    else
        # If the process is not running, start the application
        echo "Starting QUTSService..."
        /opt/qcom/QUTS/bin/QUTSService &
        echo "QUTSService has been started."
    fi
}

pcat_command() {
    echo $1
    start_time=$(date +%s)
    # Killing existing pkill without creating an error using true
    pkill -x 'QUTS' || true > /dev/null
    $1 |& while IFS= read -r line; do
        echo $line
        if echo "$line" | grep -q "ERROR"; then
            die "Error detected: $line"
        fi
    done

    end_time=$(date +%s)
    echo "======> Process Successful - $(expr $end_time - $start_time) s"
    echo "======> Waiting for platform to be ready for next stage... <======"
}

echo "======> Creating Flat Build via PCAT... this may take 3-5 Minutes <======"
echo "Flattening SAIL..."
[ -d $build_dir/FLAT_BUILD/SAIL ] || mkdir -p $build_dir/FLAT_BUILD/SAIL
pcat_command "pcat -flatten -build $build_dir/contents.xml -memorytype spinor -flavor $SAIL_TYPE -OUT $build_dir/FLAT_BUILD/SAIL"

echo "Flattening Main Domain..."
[ -d $build_dir/FLAT_BUILD/MD ] || mkdir -p $build_dir/FLAT_BUILD/MD
pcat_command "pcat -flatten -build $build_dir/contents.xml -memorytype $MEM_TYPE -flavor $PRODUCT_FLAVOR -OUT $build_dir/FLAT_BUILD/MD"

pcat_flat_flag=true

if [ "$pcat_flat_flag" = true ]; then
    # Controller/Console looks for this to determine success.
    echo "Flat build files located in: $build_dir/FLAT_BUILD"
    echo "Flat build generation is successful"
else
    die "Flat build generation unsuccessful, check logs..."
fi
