#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source "${UTILITIES_DIR}/logging.sh"

function NHLOS_validate_inputs() {
    log_msg "DEBUG" "NHLOS_validate_inputs: start"

    if [ -n "$AOP_ENABLED" ]; then
        source "${UTILITIES_DIR}/aop_build.sh"
        call_function AOP_validate_inputs
    fi

    if [ -n "$BOOT_ENABLED" ]; then
        source "${UTILITIES_DIR}/boot_build.sh"
        call_function BOOT_validate_inputs
    fi

    if [ -n "$DSP_ENABLED" ]; then
        source "${UTILITIES_DIR}/dsp_build.sh"
        call_function DSP_validate_inputs
    fi

    if [ -n "$TZ_ENABLED" ]; then
        source "${UTILITIES_DIR}/tz_build.sh"
        call_function TZ_validate_inputs
    fi

    log_msg "DEBUG" "HLOS_validate_inputs: end"
}

function NHLOS_setup_env() {
    log_msg "DEBUG" "NHLOS_setup_env: start"
    if [ -n "$AOP_ENABLED" ]; then
        call_function AOP_setup_env
    fi

    if [ -n "$BOOT_ENABLED" ]; then
        call_function BOOT_setup_env
    fi

    if [ -n "$DSP_ENABLED" ]; then
        call_function DSP_setup_env
    fi

    if [ -n "$TZ_ENABLED" ]; then
        call_function TZ_setup_env
    fi
    log_msg "DEBUG" "HNHLOS_setup_env: end"
}

function NHLOS_sync_build() {
    log_msg "DEBUG" "NHLOS_sync_build: start"
    # =================== Building AOP NOW ============================================================
    if [ -n "$AOP_ENABLED" ]; then
        call_function AOP_build
    fi

    # =================== Building BOOT NOW =============================================================
    if [ -n "$BOOT_ENABLED" ]; then
        call_function BOOT_build
    fi

    # =================== Building DSP NOW =============================================================
    if [ -n "$DSP_ENABLED" ]; then
        call_function DSP_build
    fi

    # =================== Building TZ NOW =============================================================
    if [ -n "$TZ_ENABLED" ]; then
        call_function TZ_build
    fi
    log_msg "DEBUG" "NHLOS_sync_build: end"
}
