#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/logging.sh

function AOP_validate_inputs() {
    log_msg "DEBUG" "AOP_validate_inputs: start"

    if [ -z "$BUILD_DIR" ]; then
        [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "BUILD_DIR is not set" && usage
    fi

    log_msg "DEBUG" "AOP_validate_inputs: end"
}

function AOP_setup_env() {
    log_msg "DEBUG" "AOP_setup_env: start"

    AOP_BUILD_MARKER="$BUILD_DIR/AOP_build_complete"

    log_msg "DEBUG" "AOP_setup_env: end"
}

function AOP_build() {
    log_msg "DEBUG" "AOP_build: start"

    if [ -e "$AOP_BUILD_MARKER" ]; then
        log_msg "INFO" "AOP Build already completed. Skipping build."

    else
        tst cd "$BUILD_DIR/aop/aop_proc/build"
        #Backup the current python link and create python2 link
        mv /usr/bin/python /usr/bin/python_backup
        ln -s /usr/bin/python2 /usr/bin/python

        # Lemans
        # Clean any previously built lemansau
        tst ./build_lemansau.sh -c
        tst ./build_lemansau.sh

        # Monaco
        tst ./build_monaco.sh

        # Create the build file to indicate the build is complete
        tst touch "$AOP_BUILD_MARKER"

        #Restore the original python link
        rm /usr/bin/python
        mv /usr/bin/python_backup /usr/bin/python
    fi

    log_msg "DEBUG" "AOP_build: start"
}
