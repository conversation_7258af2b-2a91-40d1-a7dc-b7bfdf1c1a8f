#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source ${UTILITIES_DIR}/utilities.sh
source ${UTILITIES_DIR}/git_utilities.sh
source ${UTILITIES_DIR}/logging.sh

function LA_validate_inputs() {
    log_msg "DEBUG" "LA_validate_inputs: start"

    if [[ -z "${CHIPCODE_TAG}" ]]; then
        [[ "${CHIPCODE_TAG}" = "" ]] && log_msg "ERROR" "Chicode Tag is not set" && usage
    fi

    if [[ -z "${QC_USER}" ]]; then
        [[ "${QC_USER}" = "" ]] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [[ -z "${QC_PASSWORD}" ]]; then
        [[ "${QC_PASSWORD}" = "" ]] && log_msg "ERROR" "Chipcode Password is not set" && usage
    fi

    if [[ -z "${CODELINARO_ACCESS_TOKEN}" ]]; then
        [[ "${CODELINARO_ACCESS_TOKEN}" = "" ]] && log_msg "ERROR" "Code Linaro Access Token is not set" && usage
    fi

    if [[ -z "${CHIPCODE_UTILITIES}" ]]; then
        [[ "${CHIPCODE_UTILITIES}" = "" ]] && log_msg "ERROR" "Chipcode utilities is not set" && usage
    fi

    if [[ -z "${GREASE_UTILITIES}" ]]; then
        [[ "${GREASE_UTILITIES}" = "" ]] && log_msg "ERROR" "Grease utilities URL is not set" && usage
    fi

    if [[ -z "${CODELINARO}" ]]; then
        [[ "${CODELINARO}" = "" ]] && log_msg "ERROR" "Code Linaro URL is not set" && usage
    fi

    if [[ -z "${CODELINARO_REPO}" ]]; then
        [[ "${CODELINARO_REPO}" = "" ]] && log_msg "ERROR" "Code Linaro repo tool URL is not set" && usage
    fi

    if [[ -z "${CHIPCODE}" ]]; then
        [[ "${CHIPCODE}" = "" ]] && log_msg "ERROR" "Chipcode URL is not set" && usage
    fi

    if [[ -z "${AU_LINUX_LA_QSSI}" ]]; then
        [[ "${AU_LINUX_LA_QSSI}" = "" ]] && log_msg "ERROR" "LA QSSI tag is not set" && usage
    fi

    if [[ -z "${QSSI_MANIFEST}" ]]; then
        [[ "${QSSI_MANIFEST}" = "" ]] && log_msg "ERROR" "LA QSSI manifest is not set" && usage
    fi

    if [[ -z "${QSSI_HISTORY}" ]]; then
        [[ "${QSSI_HISTORY}" = "" ]] && log_msg "ERROR" "LA QSSI history is not set" && usage
    fi

    if [[ -z "${AU_LINUX_LA_VENDOR}" ]]; then
        [[ "${AU_LINUX_LA_VENDOR}" = "" ]] && log_msg "ERROR" "LA Vendor tag is not set" && usage
    fi

    if [[ -z "${VENDOR_MANIFEST}" ]]; then
        [[ "${VENDOR_MANIFEST}" = "" ]] && log_msg "ERROR" "LA Vendor manifest is not set" && usage
    fi

    if [[ -z "${VENDOR_HISTORY}" ]]; then
        [[ "${VENDOR_HISTORY}" = "" ]] && log_msg "ERROR" "LA Vendor history is not set" && usage
    fi

    if [[ -z "${AU_LINUX_KERNEL}" ]]; then
        [[ "${AU_LINUX_KERNEL}" = "" ]] && log_msg "ERROR" "Linux Kernel tag is not set" && usage
    fi

    if [[ -z "${KERNEL_MANIFEST}" ]]; then
        [[ "${KERNEL_MANIFEST}" = "" ]] && log_msg "ERROR" "Linux Kernel manifest is not set" && usage
    fi

    if [[ -z "${KERNEL_HISTORY}" ]]; then
        [[ "${KERNEL_HISTORY}" = "" ]] && log_msg "ERROR" "Linux Kernel history repository URL is not set" && usage
    fi

    if [[ -z "${KERNEL_PARAMETER}" ]]; then
        [[ "${KERNEL_PARAMETER}" = "" ]] && log_msg "ERROR" "Kernel parameter is not set" && usage
    fi

    if [[ -z "${BUILD_DIR}" ]]; then
        [[ "${BUILD_DIR}" = "" ]] && log_msg "ERROR" "Build directory is not set" && usage
    fi

    if [[ -z "${CUSTOMER_ID}" ]]; then
        [[ "${CUSTOMER_ID}" = "" ]] && log_msg "ERROR" "Customer ID is not set" && usage
    fi

    if [[ -z "${PRODUCT}" ]]; then
        [[ "${PRODUCT}" = "" ]] && log_msg "ERROR" "Product is not set" && usage
    fi

    if [[ -z "${GITORG}" ]]; then
        [[ "${GITORG}" = "" ]] && log_msg "ERROR" "GITORG is not set" && usage
    fi

    if [[ -n "${AU_LINUX_LA_QSSI15}" ]] && [[ -z "${QSSI15_HISTORY}" ]]; then
        log_msg "ERROR" "LA QSSI15 is defined but QSSI15 history is not defined" && usage
    fi

    log_msg "DEBUG" "LA_validate_inputs: end"
}

function LA_setup_env() {
    log_msg "DEBUG" "LA_setup_env: start"

    LA_SYNC_REPO="${PRODUCT}_hlos_dev_la"
    CHIPCODE_LA_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${LA_SYNC_REPO}.git"
    LA_SYNC_DIR="${BUILD_DIR}/${LA_SYNC_REPO}"
    QSSI_SYNC_DIR="${LA_SYNC_DIR}/System_OSS_PROP/"
    LA_QSSI_SYNC_MARKER="${QSSI_SYNC_DIR}/system_oss_prop_complete"
    VENDOR_SYNC_DIR="${LA_SYNC_DIR}/Vendor_OSS_PROP/"
    LA_VENDOR_SYNC_MARKER="${VENDOR_SYNC_DIR}/vendor_oss_prop_complete"
    KERNEL_SYNC_DIR="${LA_SYNC_DIR}/Kernel_OSS_PROP/"
    LA_KERNEL_SYNC_MARKER="${KERNEL_SYNC_DIR}/kernel_oss_prop_complete"
    LA_QSSI_BUILD_DIR="${LA_SYNC_DIR}/SYSTEM_BUILD_DIR/"
    LA_QSSI_COPY_COMPLETE_MARKER="${LA_QSSI_BUILD_DIR}/system_oss_prop_cp_complete"
    LA_QSSI_BUILD_MARKER="${LA_QSSI_BUILD_DIR}/system_build_complete"
    LA_VENDOR_BUILD_DIR="${LA_SYNC_DIR}/VENDOR_BUILD_DIR/"
    LA_VENDOR_COPY_QSSI_COMPLETE_MARKER="${LA_VENDOR_BUILD_DIR}/system_oss_prop_cp_complete"
    LA_VENDOR_COPY_VENDOR_COMPLETE_MARKER="${LA_VENDOR_BUILD_DIR}/vendor_oss_prop_cp_complete"
    LA_VENDOR_COPY_KERNEL_COMPLETE_MARKER="${LA_VENDOR_BUILD_DIR}/kernel_platform_cp_complete"
    LA_VENDOR_BUILD_MARKER="${LA_VENDOR_BUILD_DIR}/vendor_build_complete"
    LA_SUPER_IMAGE_BUILD_MARKER="${LA_VENDOR_BUILD_DIR}/super_image_generation_complete"
    if [[ -n "${AU_LINUX_LA_QSSI15}" ]]; then
        QSSI15_SYNC_DIR="${LA_SYNC_DIR}/System15_OSS_PROP/"
        LA_QSSI15_SYNC_MARKER="${QSSI15_SYNC_DIR}/system_oss_prop_complete"
    fi

    log_msg "DEBUG" "LA_setup_env: end"
}

function LA_sync() {
    log_msg "DEBUG" "LA_sync: start"

    trc rm ~/.netrc

    log_msg "INFO" "Syncing LA..."

    # Download the hlos_dev_la distro
    log_msg "INFO" "Downloading the HLOS DEV LA Distro..."

    if [[ "$LA_SYNC_DIR" == *sa8797p* ]]; then
        echo "Skipping git_clone_chipcode as SP contains sa8797p"
    else
        git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_LA_REPO" "apps_kernel" "${LA_SYNC_DIR}"
    fi

    # Setup repo and sync_snap_v2.sh for chipcode history
    setup_grease_utils

    # sync_snap_v2 <image_type> <tree_type> <au_tag> <manifest_repo> <history_repo> <sync_dir> [sync_log_file]
    # QSSI STANDALONE TREE
    if [[ -e "${LA_QSSI_SYNC_MARKER}" ]]; then
        log_msg "INFO" "System OSS Prop process previously completed, skipping..."
    else
        sync_snap_v2 "qssi" "la" "la_qssi" "${AU_LINUX_LA_QSSI}" "${QSSI_MANIFEST}" "${QSSI_HISTORY}" "${QSSI_SYNC_DIR}"
        tst "touch ${LA_QSSI_SYNC_MARKER}"
    fi

    # QSSI 15 STANDALONE TREE
    if [[ -n "${AU_LINUX_LA_QSSI15}" ]] && [[ -e "${LA_QSSI15_SYNC_MARKER}" ]]; then
        log_msg "INFO" "System OSS 15 Prop process previously completed, skipping..."
    elif [[ -n "${AU_LINUX_LA_QSSI15}" ]] && [[ ! -e "${LA_QSSI15_SYNC_MARKER}" ]]; then
        sync_snap_v2 "qssi" "la" "la_qssi" "${AU_LINUX_LA_QSSI15}" "${QSSI_MANIFEST}" "${QSSI15_HISTORY}" "${QSSI15_SYNC_DIR}"
        tst "touch ${LA_QSSI15_SYNC_MARKER}"
    else
        log_msg "DEBUG" "System OSS 15 is not defined, skipping ..."
    fi

    # VENDOR STANDALONE TREE
    if [[ -e "${LA_VENDOR_SYNC_MARKER}" ]]; then
        log_msg "INFO" "Vendor OSS Prop process previously completed, skipping..."
    else
        sync_snap_v2 "vendor" "la" "la_vendor" "${AU_LINUX_LA_VENDOR}" "${VENDOR_MANIFEST}" "${VENDOR_HISTORY}" "${VENDOR_SYNC_DIR}"
        tst "touch ${LA_VENDOR_SYNC_MARKER}"
    fi

    # KERNEL.PLATFORM STANDALONE TREE
    if [[ -e "${LA_KERNEL_SYNC_MARKER}" ]]; then
        log_msg "INFO" "Kernel OSS Prop process previously completed, skipping..."
    else
        sync_snap_v2 "kernel" "la" "kernel_platform" "${AU_LINUX_KERNEL}" "${KERNEL_MANIFEST}" "${KERNEL_HISTORY}" "${KERNEL_SYNC_DIR}"
        tst "touch ${LA_KERNEL_SYNC_MARKER}"
    fi

    log_msg "DEBUG" "LA_sync: end"
}

function LA_build() {
    log_msg "DEBUG" "LA_build: start"

    # Compilation of QSSI Standalone
    if [[ -e "${LA_QSSI_BUILD_MARKER}" ]]; then
        log_msg "INFO" "System build process completed previously, skipping..."
    else
        tst "mkdir -p ${LA_QSSI_BUILD_DIR}"

        if [[ -e "${LA_QSSI_COPY_COMPLETE_MARKER}" ]]; then
            log_msg "INFO" "System_OSS_PROP copy process previously completed, skipping..."
        else
            local system_oss_prop_dir="${QSSI_SYNC_DIR}"
            if [[ -n "${AU_LINUX_LA_QSSI15}" ]]; then
                system_oss_prop_dir="${QSSI15_SYNC_DIR}"
            fi
            log_msg "INFO" "Copying contents of ${system_oss_prop_dir} to ${LA_QSSI_BUILD_DIR}..."
            log_cmd "rsync -auc --info=progress2 ${system_oss_prop_dir}/ ${LA_QSSI_BUILD_DIR}/"
            rsync -auc --info=progress2 "${system_oss_prop_dir}/" "${LA_QSSI_BUILD_DIR}/" || die "failed to copy System_OSS_PROP directory"
            tst "touch ${LA_QSSI_COPY_COMPLETE_MARKER}"
        fi

        tst "cd ${LA_QSSI_BUILD_DIR}"
        tst "source build/envsetup.sh"
        tst "lunch ${QSSI_LUNCH_CMD}-userdebug"
        tst "chmod a+x build.sh"
        tst "bash build.sh dist -j$(nproc) --qssi_only"
        tst "touch ${LA_QSSI_BUILD_MARKER}"
    fi

    if [[ -e "${LA_VENDOR_BUILD_MARKER}" ]]; then
        log_msg "INFO" "VENDOR BUILD process previously completed, skipping..."
    else
        tst "mkdir -p ${LA_VENDOR_BUILD_DIR}"

        if [[ -e "${LA_VENDOR_COPY_QSSI_COMPLETE_MARKER}" ]]; then
            log_msg "INFO" "System_OSS_PROP copy process previously completed, skipping..."
        else
            log_msg "INFO" "Copy the contents of System_OSS_PROP to ${LA_VENDOR_BUILD_DIR}"
            log_cmd "rsync -auc --info=progress2 ${QSSI_SYNC_DIR}/ ${LA_VENDOR_BUILD_DIR}/"
            rsync -auc --info=progress2 "${QSSI_SYNC_DIR}/" "${LA_VENDOR_BUILD_DIR}/" || die "failed to copy System_OSS_PROP directory"
            tst "touch ${LA_VENDOR_COPY_QSSI_COMPLETE_MARKER}"
        fi

        if [[ -e "${LA_VENDOR_COPY_VENDOR_COMPLETE_MARKER}" ]]; then
            log_msg "INFO" "Vendor_OSS_PROP copy process previously completed, skipping..."
        else
            log_msg "INFO" "Copy the contents of Vendor_OSS_PROP to ${LA_VENDOR_BUILD_DIR}"
            log_cmd "rsync -auc --info=progress2 ${VENDOR_SYNC_DIR}/ ${LA_VENDOR_BUILD_DIR}/"
            rsync -auc --info=progress2 "${VENDOR_SYNC_DIR}/" "${LA_VENDOR_BUILD_DIR}/" || die "failed to copy Vendor_OSS_PROP directory (timeout after 1 hour)"
            tst "touch ${LA_VENDOR_COPY_VENDOR_COMPLETE_MARKER}"
        fi

        if [[ -e "${LA_VENDOR_COPY_KERNEL_COMPLETE_MARKER}" ]]; then
            log_msg "INFO" "kernel_platform copy process previously completed, skipping..."
        else
            log_msg "INFO" "Copy the contents of kernel_platform to ${LA_VENDOR_BUILD_DIR}"
            log_cmd "rsync -auc --info=progress2 ${KERNEL_SYNC_DIR}/kernel_platform ${LA_VENDOR_BUILD_DIR}/"
            rsync -auc --info=progress2 "${KERNEL_SYNC_DIR}/kernel_platform" "${LA_VENDOR_BUILD_DIR}/" || die "failed to copy Kernel_OSS_PROP/kernel_platform directory (timeout after 30 minutes)"
            tst "touch ${LA_VENDOR_COPY_KERNEL_COMPLETE_MARKER}"
        fi

        tst "cd ${LA_VENDOR_BUILD_DIR}"

        # Apply makefile violation fixes to whitelist AFTER files are copied to build directory
        LA_apply_makefile_fixes

        # Resolve duplicate Android.bp module definitions
        LA_resolve_duplicate_modules

        if [[ -n "${AU_LINUX_LA_QSSI15}" ]]; then
            echo "14" > cts/tests/tests/os/assets/platform_versions.txt
            echo "15" >> cts/tests/tests/os/assets/platform_versions.txt
        fi
        # patch cts file
        tst "source build/envsetup.sh"
        tst "lunch ${LUNCH_CMD}-userdebug"
        tst "source ./kernel_platform/qcom/proprietary/prebuilt_HY11/vendorsetup.sh"
        tst "RECOMPILE_KERNEL=1 ./kernel_platform/build/android/prepare_vendor.sh autogvm ${KERNEL_PARAMETER}"
        local EXPERIMENTAL_USE_OPENJDK9=''
        if [[ -n "${EXPERIMENTAL_USE_OPENJDK9}" ]]; then
            EXPERIMENTAL_USE_OPENJDK9='EXPERIMENTAL_USE_OPENJDK9=1.8'
        fi
        tst "bash build.sh -j$(nproc) dist --target_only ${EXPERIMENTAL_USE_OPENJDK9}"
        tst "touch ${LA_VENDOR_BUILD_MARKER}"
    fi

    # Super Image Generation
    if [[ -e "${LA_SUPER_IMAGE_BUILD_MARKER}" ]]; then
        log_msg "INFO" "Super Image Generation process previously completed, skipping..."
    else
        tst "cd ${LA_SYNC_DIR}"
        log_msg "INFO" "Doing Super Image Generation..."

        local skip_qiifa='--skip_qiifa'
        if [[ -n "${ENABLE_QIIFA}" ]]; then
            skip_qiifa=''
        fi

        local output_ota='--output_ota'
        if [[ -n "${LA_DISABLE_OUTPUT_OTA}" ]]; then
                output_ota=''
        fi

        local no_tmp='--no_tmp'
        if [[ -n "${LA_ENABLE_TMP}" ]]; then
            no_tmp=''
        fi

        tst python "${LA_VENDOR_BUILD_DIR}/vendor/qcom/opensource/core-utils/build/build_image_standalone.py" --image super --qssi_build_path "${LA_QSSI_BUILD_DIR}" --target_build_path "${LA_VENDOR_BUILD_DIR}" --merged_build_path "${LA_VENDOR_BUILD_DIR}" --target_lunch $LUNCH_CMD "${skip_qiifa} ${output_ota} ${no_tmp}"
        tst "touch ${LA_SUPER_IMAGE_BUILD_MARKER}"
    fi

    log_msg "DEBUG" "LA_build: end"
}

function LA_apply_makefile_fixes() {
    log_msg "DEBUG" "LA_apply_makefile_fixes: start"

    local whitelist_file="${LA_VENDOR_BUILD_DIR}/vendor/qcom/opensource/core-utils/build/makefile_whitelist.py"

    if [ ! -f "$whitelist_file" ]; then
        log_msg "WARN" "Whitelist file not found at $whitelist_file"
        return 1
    fi

    log_msg "INFO" "Applying makefile violation fixes to build directory whitelist..."

    # Fix LOCAL_COPY_HEADERS violations
    local local_copy_headers_files=(
        "\"vendor/qcom/prebuilt_HY11/target/product/gen4_gvm_microdroid/Android.mk\","
        "\"vendor/qcom/prebuilt_HY11/target/product/gen4_gvm/Android.mk\","
        "\"vendor/qcom/mm-hab/uhab/Android.mk\","
    )
    for line in "${local_copy_headers_files[@]}"; do
        if ! grep -q "$line" "$whitelist_file"; then
            log_msg "INFO" "Adding $line to LOCAL_COPY_HEADERS_WHITELIST"
            sed -i "/LOCAL_COPY_HEADERS_WHITELIST/a\\
$line" "$whitelist_file"
        fi
    done

    # Fix SHELL violations
    local shell_line="\"vendor/qcom/wlan/utils/halproxydaemon/Android.mk\","
    if ! grep -q "$shell_line" "$whitelist_file"; then
        log_msg "INFO" "Adding $shell_line to SHELL_WHITELIST"
        sed -i "/SHELL_WHITELIST/a\\
$shell_line" "$whitelist_file"
    fi

    # Fix RM violations
    local rm_line="\"vendor/qcom/common/scripts/Android.mk\","
    if ! grep -q "$rm_line" "$whitelist_file"; then
        log_msg "INFO" "Adding $rm_line to RM_WHITELIST"
        sed -i "/RM_WHITELIST/a\\
$rm_line" "$whitelist_file"
    fi

    log_msg "INFO" "Makefile violation fixes applied successfully"
    log_msg "DEBUG" "LA_apply_makefile_fixes: end"
}

function LA_resolve_duplicate_modules() {
    log_msg "DEBUG" "LA_resolve_duplicate_modules: start"
    log_msg "INFO" "Resolving duplicate Android.bp and Android.mk module definitions..."

    local build_dir="${LA_VENDOR_BUILD_DIR}"
    local proprietary_base="${build_dir}/vendor/qcom/proprietary"
    local opensource_base="${build_dir}/vendor/qcom"
    local bp_conflicts_found=0
    local mk_conflicts_found=0

    if [ ! -d "$proprietary_base" ] || [ ! -d "$opensource_base" ]; then
        log_msg "WARN" "Proprietary or opensource vendor directories not found, skipping duplicate resolution"
        return 0
    fi

    log_msg "INFO" "Scanning for duplicate Android.bp files between proprietary and opensource..."

    # Find all Android.bp files in proprietary directory
    while IFS= read -r -d '' proprietary_file; do
        # Get relative path from proprietary base
        local rel_path="${proprietary_file#$proprietary_base/}"
        local opensource_file="${opensource_base}/${rel_path}"

        # Check if corresponding opensource file exists
        if [ -f "$opensource_file" ]; then
            log_msg "INFO" "Found duplicate: removing opensource version at ${opensource_file#$build_dir/}"
            rm -f "$opensource_file"
            bp_conflicts_found=$((bp_conflicts_found + 1))
        fi
    done < <(find "$proprietary_base" -name "Android.bp" -type f -print0)

    log_msg "INFO" "Scanning for duplicate Android.mk files between proprietary and opensource..."

    # Find all Android.mk files in proprietary directory
    while IFS= read -r -d '' proprietary_file; do
        # Get relative path from proprietary base
        local rel_path="${proprietary_file#$proprietary_base/}"
        local opensource_file="${opensource_base}/${rel_path}"

        # Check if corresponding opensource file exists
        if [ -f "$opensource_file" ]; then
            log_msg "INFO" "Found duplicate: removing opensource version at ${opensource_file#$build_dir/}"
            rm -f "$opensource_file"
            mk_conflicts_found=$((mk_conflicts_found + 1))
        fi
    done < <(find "$proprietary_base" -name "Android.mk" -type f -print0)

    local total_conflicts=$((bp_conflicts_found + mk_conflicts_found))
    if [ $total_conflicts -gt 0 ]; then
        log_msg "INFO" "Resolved $bp_conflicts_found duplicate Android.bp files and $mk_conflicts_found duplicate Android.mk files (kept proprietary versions)"
    else
        log_msg "INFO" "No duplicate Android.bp or Android.mk files found"
    fi

    log_msg "DEBUG" "LA_resolve_duplicate_modules: end"
}
