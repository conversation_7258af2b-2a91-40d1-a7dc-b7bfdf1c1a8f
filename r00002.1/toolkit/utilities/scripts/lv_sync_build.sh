#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source "${UTILITIES_DIR}/utilities.sh"
source "${UTILITIES_DIR}/git_utilities.sh"
source "${UTILITIES_DIR}/logging.sh"

function LV_validate_inputs() {
    log_msg "DEBUG" "parse_options: start"

    if [ -z "$CHIPCODE_TAG" ]; then
        [ "$CHIPCODE_TAG" = "" ] && log_msg "ERROR" "Chicode Tag is not set" && usage
    fi

    if [ -z "$QC_USER" ]; then
        [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [ -z "$QC_PASSWORD" ]; then
        [ "$QC_PASSWORD" = "" ] && log_msg "ERROR" "Chipcode Password is not set" && usage
    fi

    if [ -z "$CODELINARO_ACCESS_TOKEN" ]; then
        [ "$CODELINARO_ACCESS_TOKEN" = "" ] && log_msg "ERROR" "Code Linaro Access Token is not set" && usage
    fi

    if [ -z "$CHIPCODE" ]; then
        [ "$CHIPCODE" = "" ] && log_msg "ERROR" "Chipcode URL is not set" && usage
    fi

    if [ -z "$BUILD_DIR" ]; then
        [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "Build directory is not set" && usage
    fi

    if [ -z "$AU_LINUX_KERNEL" ]; then
        [ "$AU_LINUX_KERNEL" = "" ] && log_msg "ERROR" "Linux Kernel tag is not set" && usage
    fi

    if [ -z "$KERNEL_MANIFEST" ]; then
        [ "$KERNEL_MANIFEST" = "" ] && log_msg "ERROR" "Linux Kernel manifest is not set" && usage
    fi

    if [ -z "$AU_LINUX_LV" ]; then
        [ "$AU_LINUX_LV" = "" ] && log_msg "ERROR" "LV manifest name is not set" && usage
    fi

    if [ -z "$LV_MANIFEST" ]; then
        [ "$LV_MANIFEST" = "" ] && log_msg "ERROR" "LV manifest repository URL is not set" && usage
    fi

    if [ -z "$MANIFEST_BRANCH" ]; then
        [ "$MANIFEST_BRANCH" = "" ] && log_msg "ERROR" "MANIFEST_BRANCH is not set" && usage
    fi

    if [ -z "$CODELINARO" ]; then
        [ "$CODELINARO" = "" ] && log_msg "ERROR" "Code Linaro URL is not set" && usage
    fi

    if [ -z "$CODELINARO_REPO" ]; then
        [ "$CODELINARO_REPO" = "" ] && log_msg "ERROR" "Code Linaro repo tool URL is not set" && usage
    fi

    if [ -z "$PRODUCT" ]; then
        [ "$PRODUCT" = "" ] && log_msg "ERROR" "Product is not set" && usage
    fi

    if [ -z "$GITORG" ]; then
        [ "$GITORG" = "" ] && log_msg "ERROR" "GITORG is not set" && usage
    fi

    log_msg "DEBUG" "parse_options: start"
}

function LV_setup_env() {
    log_msg "DEBUG" "LV_setup_env: start"

    set_user_group_for_ride_sdk

    LV_SYNC_REPO="${PRODUCT}_hlos_dev_lv"
    CHIPCODE_LV_REPO="https://chipmaster2.qti.qualcomm.com/home2/git/${GITORG}/${LV_SYNC_REPO}.git"
    LV_SYNC_DIR="${BUILD_DIR}/${LV_SYNC_REPO}"
    LV_KERNEL_SYNC_DIR="$LV_SYNC_DIR/apps_kernel"
    LV_KERNEL_SYNC_MARKER="$LV_SYNC_DIR/apps_kernel/kernel_sync_complete"
    LV_CLO_SYNC_DIR="$LV_SYNC_DIR/lvgvm_clo"
    LV_CLO_SYNC_MARKER="$LV_CLO_SYNC_DIR/LV_CLO_SYNC_MARKER"
    LV_SYNC_APPS_PROC_DIR="$LV_SYNC_DIR/lvgvm/apps_proc"
    LV_BUILD_COMPLETE_MARKER="$LV_SYNC_DIR/lv_build_complete"

    log_msg "DEBUG" "LV_setup_env: end"
}

function LV_sync() {
    log_msg "DEBUG" "LV_sync: start"

    # Download the hlos_dev_lv distro
    git_clone_chipcode "$CHIPCODE_TAG" "$CHIPCODE_LV_REPO" "common" "${LV_SYNC_DIR}"

    # Download Kernel opensource code from CLO.
    if [ -e "$LV_KERNEL_SYNC_MARKER" ]; then
        log_msg "INFO" "LV Kernel sync process previously completed, skipping..."
    else
        log_msg "INFO" "Downloading the LV Kernel sources ..."
        tst "rm -rf $LV_KERNEL_SYNC_DIR"
        tst "mkdir -p $LV_KERNEL_SYNC_DIR"
        repo_sync "$LV_KERNEL_SYNC_DIR" "${CODELINARO}/${KERNEL_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_KERNEL}.xml"
        tst "touch $LV_KERNEL_SYNC_MARKER"
    fi

    # Download Linux opensource code from CLO
    if [ -e "$LV_CLO_SYNC_MARKER" ]; then
        log_msg "INFO" "LV CLO sync process previously completed, skipping..."
    else
        log_msg "INFO" "Downloading the LV CLO Distro..."
        tst "rm -rf $LV_CLO_SYNC_DIR"
        tst "mkdir -p $LV_CLO_SYNC_DIR"
        repo_sync "$LV_CLO_SYNC_DIR" "${CODELINARO}/${LV_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_LV}.xml"

        # copy synced CLO code base
        tst "rsync -auqc ${LV_CLO_SYNC_DIR}/ ${LV_SYNC_APPS_PROC_DIR}/"

        # Combine kernel code to Linux folder
        # cp -r "$LV_KERNEL_SYNC_DIR" "$LV_SYNC_APPS_PROC_DIR/kernel/"
        # cd $LV_SYNC_APPS_PROC_DIR/kernel/ || die "cd $LV_SYNC_APPS_PROC_DIR/kernel/ failed"
        # mv apps_kernel/kernel_platform/* kernel-6.1/kernel_platform/ || die "cd $LV_SYNC_APPS_PROC_DIR/kernel/ failed"
        tst "cp -rp $LV_KERNEL_SYNC_DIR/kernel_platform/* $LV_SYNC_APPS_PROC_DIR/kernel/kernel-6.1/kernel_platform/"

        tst "touch $LV_CLO_SYNC_MARKER"
    fi

    log_msg "DEBUG" "LV_sync: end"
}

function LV_build() {
    log_msg "DEBUG" "LV_build: start"

    if [ -e "$LV_BUILD_COMPLETE_MARKER" ]; then
        log_msg "INFO" "LV build has previously completed, skipping ..."
    else
        log_msg "INFO" "Setting user group permissions for LV build"

        set_netrc 'chipmaster2.qti.qualcomm.com' "$QC_USER" "$QC_PASSWORD"
        set_netrc 'git.codelinaro.org' "$QC_USER" "$CODELINARO_ACCESS_TOKEN"
        set_netrc "$CHIPCODE" "$QC_USER" "$QC_PASSWORD"
        tst "cp ~/.netrc /home/<USER>/"
        tst "chown -R ride_sdk_user:ride_sdk_grp /home/<USER>"
        tst "chown -R ride_sdk_user:ride_sdk_grp $LV_SYNC_DIR"

        # create script with the LV build steps
        cat << EOF > "${LV_SYNC_DIR}/lv_build.sh"
#!/bin/bash

# Exit with error
function die() {
	echo $1
	exit 1
}


echo "INFO: Start LV build ..."
# 5. Build the apps processor linux HLOS images
cd "$LV_SYNC_APPS_PROC_DIR/poky"  || die "cd $LV_SYNC_APPS_PROC_DIR/poky failed"
source build/conf/set_bb_env.sh || die "failed to setup linux HLOS images"
$LV_BUILD_CMD || die "Compilation of LV failed!"

touch "$LV_BUILD_COMPLETE_MARKER"
EOF

        # logging steps into CMDS_LOG_FILE
        log_cmd "cd $LV_SYNC_APPS_PROC_DIR/poky"
        log_cmd "source build/conf/set_bb_env.sh"
        log_cmd "build-quin-gvm-lemans-image"

        # execute LV build as ride_sdk_user
        log_msg "INFO" "Switching to user 'ride_sdk_user' and building LV..."
        su - ride_sdk_user -c "bash ${LV_SYNC_DIR}/lv_build.sh"
        rm /home/<USER>/.netrc

        # Add read+execute access to all users
        tst "chmod 755 ${LV_SYNC_APPS_PROC_DIR}/poky/build/tmp-glibc/"

        if [ ! -e "$LV_BUILD_COMPLETE_MARKER" ]; then
            die "LV build failed!"
        fi

    fi

    log_msg "DEBUG" "LV_build: end"
}
