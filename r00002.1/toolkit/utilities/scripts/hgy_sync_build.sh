#!/bin/bash
#
# '-e' not added as there might be minor build errors that can be bypassed
#
# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

source "${UTILITIES_DIR}/utilities.sh"
source "${UTILITIES_DIR}/git_utilities.sh"
source "${UTILITIES_DIR}/logging.sh"

function HGY_validate_inputs() {
    log_msg "DEBUG" "parse_options: start"

    if [ -z "$CHIPCODE_TAG" ]; then
        [ "$CHIPCODE_TAG" = "" ] && log_msg "ERROR" "Chicode Tag is not set" && usage
    fi

    if [ -z "$QC_USER" ]; then
        [ "$QC_USER" = "" ] && log_msg "ERROR" "Chipcode User is not set" && usage
    fi

    if [ -z "$QC_PASSWORD" ]; then
        [ "$QC_PASSWORD" = "" ] && log_msg "ERROR" "Chipcode Password is not set" && usage
    fi

    if [ -z "$CODELINARO_ACCESS_TOKEN" ]; then
        [ "$CODELINARO_ACCESS_TOKEN" = "" ] && log_msg "ERROR" "Code Linaro Access Token is not set" && usage
    fi

    if [ -z "$CHIPCODE" ]; then
        [ "$CHIPCODE" = "" ] && log_msg "ERROR" "Chipcode URL is not set" && usage
    fi

    if [ -z "$BUILD_DIR" ]; then
        [ "$BUILD_DIR" = "" ] && log_msg "ERROR" "Build directory is not set" && usage
    fi

    if [ -z "$AU_LINUX_LE" ]; then
        [ "$AU_LINUX_LE" = "" ] && log_msg "ERROR" "Linux LE tag is not set" && usage
    fi

    if [ -z "$AU_LINUX_LE_MANIFEST_REPO" ]; then
        [ "$AU_LINUX_LE_MANIFEST_REPO" = "" ] && log_msg "ERROR" "AU Linux LE Manifest Repo is not set" && usage
    fi

    if [ -z "$LE_MANIFEST_BRANCH" ]; then
        [ "$LE_MANIFEST_BRANCH" = "" ] && log_msg "ERROR" "LE_MANIFEST_BRANCH is not set" && usage
    fi

    if [ -z "$MANIFEST_BRANCH" ]; then
        [ "$MANIFEST_BRANCH" = "" ] && log_msg "ERROR" "MANIFEST_BRANCH is not set" && usage
    fi

    if [ -z "$KERNEL_MANIFEST" ]; then
        [ "$KERNEL_MANIFEST" = "" ] && log_msg "ERROR" "Linux Kernel manifest is not set" && usage
    fi

    if [ -z "$AU_LY" ]; then
        [ "$AU_LY" = "" ] && log_msg "ERROR" "LY manifest name is not set" && usage
    fi

    if [ -z "$AU_LINUX_LKP" ]; then
        [ "$AU_LINUX_LKP" = "" ] && log_msg "ERROR" "AU_LINUX_LKP is not set" && usage
    fi

    if [ -z "$LY_MANIFEST" ]; then
        [ "$LY_MANIFEST" = "" ] && log_msg "ERROR" "LY manifest repository URL is not set" && usage
    fi

    if [ -z "$HGY_BUILD_CMD" ]; then
        [ "$HGY_BUILD_CMD" = "" ] && log_msg "ERROR" "HGY_BUILD_CMD is not set" && usage
    fi

    if [ -z "$CODELINARO" ]; then
        [ "$CODELINARO" = "" ] && log_msg "ERROR" "Code Linaro URL is not set" && usage
    fi

    if [ -z "$CODELINARO_REPO" ]; then
        [ "$CODELINARO_REPO" = "" ] && log_msg "ERROR" "Code Linaro repo tool URL is not set" && usage
    fi

    if [ -z "$PRODUCT" ]; then
        [ "$PRODUCT" = "" ] && log_msg "ERROR" "Product is not set" && usage
    fi

    log_msg "DEBUG" "parse_options: end"
}

function HGY_setup_env() {
    log_msg "DEBUG" "HGY_setup_env: start"

    set_user_group_for_ride_sdk

    HGY_SYNC_REPO="${PRODUCT}_hlos_dev_hgy"
    HGY_SYNC_DIR="${BUILD_DIR}/${HGY_SYNC_REPO}"
    HGY_BUILD_DIR="${HGY_SYNC_DIR}/Auto/"
    HGY_PREPARE_BUILD_MARKER="${HGY_BUILD_DIR}/build_prepare_complete"
    HGY_BUILD_MARKER_FILE="$HGY_BUILD_DIR/build_complete"
    HGY_SYNC_MARKER="${HGY_SYNC_DIR}/le_sync/le_sync_done"
    LE_SYNC_DIR="${HGY_SYNC_DIR}/le_sync/LE_SYNC/"
    LE_SYNC_MARKER="$LE_SYNC_DIR/le_sync_done"
    LE_KERNEL_SYNC_DIR="${HGY_SYNC_DIR}/le_sync/KERNEL_SYNC/"
    LE_KERNEL_SYNC_MARKER="$LE_KERNEL_SYNC_DIR/kernel_sync_done"
    LE_AUTO_PVM_SYNC_DIR="${HGY_SYNC_DIR}/le_sync/AUTO_PVM_SYNC/"
    LE_AUTO_PVM_SYNC_MARKER="$LE_AUTO_PVM_SYNC_DIR/auto_pvm_sync_done"
    LY_AMSS_STANDARD_OEM_DIR="${HGY_SYNC_DIR}/le_sync/${PRODUCT}_amss_standard_oem/"
    HGY_BUILD_LOG="${HGY_BUILD_DIR}/build_output.log"
    CHIPCODE_AMSS_STANDARD_OEM_REPO="https://qpm-git.qualcomm.com/home2/git/${GITORG}/${PRODUCT}_amss_standard_oem.git"

    log_msg "DEBUG" "HGY_setup_env: end"
}

function HGY_sync() {
    log_msg "DEBUG" "HGY_sync: start"

    if [ -e "$HGY_SYNC_MARKER" ]; then
        log_msg "INFO" "HGY sync has completed previously, proceed to build ..."
    else
        repo_install ${HGY_SYNC_DIR}

        # Downloading Base Yocto4.0 support
        if [ -e "$LE_SYNC_MARKER" ]; then
            log_msg "INFO" "LE sync has completed previously, proceeding with next steps ..."
        else
            mkdir -p "$LE_SYNC_DIR" && cd "$LE_SYNC_DIR" || die "cd $LE_SYNC_DIR failed!"
            repo_sync "${LE_SYNC_DIR}" "${AU_LINUX_LE_MANIFEST_REPO}" "${LE_MANIFEST_BRANCH}" "${AU_LINUX_LE}"
            touch "$LE_SYNC_MARKER"
        fi

        # Downloading Kernel
        if [ -e "$LE_KERNEL_SYNC_MARKER" ]; then
            log_msg "INFO" "LE Kernel sync has completed previously, proceeding with next steps ..."
        else
            mkdir -p "$LE_KERNEL_SYNC_DIR" && cd "$LE_KERNEL_SYNC_DIR" || die "cd $LE_KERNEL_SYNC_DIR failed!"
            repo_sync "${LE_KERNEL_SYNC_DIR}" "${CODELINARO}/${KERNEL_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LINUX_LKP}"
            touch "$LE_KERNEL_SYNC_MARKER"
        fi

        # Downloading Auto PVM support
        if [ -e "$LE_AUTO_PVM_SYNC_MARKER" ]; then
            log_msg "INFO" "LE Auto PVM sync has completed previously, proceeding with next steps ..."
        else
            mkdir -p "$LE_AUTO_PVM_SYNC_DIR" && cd "$LE_AUTO_PVM_SYNC_DIR" || die "cd $LE_AUTO_PVM_SYNC_DIR failed!"
            repo_sync "${LE_AUTO_PVM_SYNC_DIR}" "${CODELINARO}/${LY_MANIFEST}.git" "${MANIFEST_BRANCH}" "${AU_LY}"
            touch "$LE_AUTO_PVM_SYNC_MARKER"
        fi

        git_clone_chipcode $CHIPCODE_TAG $CHIPCODE_AMSS_STANDARD_OEM_REPO "common" $LY_AMSS_STANDARD_OEM_DIR

        touch "$HGY_SYNC_MARKER"
    fi

    log_msg "DEBUG" "HGY_sync: end"
}

function HGY_build() {
    log_msg "DEBUG" "HGY_build: start"

    if [ -e "$HGY_BUILD_MARKER_FILE" ]; then
        log_msg "INFO" "Building HGY completed, proceeding to META generation..."
    else
        # Change ownership of HGY_BUILD_DIR to ride_sdk_user
        mkdir -p "$HGY_BUILD_DIR" || die "mkdir -p $HGY_BUILD_DIR failed!"
        chown -R ride_sdk_user:ride_sdk_grp /home/<USER>
        chown ride_sdk_user:ride_sdk_grp "$HGY_BUILD_DIR"

        # Create script with the build steps
        cat << EOF > "${HGY_BUILD_DIR}/build.sh"
#!/bin/bash
export SHELL=/bin/bash
# Exit with error
die() {
    echo \$1
    exit 1
}

if [ -e "${HGY_PREPARE_BUILD_MARKER}" ]; then
    echo "Folder preparation complete, proceeding with build steps ..."
else
    # Setup build directory
    mkdir -p "${HGY_BUILD_DIR}" && cd "${HGY_BUILD_DIR}" || die "cd ${HGY_BUILD_DIR} failed!"
    echo "rsync -auqc ${LY_AMSS_STANDARD_OEM_DIR} ${HGY_BUILD_DIR}"
    rsync -auqc "${LY_AMSS_STANDARD_OEM_DIR}" "${HGY_BUILD_DIR}" || die "rsync -auqc ${LY_AMSS_STANDARD_OEM_DIR} ${HGY_BUILD_DIR} failed!"
    echo "rsync -auqc $LE_AUTO_PVM_SYNC_DIR ${HGY_BUILD_DIR}/apps/apps_proc/"
    rsync -auqc "$LE_AUTO_PVM_SYNC_DIR" "${HGY_BUILD_DIR}/apps/apps_proc/" || die "rsync -auqc ${LY_AMSS_STANDARD_OEM_DIR} ${HGY_BUILD_DIR} failed!"
    echo "rsync -auqc ${LE_SYNC_DIR}/layers/ ${HGY_BUILD_DIR}/apps/apps_proc/layers/"
    rsync -auqc "${LE_SYNC_DIR}/layers/" "${HGY_BUILD_DIR}/apps/apps_proc/layers/" || die "rsync -auqc ${LY_AMSS_STANDARD_OEM_DIR} ${HGY_BUILD_DIR} failed!"
    echo "cp -fp ${LE_SYNC_DIR}/setup-environment ${HGY_BUILD_DIR}/apps/apps_proc/"
    cp -fpv "${LE_SYNC_DIR}/setup-environment" "${HGY_BUILD_DIR}/apps/apps_proc/" || die "cp -fp ${LE_SYNC_DIR}/setup-environment ${HGY_BUILD_DIR}/apps/apps_proc/ failed!"
    echo "rsync -auqc ${LE_KERNEL_SYNC_DIR} ${HGY_BUILD_DIR}/apps/apps_proc/kernel/"
    rsync -auqc "${LE_KERNEL_SYNC_DIR}" "${HGY_BUILD_DIR}/apps/apps_proc/kernel/" || die "rsync -auqc ${LE_KERNEL_SYNC_DIR} ${HGY_BUILD_DIR}/apps/apps_proc/kernel/ failed!"
    echo "mkdir -p ${HGY_BUILD_DIR}/apps/apps_proc/sources"
    mkdir -p "${HGY_BUILD_DIR}/apps/apps_proc/sources" || die "mkdir -p ${HGY_BUILD_DIR}/apps/apps_proc/sources failed!"
    echo "rsync -auqc ${LE_KERNEL_SYNC_DIR} ${HGY_BUILD_DIR}/apps/apps_proc/sources/kernel/"
    rsync -auqc "${LE_KERNEL_SYNC_DIR}" "${HGY_BUILD_DIR}/apps/apps_proc/sources/kernel/" || die "rsync -auqc ${LE_KERNEL_SYNC_DIR} ${HGY_BUILD_DIR}/apps/apps_proc/sources/kernel/ failed!"

    touch "${HGY_PREPARE_BUILD_MARKER}"
fi

echo "Start yocto build ..."

# Build the apps processor linux HLOS images
cd ${HGY_BUILD_DIR}/apps/apps_proc/poky/build  || die "cd ${HGY_BUILD_DIR}/apps/apps_proc/poky/build failed"
source conf/set_bb_env.sh -t sa8797 || die "failed to setup linux HLOS images"

# Execute the build command
${HGY_BUILD_CMD} 2>&1 | tee "${HGY_BUILD_LOG}"
if grep -q "ERROR" "${HGY_BUILD_LOG}"; then
    echo "Build failed. Applying patch..."
    sed -i 's/.*_xgetbv.*/\ \ return\ 0;/' ${HGY_BUILD_DIR}/apps/apps_proc/build-auto/tmp-glibc/work/x86_64-linux/orc-native/0.4.39-r0/orc-0.4.39/orc/orccpu-x86.c

    # Trigger the build again
    ${HGY_BUILD_CMD} 2>&1 | tee "${HGY_BUILD_LOG}"
    if grep -q "ERROR" "${HGY_BUILD_LOG}"; then
        die "Build Failed!"
    fi
fi
touch ${HGY_BUILD_MARKER_FILE}
EOF

        echo "Setting user group permissions for HGY build"
        set_netrc 'chipmaster2.qti.qualcomm.com' $QC_USER $QC_PASSWORD
        set_netrc 'git.codelinaro.org' $QC_USER $CODELINARO_ACCESS_TOKEN
        set_netrc $CHIPCODE $QC_USER $QC_PASSWORD
        mv ~/.netrc /home/<USER>/

        # Execute HGY build as ride_sdk_user
        echo "Switching to user 'ride_sdk_user' and building HGY..."
        su - ride_sdk_user -c "bash ${HGY_BUILD_DIR}/build.sh"

        rm /home/<USER>/.netrc

        if [ ! -e "$HGY_BUILD_MARKER_FILE" ]; then
            die "HGY build failed!"
        fi

    fi

    log_msg "DEBUG" "HGY_build: end"
}
