#!/bin/bash

# Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

# Exit with error
die() {
    echo $1
    exit 1
}

usage() {
    echo "$0 --buildpath [BUILD_DIRECTORY] --numsoc [1 (SX)| 2 (MX)]"
    exit 1
}

# Get the directory of the script
script_directory="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Print the script's directory to the terminal
echo "The script is located in: $script_directory"

[ -r $script_directory/bsp_config ] || die "Config file ($script_directory/bsp_config) not found."
source $script_directory/bsp_config || echo "source of confifg file : bsp_config failed."

while [[ $# -gt 0 ]]; do
  key="$1"

  case $key in
    --help)
      usage
      shift
      ;;
    --buildpath)
      build_dir=$2
      shift 
      shift 
      ;;
    --numsoc)
      PLATFORM_TYPE=$2
      shift 
      shift 
      ;;
    *)
      POSITIONAL+=("$1") 
      shift 
      ;;
  esac
done

pcat_flash_flag=false
is_build_meta=true

if [ -z "$build_dir" ]; then 
   [ "$build_dir" = "" ] && echo "Build Path not entered" && usage
fi

echo "Platform type is $AU_TYPE - $PLATFORM_TYPE"

# Setting partitions and files prefixes for Gen4 Platform Types
if [ "$AU_TYPE" == "Monaco" ]; then
   partition_xml_dir=$build_dir/common/config/emmc/$PRODUCT_FLAVOR
   partition_index="0,1,2"
   platform_prefix=2
else
   if [ "$PRODUCT_FLAVOR" == "8650_asic" ]; then
      partition_xml_dir=$build_dir/common/config/ufs/8650
   else
      partition_xml_dir=$build_dir/common/config/ufs/$PRODUCT_FLAVOR
   fi
   partition_index="0,1,2,3,4,5,6,7,8,9"
   platform_prefix=3
fi

usermod -a -G dialout root

# This function sets the various files that will be in a META build (xmls, cdt files, etc)
set_flash_params() {
    if [ "$1" = true ]; then
        # IF THE CASE OF BUILD BEING META
        QC_CDT_UFS_DIR=$build_dir/common/build/ufs/$PRODUCT_FLAVOR
        if [[ -d "$build_dir/boot/BIN_${AU_TYPE}_AU" ]]; then
            # QC_PROG_DIR if build is NOT 4.4.0 based
            QC_PROG_DIR=$build_dir/boot/BIN_${AU_TYPE}_AU/boot_images/boot/QcomPkg/SocPkg/$AU_TYPE/Bin/AU/DEBUG/prog_firehose_ddr.elf
            FLASH_DELAY=true
        else
            QC_PROG_DIR=$build_dir/boot/boot_images/boot/QcomPkg/SocPkg/$AU_TYPE/Bin/AU/DEBUG/prog_firehose_ddr.elf
        fi
        QC_PARTITION_XML=$(find $partition_xml_dir -name "partition_ext.xml" -exec realpath {} \;)
        QC_UFS_XML=$(find $build_dir/common -name "provision_default.xml" -exec realpath {} \;)
        PROG_DIR=/tmp/cdt_ufs
        echo $QC_PARTITION_XML
        echo $QC_UFS_XML
    else
        # IF CASE OF BUILD BEING FLAT
        PROG_DIR=$build_dir 
    fi
    echo "PROGRAMMING DIR = $PROG_DIR"
}

cdt_ufs_prep() {
    echo "Preparing CDT and UFS files..."
    rm -rf /tmp/cdt_ufs
    [ ! -d /tmp/cdt_ufs ] && sudo mkdir -p /tmp/cdt_ufs 

    # 1 - Edit the rawprogram3.xml to include LEMANSAU*.bin (ie. SED..)
    cp $QC_CDT_UFS_DIR/gpt_*${platform_prefix}.bin /tmp/cdt_ufs
    cp $QC_CDT_UFS_DIR/patch${platform_prefix}.xml /tmp/cdt_ufs
    cp $QC_CDT_UFS_DIR/rawprogram${platform_prefix}.xml /tmp/cdt_ufs
    # Copying the program firehose ddr file to cdt directory
    cp $QC_PROG_DIR /tmp/cdt_ufs
    cp $QC_PARTITION_XML /tmp/cdt_ufs
    cp $QC_UFS_XML /tmp/cdt_ufs
    # 2 - Handling CDT for Ride SX or MX
    if [[ $PLATFORM_TYPE -eq 2 ]]; then
        echo "Setting CDT to Ride MX Configuration"
        cp $build_dir/common/cdt/LEMANSAU_QAM_2.1.0.bin /tmp/cdt_ufs
        sed -i 's/filename="" label="cdt"/filename="LEMANSAU_QAM_2.1.0.bin" label="cdt"/g' /tmp/cdt_ufs/rawprogram${platform_prefix}.xml
    else
        echo "Setting CDT to Ride SX Configuration"
        cp $build_dir/common/cdt/LEMANSAU_QAM_1.1.0.bin /tmp/cdt_ufs
        sed -i 's/filename="" label="cdt"/filename="LEMANSAU_QAM_1.1.0.bin" label="cdt"/g' /tmp/cdt_ufs/rawprogram${platform_prefix}.xml
    fi
    chmod -R 777 /tmp/cdt_ufs/
    chown -R root:root /tmp/cdt_ufs/
}

flashing_precheck() {
    if [ ! -d "/opt/QTI/QUD" ]; then
        die "QUD directory does not exist, please check QUD installation."
    fi

    if [ ! -d "/opt/qcom/QUTS" ]; then
        die "QUTS directory does not exist, please check QUTS installation."
    fi

    if [ ! -d "/opt/qcom/PCAT" ]; then
        die "PCAT directory does not exist, please check PCAT installation."
    fi
}

check_quts_service() {
    # Check if the process is running
    if pgrep -x "QUTSService" >/dev/null; then
      echo "The process QUTSService is already running."
    else
      # If the process is not running, start the application
      echo "Starting QUTSService..."
      /opt/qcom/QUTS/bin/QUTSService &
      echo "QUTSService has been started."
    fi
}

pcat_command() {
    echo $1
    start_time=`date +%s`
    # Killing existing pkill without creating an error using true
    pkill -x 'QUTS' || true > /dev/null
    $1 |& while IFS= read -r line; do
        echo $line
        if echo "$line" | grep -q "ERROR"; then
            die "Error detected: $line"
        fi
    done
    end_time=`date +%s`
    echo "======> Process Successful - `expr $end_time - $start_time` s"
    echo "======> Waiting for platform to be ready for next stage... <======"
}

build_verification() {
    # Check if build is a flat build or regular META
    if [ -e "$build_dir/contents.xml" ]; then
        echo "Build is a META..."
        set_flash_params true
        cdt_ufs_prep
        build_env=$build_dir/contents.xml
        is_build_meta=true
    else
        echo "Build is a flat build..."
        set_flash_params false
        build_env=$build_dir
        is_build_meta=false
    fi
}

flashing_precheck
build_verification
check_quts_service

# PCAT Devices and store IDs
if [ -x lsusb ]; then
    QDL_exists=$(lsusb | grep -i "QDL")
    if [ -z "$QDL_exists" ]; then
        die "QDL Device does not exist, please verify USB connection, and QUTS/PCAT installation"
    fi
fi
echo "Looking for devices in EDL Mode..."
pcat_devices=$(pcat -devices | grep "EDL" | cut -d "|" -f 4 | tr -d " " )
echo $pcat_devices

if [ ! -z "$pcat_devices" ]
then
    echo "Device detected in EDL mode!"
    for PCAT_DEV in $pcat_devices
    do
        if [ "$is_build_meta" = true ]; then
            echo "======> Creating Flat Build via PCAT... this may take 3-5 Minutes <======"
            echo "Flattening SAIL..."
            [ -d $build_dir/FLAT_BUILD/SAIL ] || mkdir -p $build_dir/FLAT_BUILD/SAIL
            pcat_command "pcat -flatten -build $build_dir/contents.xml -memorytype spinor -flavor $SAIL_TYPE -OUT $build_dir/FLAT_BUILD/SAIL"
            
            echo "Flattening Main Domain..."
            [ -d $build_dir/FLAT_BUILD/MD ] || mkdir -p $build_dir/FLAT_BUILD/MD
            pcat_command "pcat -flatten -build $build_dir/contents.xml -memorytype $MEM_TYPE -flavor $PRODUCT_FLAVOR -OUT $build_dir/FLAT_BUILD/MD"
            
        fi

        echo "======> Erasing Software on $PCAT_DEV... this may take 3-5 Minutes <======"
        pcat_command "pcat -plugin sd -device $PCAT_DEV -memorytype $MEM_TYPE -deviceprog $PROG_DIR/prog_firehose_ddr.elf -SLOT 0 -ERASE TRUE -PARTITIONINDEX ${partition_index}"

        if [ "$is_build_meta" = true ]; then
            echo "======> Provisioning UFS via PCAT for $PCAT_DEV... this may take 3-5 Minutes <======"
            pcat_command "pcat -plugin sd -device $PCAT_DEV -build $PROG_DIR -memorytype $MEM_TYPE -flavor $PRODUCT_FLAVOR -ufsprov true -ufsprovxml $PROG_DIR/provision_default.xml -deviceprog $PROG_DIR/prog_firehose_ddr.elf -reset false"
            
            echo "======> Provisioning CDT via PCAT for $PCAT_DEV... this may take 3-5 Minutes <======"
            pcat_command "pcat -plugin sd -device $PCAT_DEV -build $build_env -memorytype $MEM_TYPE -flavor $PRODUCT_FLAVOR -rawprog $PROG_DIR/rawprogram${platform_prefix}.xml -patchprog $PROG_DIR/patch${platform_prefix}.xml -deviceprog $PROG_DIR/prog_firehose_ddr.elf -reset false"
        fi
        sleep 30
        echo "======> Flashing META via PCAT for $PCAT_DEV... this may take 3-5 Minutes <======"
        pcat_command "pcat -plugin sd -device $PCAT_DEV -build $build_env -memorytype $MEM_TYPE -flavor $PRODUCT_FLAVOR -reset false"

        echo "======> Flashing SAIL via PCAT for $PCAT_DEV... this may take 3-5 Minutes <======"
        pcat_command "pcat -plugin sd -device $PCAT_DEV -build $build_env -memorytype spinor -flavor $SAIL_TYPE -deviceprog $PROG_DIR/prog_firehose_ddr.elf -erase true"
        sleep 20
    done
    pcat_flash_flag=true
else
    die "No devices found in EDL, please put device in EDL mode before flashing."
fi

if [ "$pcat_flash_flag" = true ]; then
    # Controller/Console looks for this to determine success.
    echo "Flash build is successful"
else
    die "Flash build unsuccessful, check logs..."
fi
