#!/usr/bin/env python3
#
#
# Example for using the EPM interface
#

import serial
import time

# Please specify the COM port the EPM interface is using
port='/dev/ttyACM0'
# port='/dev/cu.usbmodem146402'

epm = serial.Serial(port, 115200)

epm.write('devicePower 0\r'.encode('ascii'))
time.sleep(0.5)
epm.write('pin 1 31\r'.encode('ascii'))
time.sleep(0.05)
epm.write('devicePower 1\r'.encode('ascii'))
time.sleep(0.1)
epm.write('ttl outputBit 1 1\r'.encode('ascii'))
time.sleep(0.5)
epm.write('ttl outputBit 1 0\r'.encode('ascii'))
time.sleep(0.5)
epm.write('pin 0 31\r'.encode('ascii'))
