#!/usr/bin/env python3
#
#
# Example for using the EPM interface
#

import serial
import time

# Please specify the COM port the EPM interface is using
port='/dev/ttyACM0'

epm = serial.Serial(port, 115200)

epm.write('devicePower 0\r'.encode('ascii'))
time.sleep(0.5)
epm.write('ttl outputBit 1 0\r'.encode('ascii'))
time.sleep(0.1)
epm.write('gpio volup 0\r'.encode('ascii'))
time.sleep(0.01)
epm.write('ttl outputBit 2 1\r'.encode('ascii'))
time.sleep(0.1)
epm.write('ttl outputBit 4 0\r'.encode('ascii'))
time.sleep(0.5)
epm.write('devicePower 1\r'.encode('ascii'))
time.sleep(0.9)
epm.write('ttl outputBit 1 1\r'.encode('ascii'))
time.sleep(0.8)
epm.write('ttl outputBit 1 0\r'.encode('ascii'))
time.sleep(8.1)
epm.write('ttl outputBit 2 0\r'.encode('ascii'))


