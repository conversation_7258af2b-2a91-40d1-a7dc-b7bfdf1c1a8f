#=============================================================================
# FILE:         SystemWidePowerOff.py
#
# OVERVIEW:     ALPACA script for controlling TAC for Automotive platforms.
#
# SUPPORT:      E-mail: <EMAIL>
#               SharePoint: go/alpaca
#
# DEPENDENCIES: PySerial
#
#               Copyright (c) 2016-2017 Qualcomm Technologies, Inc.
#               All Rights Reserved.
#               Qualcomm Technologies Proprietary and Confidential.
#=============================================================================
#=============================================================================
# EDIT HISTORY FOR MODULE
#
# 2020-07-27  msharma  Initial revision.
#
#===========================================================================*/
#-----------------------------------------------------------------------------
#  Include Files
#-----------------------------------------------------------------------------
import time
import sys

# Standalone script path has to be given.
from AutoAlpaca import AutoAlpaca

alpacaObj = AutoAlpaca()

# Using this object, any AutoAlpaca functionality can be accessible.
# Example: hardReset(), bootInFastboot(), powerOff(), powerOn(), and so on.


alpacaObj.powerOff()
