#=============================================================================
# FILE:         DeviceConnector.py
#
# OVERVIEW:     ALPACA script for controlling TAC for Automotive platforms.
#
# SUPPORT:      E-mail: <EMAIL>
#               SharePoint: go/alpaca
#
# DEPENDENCIES: PySerial
#
#               Copyright (c) 2016-2017 Qualcomm Technologies, Inc.
#               All Rights Reserved.
#               Qualcomm Technologies Proprietary and Confidential.
#=============================================================================
#=============================================================================
# EDIT HISTORY FOR MODULE
#
# 2020-07-27  msharma  added copyrights.
#
#===========================================================================*/

class DeviceConnector(object):
    '''
    Base class for device connectors(Alpaca, spiderboards etc.) that phyiscally connects a device to host
    '''
    vidpid = []

    def __init__(self, port, deviceId=None):
        '''
        Constructor
        '''
        self.port = port
        self.deviceId = deviceId

    def setDeviceId(self, deviceId):
        self.deviceId = deviceId

    def getDeviceId(self):
        return self.deviceId

    def connectUsb(self):
        '''
        Enable USB connection of device to host
        :return: None
        '''
        pass

    def disconnectUsb(self):
        '''
        Disconnect USB connection of device from host
        :return:
        '''
        pass

    def powerOn(self):
        '''
        Powers on the device
        :return: None
        '''
        pass

    def powerOff(self):
        '''
        Powers off the device
        :return: None
        '''
        pass

    def peripheralPowerOn(self):
        '''
        Turns On PeripheralPower
        :return: None
        '''
        pass

    def peripheralPowerOff(self):
        '''
        Turns Off PeripheralPower
        :return: None
        '''
        pass

    def enterEdl(self):
        '''
        Puts the device in emrgency download mode
        :return: None
        '''
        pass

    def exitEdl(self):
        '''
        Exits the Emergency Download mode
        :return: None
        '''
        pass

    def submitCommand(self, command):
        '''
        Give manual commands to the device connector
        :param command: command to submit
        :return: None
        '''
        pass

    def pressPowerButton(self,duration):
        '''
        Press power button of device using Device Connector
        :return: None
        '''
        pass

    def hardReset(self, timeout=None):
        '''
        Do a hard reset of device
        :return: None
        '''
        pass

    def disconnectGivenPorts(self,ports):
        '''
        Disconnects the passed in ports from the host cpu
        :param ports: port to disconnect
        :return: None
        '''
        for port in ports:
            port.disconnectUsb()
            
    def connectGivenPorts(self,ports):
        '''
        connects the given ports to the host cpu
        :param ports: port to connect
        :return: None
        '''
        for port in ports:
            port.connectUsb()
            
    def getOtherPorts(self,deviceConnectorPorts,port):
        '''
        Gets
        :param deviceConnectorPorts:
        :param port:
        :return:
        '''
        ports = list(set(deviceConnectorPorts) - set(port))
        return ports

    def pressVolumeDownButton(self, duration=0.1):
        '''
        Press Volume Down button
        :return:
        '''
        pass

    def getPort(self):
        '''
        Returns the port the connector is on
        :return: port
        '''
        return self.port

    def close(self):
        pass

    def getUniqueId(self):
        pass
    
    
    def pressVolumeUpButton(self, duration=0.1):
        '''
        Press volume up button
        :param duration: duration to hold the button
        :return: None
        '''
        pass
    
    def powerOnSim(self, slot):
        '''
        Powering up the sim
        :param slot: Slot 1 or 2 (:int)
        :return: None
        '''
        pass
        
    def powerOffSim(self, slot):
        '''
        Powering down the sim
        :param slot: Slot 1 or 2 (:int)
        :return: None
        '''
        pass
