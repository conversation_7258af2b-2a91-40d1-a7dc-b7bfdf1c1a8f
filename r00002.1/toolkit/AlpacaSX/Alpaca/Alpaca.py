import time
import logging
from .DeviceConnector import DeviceConnector
from .alpacaLib import tac
from .Decorators import retry
from .DeviceConnectorException import DeviceConnectorException

log = logging.getLogger(__name__)


class Alpaca(DeviceConnector):
    '''
    classdocs
    '''
    vidpid = ['05C6:9302']

    def __init__(self, port=None, deviceId=None):
        '''
        Constructor
        '''
        super().__init__(port, deviceId)
        self._tac = None

        # # Get basic information
        # IMPORTANT NOTE
        # Enabling Tac Port Here Cause Problem for pickling when you are using python multiprocessing, so added getter for tac which will work for multiprocessing.
        # self.port = self.tac.read("port_name")
        # log.info("Connected to Device Connector on " + self.port)

    @property
    def tac(self):

        if self._tac is None:
            self._tac = tac(self.port)
            self.port = self._tac.read("port_name")
            log.info("Connected to <PERSON>ce Connector on " + self.port)

        return self._tac

    @retry(DeviceConnectorException, attempts=3)
    def connectUsb(self):
        log.info('Connecting USB')
        self.tac.write("usb", '1')
        time.sleep(2)

    @retry(DeviceConnectorException, attempts=3)
    def disconnectUsb(self):
        log.info('Disconnecting USB')
        self.tac.write("usb", '0')
        time.sleep(2)

    def powerOn(self):
        log.info('Powering on device')
        self.tac.write("power", '1')
        time.sleep(1)

    def powerOff(self):
        log.info('Powering off device')
        self.tac.write("power", '0')
        time.sleep(1)

    def enterEdl(self):
        log.info('Entering EDL')
        self.tac.write('edl', '1')
        time.sleep(2)
        
    def powerOnSim(self, slot):
        log.info('Powering on Sim ' + str('slot'))
        cmd = 'control_' + str(slot)
        self.tac.write(cmd, '0')
        time.sleep(2)
        
    def powerOffSim(self, slot):
        log.info('Powering off Sim ' + str('slot'))
        cmd = 'control_' + str(slot)
        self.tac.write(cmd, '1')
        time.sleep(2)

    def exitEdl(self):
        log.info('Exiting EDL')
        self.tac.write('edl', '0')
        time.sleep(2)

    def submitCommand(self, command):
        log.info('Sending command - ' + command)
        self.tac.write_command(command)
        time.sleep(2)

    def pressPowerButton(self,duration=0.1):
        self.tac.press_button("power_key",duration)
        time.sleep(1)

    def pressVolumeDownButton(self, duration=0.1):
        self.tac.press_button("volume_down", duration)
        time.sleep(1)

    def hardReset(self, timeout=None):
        if timeout:
            log.warning('Ignoring the timeout parameter')
        log.info('Doing a hard reset of device')
        self.disconnectUsb()
        self.powerOff()
        time.sleep(5)
        self.powerOn()
        self.connectUsb()
        time.sleep(60)
        
    def bootInEdl(self):
        self.tac.boot_edl()

    def close(self):
        self.tac.close()

    def getUniqueId(self):
        return self.tac.read("uuid")
    
    def pressVolumeUpButton(self, duration=0.1):
        self.tac.press_button("volume_up", duration)
        time.sleep(1)
