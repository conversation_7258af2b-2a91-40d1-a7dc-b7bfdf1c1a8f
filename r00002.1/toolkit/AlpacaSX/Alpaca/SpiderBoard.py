#=============================================================================
# FILE:         SpiderBoard.py
#
# OVERVIEW:     ALPACA script for controlling TAC for Automotive platforms.
#
# SUPPORT:      E-mail: <EMAIL>
#               SharePoint: go/alpaca
#
# DEPENDENCIES: PySerial
#
#               Copyright (c) 2016-2017 Qualcomm Technologies, Inc.
#               All Rights Reserved.
#               Qualcomm Technologies Proprietary and Confidential.
#=============================================================================
#=============================================================================
# EDIT HISTORY FOR MODULE
#
# 2020-07-27  msharma  added copyrights.
#
#===========================================================================*/
import logging
import time

from .Alpaca import Alpaca


log = logging.getLogger(__name__)

class SpiderBoard(Alpaca):
    
    vidpid = ['16C0:0483']
    
    def powerOn(self):
        log.info('Turning On PeripheralPower')
        self.tac.write("peripheralPower", '1')
        time.sleep(5)

    def powerOff(self):
        log.info('Turning Off PeripheralPower')
        self.tac.write("peripheralPower", '0')
        time.sleep(2)
        