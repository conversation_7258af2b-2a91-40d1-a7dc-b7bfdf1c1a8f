'''
Created on Mar 29, 2018

@author: ramamurt
'''
#=============================================================================
# FILE:         Decorators.py
#
# OVERVIEW:     ALPACA script for controlling TAC for Automotive platforms.
#
# SUPPORT:      E-mail: <EMAIL>
#               SharePoint: go/alpaca
#
# DEPENDENCIES: PySerial
#
#               Copyright (c) 2016-2017 Qualcomm Technologies, Inc.
#               All Rights Reserved.
#               Qualcomm Technologies Proprietary and Confidential.
#=============================================================================
#=============================================================================
# EDIT HISTORY FOR MODULE
#
# 2020-07-27  msharma  added copyrights.
#
#===========================================================================*/
from functools import wraps
import logging
import time
import signal

log = logging.getLogger(__name__)

def retry(exceptionToCheck, attempts = 10, delay = 1, backoff = 1):
    '''
    retry the decorated function
    :param exceptionToCheck: Exceptions to check
    :param attempts: number of times to try
    '''
    def decoRetry(function):
        @wraps(function)
        def funRetry(*args, **kwargs):
            mdelay = delay
            retryAttempts = attempts
            while retryAttempts >= 1:
                try:
                    return function(*args, **kwargs)
                except exceptionToCheck as e:
                    msg = "{} - Retrying again in {} seconds.".format(str(e), mdelay)
                    log.warning(msg)
                    time.sleep(mdelay)
                    mdelay *= backoff
                    retryAttempts -= 1
            return function(*args, **kwargs)
        return funRetry
    return decoRetry


def timeout(seconds=240):
    def decorator(func):
        def _handlerForTimeout():
            log.warn('Timed out after 240 seconds')

        @wraps(func)
        def wrapper(*args, **kwargs):
            signal.signal(signal.SIGALRM, _handlerForTimeout)
            signal.alarm(seconds)
            try:
                result = func(*args, **kwargs)
            finally:
                signal.alarm(0)
            return result

        return wrapper

    return decorator