"""
AutoAlpaca class
- AutoAlpaca for controlling the Alpaca board.
"""
#=============================================================================
# FILE:         AutoAlpaca.py
#
# OVERVIEW:     ALPACA script for controlling TAC for Automotive platforms.
#
# SUPPORT:      E-mail: <EMAIL>
#               SharePoint: go/alpaca
#
# DEPENDENCIES: PySerial
#
#               Copyright (c) 2016-2017 Qualcomm Technologies, Inc.
#               All Rights Reserved.
#               Qualcomm Technologies Proprietary and Confidential.
#=============================================================================
#=============================================================================
# EDIT HISTORY FOR MODULE
#
# 2020-07-27  msharma  Initial revision.
# 2020-12-10  jaikanth  Added Scripts to Poweroff/Poweron
#
#===========================================================================*/
#-----------------------------------------------------------------------------
#  Include Files
#-----------------------------------------------------------------------------
import time
import logging
from Alpaca import Alpaca

log = logging.getLogger(__name__)

class AutoAlpaca(Alpaca.Alpaca):
	def __init__(self, port=None):
		log.info("Initializing Automotive Alpaca")
		super().__init__(port)

	def setPowerTTL(self):
		log.info("Setting powerTTL")
		self.tac.write('power_key', 1)
		time.sleep(.1)

	def clearPowerTTL(self):
		log.info("Clearing powerTTL")
		self.tac.write('power_key', 0)
		time.sleep(2)

	def powerOff(self):
				log.info("Powering off the system")
				self.tac.write('volume_down', 0)
				self.tac.write('power', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('pshold', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('resetsec', 0)
				self.tac.write('edl', 0)

	def powerOn(self):
				log.info("Powering on the system")
				self.tac.write('power', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('pshold', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('resetsec', 0)
				self.tac.write('edl', 0)
				time.sleep(0.2)
				self.tac.write('power', 1)
				time.sleep(0.5)
				self.tac.write('power_key', 1)
				time.sleep(1.5)
				self.tac.write('power_key', 0)
				time.sleep (1)

	def setBoardName(self, name):
				log.info("Set board name")
				self.tac.write('setname', name)

	def getBoardName(self):
				log.info("Get board name")
				return self.tac.read('getname')

	def setBoardUUID(self, name):
				log.info("Set board UUID")
				self.tac.write('setid', name)

	def getBoardUUID(self):
				log.info("Get board UUID")
				return self.tac.read('uuid')

	def isSOC1Up(self):
				log.info("Get SOC1 Status")
				if (self.tac.read('monitor_1') == '0'):
					#TAC_S1_RDY is ACTIVE LOW
					return 1
				else:
					return 0

	def isSOC2Up(self):
				log.info("Get SOC2 Status")
				if (self.tac.read('monitor_2') == '0'):
					#TAC_S2_RDY is ACTIVE LOW
					return 1
				else:
					return 0

	def isFanGood(self):
				log.info("Get Fan Fault Status")
				if (self.tac.read('monitor_3') == '0'):
					# FAN GOOD is ACTIVE LOW
					return 1
				else:
					return 0

	def resetSOC1(self):
				log.info("Resetting SOC2 only")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('edl', 0)
				self.tac.write('pshold', 0)
				time.sleep(0.5)
				self.tac.write('start', 1)
				time.sleep(1)
				self.tac.write('start', 0)
				time.sleep(0.3)

	def resetSOC2(self):
				log.info("Resetting SOC1 only")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('edl', 0)
				self.tac.write('pshold', 0)
				time.sleep(0.5)
				self.tac.write('pshold', 1)
				time.sleep(1)
				self.tac.write('pshold', 0)
				time.sleep(0.3)

	def bootInFastboot(self):
				log.info("Booting SOC1 into fastboot mode")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('resetsec', 0)
				self.tac.write('edl', 0)
				self.tac.write('pshold', 0)
				self.tac.write('volume_down', 1)
				time.sleep(.8)
				self.tac.write('start', 1)
				time.sleep(.3)
				self.tac.write('start', 0)
				time.sleep(15)
				self.tac.write('volume_down', 0)
				time.sleep(0.3)

	def bootInFastbootSecondary(self):
				log.info("Booting SOC2 into fastboot mode")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('resetsec', 0)
				self.tac.write('edl', 0)
				self.tac.write('resetsec', 1)
				time.sleep(.8)
				self.tac.write('pshold', 1)
				time.sleep(.3)
				self.tac.write('pshold', 0)
				time.sleep(15)
				self.tac.write('resetsec', 0)
				time.sleep(0.3)

	def bootInFastbootAll(self):
				log.info("Booting both SOCs into fastboot mode")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('power_key', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('edl', 0)
				self.tac.write('pshold', 0)
				self.tac.write('volume_down', 1)
				self.tac.write('resetsec', 1)
				time.sleep(.8)
				self.tac.write('start', 1)
				self.tac.write('pshold', 1)
				time.sleep(.3)
				self.tac.write('start', 0)
				self.tac.write('pshold', 0)
				time.sleep(15)
				self.tac.write('volume_down', 0)
				self.tac.write('resetsec', 0)
				time.sleep(0.3)

	def bootInEDL(self):
				log.info("Booting SOC1 to EDL mode")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('start', 0)
				self.tac.write('edl', 1)
				time.sleep(0.5)
				self.tac.write('start', 1)
				time.sleep(2)
				self.tac.write('start', 0)
				time.sleep(3)
				self.tac.write('edl', 0)

	def bootInEDLSecondary(self):
				log.info("Booting SOC2 to EDL mode")
				self.tac.write('start', 0)
				self.tac.write('usb', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('pshold', 0)
				self.tac.write('swdwnldsec', 1)
				time.sleep(0.5)
				self.tac.write('pshold', 1)
				time.sleep(2)
				self.tac.write('pshold', 0)
				time.sleep(3)
				self.tac.write('swdwnldsec', 0)
				time.sleep(1)


	def bootInEDLAll(self):
				log.info("Booting both SOCs to EDL mode")
				self.tac.write('start', 0)
				self.tac.write('pshold', 0)
				self.tac.write('usb', 0)
				self.tac.write('volume_up', 0)
				self.tac.write('volume_down', 0)
				self.tac.write('edl', 1)
				self.tac.write('swdwnldsec', 1)
				time.sleep(0.5)
				self.tac.write('start', 1)
				self.tac.write('pshold', 1)
				time.sleep(2)
				self.tac.write('start', 0)
				self.tac.write('pshold', 0)
				time.sleep(3)
				self.tac.write('edl', 0)
				time.sleep(3)
				self.tac.write('swdwnldsec', 0)
				time.sleep(1)

	def poweroffSOC1(self):
				time.sleep(0.5)
				self.tac.write('start', 1)


	def poweroffSOC2(self):
				time.sleep(0.5)
				self.tac.write('pshold', 1)

	def poweronSOC1(self):
				time.sleep(1)
				self.tac.write('start', 0)
				time.sleep(0.3)

	def poweronSOC2(self):
				time.sleep(1)
				self.tac.write('pshold', 0)
				time.sleep(0.3)





