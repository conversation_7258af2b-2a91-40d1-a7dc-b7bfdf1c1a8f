#!/bin/bash -e

# Copyright (c) 2022-2025 Qualcomm Technologies, Inc.
# All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.



# Exit with error
die() {
    echo $1
    exit 1
}

usage() {
    echo "$0 "
    echo " "
    echo "Installer Requirements:"
    echo "-Local SUDO access"
    echo " "
    echo "options:"
    echo "--help       Shows this menu"
    echo "--installdir        Specify installation directory if different than current"
    echo "--default    Execute setup without prompts and using defaults"
    exit 1
}


DEFAULT_FLAG=false
timestamp=$(date +%Y%m%d_%H%M)

while [[ $# -gt 0 ]]; do
  key="$1"

  case $key in
    --help)
      usage
      shift
      ;;
    --installdir)
      BUNDLE_DIR=$2
      shift 
      shift 
      ;;
    --default)
      DEFAULT_FLAG=true
      shift
      ;;
    *)
      POSITIONAL+=("$1") 
      shift 
      ;;
  esac
done


if [ -z "$BUNDLE_DIR" ]; then 
    BUNDLE_DIR=$PWD
fi

if [ -z "$RIDESDK_RUNTIME_DIR" ]; then 
    export RIDESDK_RUNTIME_DIR=/local/mnt/rideconsole/runtime
fi

cd $BUNDLE_DIR
exec > >(tee -i ./bundle_setup.log)
exec 2>&1

[ ! -d "$RIDESDK_RUNTIME_DIR" ] && sudo mkdir -p "$RIDESDK_RUNTIME_DIR"
if [ ! -d "$RIDESDK_RUNTIME_DIR" ]; then
    echo "Bundle install failed, cannot locate RIDESDK_RUNTIME_DIR=$RIDESDK_RUNTIME_DIR"
    exit 1
fi



export BUNDLE_PKG_DIR=pkg
BUNDLE_RUNTIME_DIR=$BUNDLE_DIR/runtime
BUNDLE_SCRIPTS_DIR=$BUNDLE_DIR/scripts

PROFILES_DIR=$BUNDLE_RUNTIME_DIR/config/profiles
if [ -d "$PROFILES_DIR" ]; then
    echo "Copying profiles to $RIDESDK_RUNTIME_DIR"
    sudo cp -r $PROFILES_DIR $RIDESDK_RUNTIME_DIR || die "Error copying profiles to $RIDESDK_RUNTIME_DIR"
fi

STACK_SDK_FILE=$(find $BUNDLE_PKG_DIR -name qride-stack-sdk-*.tar.gz -print -quit | awk -F/ '{print $NF}')
if [ -n "$STACK_SDK_FILE" ]; then
    echo "Extracting the STACK SDK files"
    $BUNDLE_PKG_DIR/stack-sdk/setup-stack-sdk.sh
fi

RIDEHAL_FILE=$(find $BUNDLE_PKG_DIR -name ridehal*.tar.gz -print -quit | awk -F/ '{print $NF}')
if [ -n "$RIDEHAL_FILE" ]; then
    echo "Extracting the RideHAL files"
    $BUNDLE_PKG_DIR/ridehal/setup-ridehal.sh
fi


echo "Updating permissions..."
sudo chown -R $USER $BUNDLE_DIR
sudo chgrp -R $(id -g -n) $BUNDLE_DIR
find $BUNDLE_DIR -type f -iname "*.sh" -exec chmod +x {} \;

if [ -d $BUNDLE_DIR/toolchain ]; then
    echo "@@@ Building the Ride SDK Toolchain Docker @@@"
    cd $BUNDLE_DIR/toolchain
    ./build-build.sh || die "ERROR: Docker creation failed."
fi

SPECIFIC_SETUP=bundle_specific_setup.sh
if [ -f $BUNDLE_SCRIPTS_DIR/$SPECIFIC_SETUP ]; then
    cd $BUNDLE_DIR
    $BUNDLE_SCRIPTS_DIR/$SPECIFIC_SETUP
fi
