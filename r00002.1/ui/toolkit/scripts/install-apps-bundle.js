// Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// Easier to use a template to build out table to select apps to install.
// entry [ <id>, <display name>, <optional comment>, <unused> ]
var installAppsEntries = [
    [ "updateSocTime", "Update Time", "Set time on the SOC according to server system time." ],
    [ "miscPatch", "Misc. Patches", "Any miscellaneous patch commands in ride-console/bundle/scripts/patch.sh" ],
    [ "platformReset", "Platform Reset", "" ],
];
