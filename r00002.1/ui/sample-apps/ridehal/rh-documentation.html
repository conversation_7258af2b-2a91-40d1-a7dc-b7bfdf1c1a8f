<div class="faq text">
    <h2>Overview</h2>
    
    <p>RideHal is the Qualcomm ADAS hardware abstraction layer that provides user friendly APIs
       to access the Qualcomm ADAS hardware accelerator.  It is an ADAS oriented service that provides
       user friendly components and utils to cover the perception and compute vision related tasks.
       RideHal is configured using commandline parameters.  Often a shell script file is used to run
       RideHal with a particular usecase configuration.
    </p>
    
    <p>The Custom Camera Pipeline UI is intended to be a user friendly UI to assist in the creation
       of a RideHal configuration for a specified use case.  The UI generates a RideHal config shell
       script.
    </p>
    
    <p>The version of RideHal included is built with a specific version of QNN.  Click the "About" link
       at the bottom of the page to see the version.
    </p>
    
    <h2>Disclaimers</h2>
    
    <ul>
        <li>
            <p>The configuration UI is not intended to support every possible configuration that RideHal supports.
               It is intended for novice and casual users to ramp up using RideHal more quickly.
               As users become familiar with the shell script parameters, they can further enhance configurations
               manually as needed.
            </p>
        </li>

        <li>
            <p>Manually created or modified config scripts cannot be read into the UI.  The UI maintains a separate JSON
               metadata file from which the shell script is generated and config profiles later read back.
            </p>
        </li>

        <li>
            <p>The UI generates config shell script assuming RideHal is installed on the platform in a particular directory.
            </p>
        </li>

        <li>
            <p>The config shell script generated by the UI is not guaranteed to run with RideHal depending on the underlying
               library limitations such as format, resolution and other resources.  The UI will indicate a few config errors or warnings
               but cannot catch everything.
            </p>
        </li>
    </ul>
    
    <h2>Manage Configuration</h2>

    <p>The Manage Configuration tab is where config profiles can be created, modified, viewed, cloned, deleted,
       or deployed to target.  Only one config can be deployed to the target a time.
    </p>
    
    <p>A config profile contains 1 or more pipelines which are defined by 1 or more RideHal components.  A typical
       pipeline contains an input component such as a Camera or Data Reader, PreProcessing, Inference.
    </p>
    
    <h2>Logging</h2>
    
    <p>RideHal sends log messages to the corresponding platform system logger which will be intermixed with
       messages from other platform processes.  A parameter determines what level of message is logged.
       A global level sets the log level for all RideHal components.  RideHal does have ability to configure per
       component instance log levels.  RideHal has an option to also send only its log messages to stdout.
       This option is useful when RideHal has initialization errors, often due to a configuration error or
       something is not supported.
    </p>
    
    <h2>QNN Test</h2>

    <p>When configuring a pipeline, it can be useful to know the exact input and output tensor definitions a
       network model is expecting.  QNN Test is a tool, provided by RideHal, which shows such network information.
       Select the desired network and click Analyze.  Networks compiled with a version other than the QNN
       version built with RideHal may or may not be compatible for analysis.  If the network cannot be analyzed,
       it will not work with this version of RideHal.
    </p>
    
    <p>If compatible, the analysis output will have lines that start with "[QNN0]".  For example,
    </p>
    <pre>[QNN0] input 0 name=input type=14 dims=[1, 800, 1152, 3, ] scale=0.0186584 offset=-114
    </pre>
    <p>indicates the network expects the first input to be width=1152, height=800, format=rgb.  This information could
       be entered in a pipeline under PreProc / Target.  Or it can be used to define a Data Reader Tensor.
    </p>
    
    <p>Note QNN Test acts like a deployed config profile, meaning it replaces any config that was deployed.
    </p>
    
    <h2>Visualization</h2>
    
    <p>RideHal has a visualization option.  A display must be connected to the displayport on the platform.
       Only Cameras and Data Readers with an mpeg file can be visualized.
    </p>
    <ul>
        <li>8650 ******* HGY connect to "eDP0"
        <li>8650 *******.1 HQX connect to "DSI0"
    </ul>
    
    <h2>Run Profile on Platform</h2>
    
    <p>Once a config profile is defined, in order to run it, first Deploy it to the platform (from Manage Configuration tab).
       If any Data Readers are defined with mpeg file, they will be processed and image dataset
       files created.  Data sets and any networks are transferred to the platform.
    </p>
    
    <p>The UI will show what config profile is currently deployed if any.  It will also indicate when it was deployed in
       case it is older than the timestamp shown on the Manage Configuration tab.
    </p>
    
    <p>From the "Run Profile on Platform" tab, user may "Start" or "Stop" the deployed config profile.
       For each component defined in the config, the output should indicate a corresponding "Init" and "Start"
       followed by periodic KPI.  Note a component only shows KPI if it is running and receiving data.
       Just because you see some component KPI, do not assume all components are working.
    </p>
    <pre>
Init 0-DR-TENSOR OK
Init 0-INFER OK
Init 1-DR-TENSOR OK
Init 1-INFER OK
Start 0-DR-TENSOR OK
Start 0-INFER OK
Start 1-DR-TENSOR OK
Start 1-INFER OK
1-DR-TENSOR : FPS=30.370 AVG=0.011 MIN=0.007 MAX=0.221 COUNT=152
1-INFER : FPS=30.333 AVG=5.753 MIN=5.570 MAX=22.596 COUNT=152
0-DR-TENSOR : FPS=15.317 AVG=0.011 MIN=0.007 MAX=0.084 COUNT=77
0-INFER : FPS=15.302 AVG=5.852 MIN=5.563 MAX=21.799 COUNT=77
    </pre>
    <p>For example, if component 0-DR-TENSOR has a problem, if it is not feeding data
       into 0-INFER as expected, then 0-INFER will not show any KPI either.
    </p>
    
</div>
