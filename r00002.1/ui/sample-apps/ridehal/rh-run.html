<script type="text/javascript">
    addBundleStylesheetToElement("ui/sample-apps/ridehal/styles/rh-config.css", "#rh-run-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-run.js", "#rh-run-tab");
</script>

<div class="run-profile-container">
    <div class="banner">
        <table class="banner-container">
            <tr>
                <td><img src="icons/nav/info.svg" class="banner-icon deployed-icon" /></td>
                <td class="banner-message deployed-message" id="deployedProfileMeta">No config detected on platform.
                    Please deploy a profile first.  Only one profile can be deployed at a time.
                </td>

                <td>
                    <button type="button" class="btn run-profile-button" id="rideHalStart">
                        <img src="icons/av/play.svg" class="run-profile-icon" />
                        <span class="button-label">Start</span>
                    </button>
                </td>
                <td>
                    <button type="button" class="btn run-profile-button" id="rideHalStop">
                        <img src="icons/av/stop.svg" class="run-profile-icon" />
                        <span class="button-label">Stop</span>
                    </button>
                </td>
            </tr>
        </table>
    </div>

    <div class="kpi-output pt-3">
        <div class="text-heading">
            <span>Output</span>
        </div>
    
        <div class="progress-container" id="sampleAppsDemoProgressContainer" style="display: none;">
            <div class="progress-banner" id="progressBanner">
                <div class="progress-banner-container">
                    <div class="progress-bar-and-message">
                        <div class="progress">
                            <div class="progress-bar indeterminate" role="progressbar" aria-valuenow="25" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <div class="log-box">
                <div id="sampleAppsOutput" class="log-output text-wrap scrollbar"></div>
            </div>
        </div>
    </div>

</div>
