// Copyright (c) 2022-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.


var lastOutputUpdateMs = 0;


checkInstalledOnSoc("ridehal", 1);


// get currently deloyed profile (from rh-config-manage.js)
// assume need to go through Manage to get to Run
document.getElementById("deployedProfileMeta").innerHTML = deployedProfileMeta;


$("#rideHalStart").click(function () {
    console.info("click Start");
    rideHalStart();
});


function rideHalStart() {
    $('#sampleAppsDemoProgressContainer').css("display", "block");
    $("#sampleAppsOutput").empty();
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/startDeployedProfile",
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        $('#sampleAppsDemoProgressContainer').hide();
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess("Start App command sent.");
        } else {
            utilsMessageError("Error starting app. " + response["err_msg"] + " Please check rideconsole.log");
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsDemoProgressContainer').hide();
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });    
}


$("#rideHalStop").click(function () {
    console.info("click Stop");
    rideHalStop();
});


function rideHalStop() {
    $('#sampleAppsDemoProgressContainer').css("display", "block");
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/stopApp",
        dataType: "json",
        timeout: 90000, // milliseconds
    });

    ajaxCall.done(function (response) {
        $('#sampleAppsDemoProgressContainer').hide();
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess("Stopped app on platform.");
        } else {
            utilsMessageError("Error stopping app. " + response["err_msg"] + " Please check rideconsole.log");
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsDemoProgressContainer').hide();
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}

