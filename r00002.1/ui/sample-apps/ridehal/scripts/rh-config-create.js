// Copyright (c) 2023-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// This file contains functions used to generate RideHal config from UI input.
// This file focuses on creating profile meta in JSON.

// Component names could be used in shell var names.  So cannot start with number.
// Cannot contain hyphen.


// Original design was different pipeline types for camera and lidar.
// But for now can probably handle as different input type of the pipeline.
var PIPELINE_TYPE_CAMERA = "camera";

var INPUT_TYPE_DATA_READER = "datareader";
var INPUT_TYPE_CAMERA = "camera";

var DATA_READER_TYPE_IMAGE = "image";
var DATA_READER_TYPE_TENSOR = "tensor";


function getPluginInputName(pipelineIndex) {
    let result = "P" + pipelineIndex + "_UNKNOWN_TYPE";
    switch (document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value) {
        case INPUT_TYPE_DATA_READER:
            switch (document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderType").value) {
                case DATA_READER_TYPE_IMAGE:
                    result = "P" + pipelineIndex + "_DR_IMAGE";
                    break;
                case DATA_READER_TYPE_TENSOR:
                    result = "P" + pipelineIndex + "_DR_TENSOR";
                    break;
            }
            break;
        case INPUT_TYPE_CAMERA:
            result = "CAM_" + document.getElementById(pipelineIndex + ID_DELIMITER + "cameraChannel").value;
            break;
    }
    return result;
}


function getPluginInputTopic(pipelineIndex) {
    let result = "/topic/";

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value == INPUT_TYPE_DATA_READER) {
        result += "DataReader";
    } else {
        result += "Camera";
    }
    
    result += "/" + getPluginInputName(pipelineIndex);

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value == INPUT_TYPE_CAMERA) {
        result += "/" + document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
        result += "/" + document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;
        result += "/" + document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;
    }
    return result;
}


function getPluginPreProcName(pipelineIndex) {
    let result = "P" + pipelineIndex + "_" + document.getElementById(pipelineIndex + ID_DELIMITER + "preProcType").value;
    return result;
}


function getPluginPreProcTopic(pipelineIndex) {
    let result = "/topic/";
    result += document.getElementById(pipelineIndex + ID_DELIMITER + "preProcType").value;
    result += "/" + getPluginPreProcName(pipelineIndex);
    return result;
}


function getPluginInferName(pipelineIndex) {
    let result = "P" + pipelineIndex + "_INFER";
    return result;
}


function getPluginInferTopic(pipelineIndex) {
    let result = "/topic/";
    result += "Qnn";
    result += "/" + getPluginInferName(pipelineIndex);
    return result;
}


function getPluginPostProcName(pipelineIndex) {
    let result = "P" + pipelineIndex + "_POSTPROC";
    return result;
}


function getPluginPostProcTopic(pipelineIndex) {
    let result = "/topic/";
    result += document.getElementById(pipelineIndex + ID_DELIMITER + "postProcType").value;
    result += "/" + getPluginPostProcName(pipelineIndex);
    return result;
}


function createCamera(pipelineIndex) {
    let componentObj = {};
    let inputType = document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value;
    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = "Camera";
    componentObj["name"] = getPluginInputName(pipelineIndex);
    componentObj["channel"] = parseInt(document.getElementById(pipelineIndex + ID_DELIMITER + "cameraChannel").value);
    componentObj["num_streams"] = "1";          // assume only one for now
    componentObj["width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
    componentObj["height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;
    componentObj["format"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;
    componentObj["isp_use_case"] = document.getElementById(pipelineIndex + ID_DELIMITER + "cameraIspUseCase").value;
    componentObj["request_mode"] = document.getElementById(pipelineIndex + ID_DELIMITER + "cameraRequestMode").value;
    componentObj["enable_visualization"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputEnableVisualization").value;

    componentObj["output_topic"] = getPluginInputTopic(pipelineIndex);

    return componentObj;
}


function createDataReader(pipelineIndex) {
    let componentObj = {};
    let inputType = document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value;

    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = "DataReader";
    componentObj["name"] = getPluginInputName(pipelineIndex);
    
    componentObj["frame_rate"] = parseInt(document.getElementById(pipelineIndex + ID_DELIMITER + "inputFrameRate").value);
    
    componentObj["data_reader_type"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderType").value;
    if (componentObj["data_reader_type"] == DATA_READER_TYPE_TENSOR) {
        componentObj["tensor_type"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderTensorType").value;
        componentObj["tensor_dims"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderTensorDims").value;
    } else {
        componentObj["width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
        componentObj["height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;
        componentObj["format"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;

        componentObj["video_file"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderVideoFile").value;
        componentObj["video_fps"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderVideoFps").value;
        componentObj["video_start_sec"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderVideoStartSec").value;
        componentObj["video_len_sec"] = document.getElementById(pipelineIndex + ID_DELIMITER + "dataReaderVideoLenSec").value;
        componentObj["video_width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
        componentObj["video_height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;
        componentObj["video_format"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;
    }
    componentObj["enable_visualization"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputEnableVisualization").value;

    componentObj["output_topic"] = getPluginInputTopic(pipelineIndex);

    return componentObj;
}


function createPreProc(pipelineIndex) {
    let componentObj = {};

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginPreProcEnabled").value !== "true") {
         return componentObj;
    }

    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = document.getElementById(pipelineIndex + ID_DELIMITER + "preProcType").value;
    componentObj["name"] = getPluginPreProcName(pipelineIndex);
 
    let modelWidth = document.getElementById(pipelineIndex + ID_DELIMITER + "targetWidth").value;
    let modelHeight = document.getElementById(pipelineIndex + ID_DELIMITER + "targetHeight").value;
    let inputWidth = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
    let inputHeight = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value
    
    if ((typeof inputWidth === "undefined") || (inputWidth <= 0)) {
        utilsMessageError("Input width must be > 0.");
        return;
    }
    
    if ((typeof inputHeight === "undefined") || (inputHeight <= 0)) {
        utilsMessageError("Input height must be > 0.");
        return;
    }
    
    if ((typeof modelWidth === "undefined") || (modelWidth <= 0)) {
        utilsMessageError("Network model width must be > 0.");
        return;
    }
    
    if ((typeof modelHeight === "undefined") || (modelHeight <= 0)) {
        utilsMessageError("Network model height must be > 0.");
        return;
    }

    let inputFormat = document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;
    let modelFormat = document.getElementById(pipelineIndex + ID_DELIMITER + "targetFormat").value;
    
    componentObj["source_width"] = inputWidth;
    componentObj["source_height"] = inputHeight;
    componentObj["source_format"] = inputFormat;

    // user configured
    let undistortionEnabled = false;
//    let undistortionEnabled = (document.getElementById(pipelineIndex + ID_DELIMITER + "UndistortionEnabled").value === "true");
    let roiEnabled = (document.getElementById(pipelineIndex + ID_DELIMITER + "ROICropEnabled").value === "true");

    let roiX = 0;
    let roiY = 0;
    let roiWidth = inputWidth;        // default full camera
    let roiHeight = inputHeight;

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginPreProcEnabled").value === "true") {
        if (roiEnabled) {
            roiX = document.getElementById(pipelineIndex + ID_DELIMITER + "roiX").value;
            roiY = document.getElementById(pipelineIndex + ID_DELIMITER + "roiY").value;
            roiWidth = document.getElementById(pipelineIndex + ID_DELIMITER + "roiWidth").value;
            roiHeight = document.getElementById(pipelineIndex + ID_DELIMITER + "roiHeight").value;

            // Add so easier to read back config and parse
            componentObj["roi_crop"] = "true";
            componentObj["roi_x"] = parseInt(roiX);
            componentObj["roi_y"] = parseInt(roiY);
            componentObj["roi_width"] = parseInt(roiWidth);
            componentObj["roi_height"] = parseInt(roiHeight);
        }

        if (undistortionEnabled) {
            componentObj["undistortion"] = "true";
            let remapX = document.getElementById(pipelineIndex + ID_DELIMITER + "undistortionRemapTableX").value;
            let remapY = document.getElementById(pipelineIndex + ID_DELIMITER + "undistortionRemapTableY").value;
            let remapTables = [];
            let remapEntry = [];
            if ((typeof remapX === "undefined") || (remapX === "") || (typeof remapY === "undefined") || (remapY === "")) {
                componentObj["enable_undistortion"] = "false";
            } else {
                componentObj["enable_undistortion"] = "true";
                remapEntry.push(remapX);
                remapEntry.push(remapY);
            }
            remapTables.push(remapEntry);
            componentObj["remap_tables"] = remapTables;
        }

        // Add so easier to read back config and parse
        componentObj["target_width"] = parseInt(modelWidth);
        componentObj["target_height"] = parseInt(modelHeight);
        componentObj["target_format"] = modelFormat;

        componentObj["input_topic"] = getPluginInputTopic(pipelineIndex);
        componentObj["output_topic"] = getPluginPreProcTopic(pipelineIndex);
    }

    return componentObj;
}


function createInference(pipelineIndex) {
    let componentObj = {};
    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = "Qnn";
    componentObj["name"] = getPluginInferName(pipelineIndex);
    
    componentObj["model_path"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inferenceModel").value;
    componentObj["backend_core"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inferenceBackendCore").value;

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginPreProcEnabled").value === "true") {
        componentObj["input_topic"] = getPluginPreProcTopic(pipelineIndex);
    } else {
        componentObj["input_topic"] = getPluginInputTopic(pipelineIndex);
    }

    componentObj["output_topic"] = getPluginInferTopic(pipelineIndex);

    return componentObj;
}


function createPostProc(pipelineIndex) {
    let componentObj = {};
    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = document.getElementById(pipelineIndex + ID_DELIMITER + "postProcType").value;
    componentObj["name"] = getPluginPostProcName(pipelineIndex);
    componentObj["log_level"] = document.getElementById(pipelineIndex + ID_DELIMITER + "postProcLogLevel").value;

    componentObj["processor"] = document.getElementById(pipelineIndex + ID_DELIMITER + "postProcProcessor").value;
    componentObj["width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
    componentObj["height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;

    componentObj["input_topic"] = getPluginInferTopic(pipelineIndex);
    componentObj["output_topic"] = getPluginPostProcTopic(pipelineIndex);

    return componentObj;
}


// DepthFromStereo is different, for example it takes 2 inputs instead of 1.
function createEvaOF(pipelineIndex) {
    let componentObj = {};
    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = "OpticalFlow";
    componentObj["name"] = "P" + pipelineIndex + "_OPTFLOW";

    componentObj["width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
    componentObj["height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;
    componentObj["frame_rate"] = parseInt(document.getElementById(pipelineIndex + ID_DELIMITER + "evaOfFrameRate").value);
    componentObj["format"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputFormat").value;
    componentObj["mode"] = document.getElementById(pipelineIndex + ID_DELIMITER + "evaOfMode").value;
    componentObj["direction"] = document.getElementById(pipelineIndex + ID_DELIMITER + "evaOfDirection").value;
    componentObj["step_size"] = document.getElementById(pipelineIndex + ID_DELIMITER + "evaOfStepSize").value;

    componentObj["input_topic"] = getPluginInputTopic(pipelineIndex);
    componentObj["output_topic"] = "/topic/" + componentObj["component_type"] + "/" + componentObj["name"];

    return componentObj;
}


function createVideoEncoder(pipelineIndex) {
    let componentObj = {};
    componentObj["pipeline_id"] = pipelineIndex;
    componentObj["component_type"] = "VideoEncoder";
    componentObj["name"] = "P" + pipelineIndex + "_VENC";
    componentObj["mbps"] = document.getElementById(pipelineIndex + ID_DELIMITER + "vencMbps").value;
    componentObj["width"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputWidth").value;
    componentObj["height"] = document.getElementById(pipelineIndex + ID_DELIMITER + "inputHeight").value;

    componentObj["input_topic"] = getPluginInputTopic(pipelineIndex);
    componentObj["output_topic"] = "/topic/VideoEncoder/" + componentObj["name"];
    
    return componentObj;
}


function createTinyViz(componentsArray, numPipelines) {
    let entries = [];
    for (let pipelineIndex = 0; pipelineIndex < numPipelines; ++pipelineIndex) {
        if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginInputEnabled").value == "true") {
            if (document.getElementById(pipelineIndex + ID_DELIMITER + "inputEnableVisualization").value == "true") {
                let newEntry = {};
                // Camera component name maybe shared by multiple pipelines so cannot specify pipeline
                // but for visual, each camera name needs to be unique.
                newEntry["name"] = "P" + pipelineIndex + "_" + getPluginInputName(pipelineIndex);
                newEntry["cam_topic"] = getPluginInputTopic(pipelineIndex);
                if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginPostProcEnabled").value == "true") {
                    newEntry["obj_topic"] = getPluginPostProcTopic(pipelineIndex);
                }
                entries.push(newEntry);
            }
        }
    }

    if (entries.length > 0) {
        let componentObj = {};
        componentObj["component_type"] = "TinyViz";
        componentObj["name"] = "VIZ";
        componentObj["entries"] = entries;
        componentsArray.push(componentObj);
    }
}


function createLogConfig(componentsArray) {
    let componentObj = {};
    componentObj["component_type"] = "LogConfig";
    componentObj["log_display"] = false;
    if (document.getElementById("logDisplayEnabled").checked) {
        componentObj["log_display"] = true;
    }
    componentObj["log_level"] = document.getElementById("logLevel").value;
    componentsArray.push(componentObj);
}


function createCameraPipeline(componentsArray, pipelineIndex) {
    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginInputEnabled").value == "true") {
        if (document.getElementById(pipelineIndex + ID_DELIMITER + "inputType").value == INPUT_TYPE_DATA_READER) {
            let dataReader = createDataReader(pipelineIndex);
            componentsArray.push(dataReader);
        } else {
            let sensor = createCamera(pipelineIndex);
            componentsArray.push(sensor);
        }
    }

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginEVAOFEnabled").value == "true") {
        let eva = createEvaOF(pipelineIndex);
        componentsArray.push(eva);
    }

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginVideoEncoderEnabled").value == "true") {
        let encoder = createVideoEncoder(pipelineIndex);
        componentsArray.push(encoder);
    }
    
    let preProc = createPreProc(pipelineIndex);
    if (Object.keys(preProc).length > 0) {
        componentsArray.push(preProc);
    }
    
    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginInferenceEnabled").value == "true") {
        let inference = createInference(pipelineIndex);
        componentsArray.push(inference);
    }

    if (document.getElementById(pipelineIndex + ID_DELIMITER + "PluginPostProcEnabled").value == "true") {
        let postProc = createPostProc(pipelineIndex);
        componentsArray.push(postProc);
    }
}


function stackSdkConfigCreate() {
    let errorResult = "";
    let result = errorResult;
    let componentsArray = [];
    createLogConfig(componentsArray);

    let numPipelines = document.getElementById("pipelinesRowsTable").childElementCount;
    for (let pipelineIndex = 0; pipelineIndex < numPipelines; ++pipelineIndex) {
        let pipelineType = document.getElementById(pipelineIndex + ".pipelineType").value;
        if (pipelineType === PIPELINE_TYPE_CAMERA) {
            createCameraPipeline(componentsArray, pipelineIndex);
        } else {
            utilsMessageError("Pipeline type " + pipelineType + " not supported.");
            return errorResult;
        }
    }

    createTinyViz(componentsArray, numPipelines);
    
    let jsonObj = {};
    jsonObj["profileName"] = $('#customProfileName').val();
    jsonObj["configTimestamp"] = new Date();
    jsonObj["components"] = componentsArray;
    result = JSON.stringify(jsonObj, null, 4);
    console.log("stackSdkConfigCreate: " + result);
    return result;
}
