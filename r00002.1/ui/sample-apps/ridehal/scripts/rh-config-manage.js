// Copyright (c) 2023-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// Functions and parameters to manage custom config profiles eg: deploy/delete/clone/create profile

availableProfiles = {};

var ID_DELIMITER = "--";

checkInstalledOnSoc("ridehal", 1);

// array of columns, each column has one or more rows which defines a card for that spot in grid
// get a list of available custom profile configurations
function getCustomProfiles() {
    var getAvailableProfilesCall = $.ajax({
        type: "GET",
        url: "/ridehal/getCustomProfileList/",
        dataType: "json",
    });

    getAvailableProfilesCall.done( function(response) {
        profilesTableDiv = document.querySelector("tbody.table-body");
        if (profilesTableDiv === null) {
            return;
        }
        if(response) {
            availableProfiles = response;
            $("tbody.config-table-body").empty();
            for (var profile in availableProfiles) {
                let profileTableEntry = 
                    '<tr class="table-row custom-config-profile-table-row">' +
                        '<td class="text profile-name">' + profile + '</td>' +
                        '<td class="text text-center date">' + utilsFormatDateStr(availableProfiles[profile]) + '</td>' +
                        '<td> \
                            <div class="button profile-button-group"> \
                                <button class="btn button-small profile-view-button" profilename="' + profile + '">' +
                                    '<span class="button-small-label" profilename="' + profile + '">View</span>' +
                                '</button>' +
                                '<button class="btn button-small profile-clone-button" profilename="' + profile + '">' +
                                    '<span class="button-small-label" profilename="' + profile + '">Clone</span>' +
                                '</button>' +
                                '<button class="btn button-small profile-deploy-button" profilename="' + profile + '">' +
                                    '<span class="button-small-label" profilename="' + profile + '">Deploy</span>' +
                                '</button>' +
                                '<button class="btn button-small profile-delete-button" profilename="' + profile + '">' +
                                    '<span class="button-small-label" profilename="' + profile + '">Delete</span> \
                                </button> \
                            </div> \
                        </td> \
                    </tr>';
                $("tbody.config-table-body").append (profileTableEntry);
            }
        }
    });

    getAvailableProfilesCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}

function refreshPage() {
    getCustomProfiles();
    getDeployedProfileMeta();
    $("#sampleAppsOutput").empty();
}

$(document).ready( function () {
    refreshPage();
    createFieldsForModal("cameraPipelineModal");
    // create event handlers for plugins
    cameraPipelinePluginCards.forEach(pluginColumn =>
        eventHandlerForCameraPlugins(pluginColumn, pipelineId,
        modalTreeList["cameraPipelineModal"].prefixForHidden));
});

var deployedProfileMeta = $("#deployedProfileMeta").text();

function getDeployedProfileMeta() {
    var ajaxCall = $.ajax({
        type: "GET",
        url: "/ridehal/getDeployedProfileMeta",
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        console.log(response);
        if (response) {
            if (response["status"] == "Success") {
                let metaJson = JSON.parse(response["result"]);
                var msg = "Profile on platform: " + metaJson["profileName"] + "<br /> Deployed: " + metaJson["deployTimestamp"] + " UTC";
                document.getElementById("deployedProfileMeta").innerHTML = msg;
                deployedProfileMeta = msg;
            }
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}


// profile names are limited to alphanumeric, hyphen and underscore
// return whether name matches criteria
function validateProfileName(profileName) {
    var regEx = /^[0-9a-zA-Z-_\.]+$/;
    return profileName.match(regEx);
}


function viewProfile(profileName) {
    var ajaxCall = $.ajax({
        type: "GET",
        url: "/log/getFile/",
        data: { filePath: "/runtime/config/ridehal/custom-profiles/" + profileName + ".sh" },
        dataType: "text",
    });

    ajaxCall.done(function (response) {
        $("#sampleAppsOutput").empty();

        let outputElement = document.getElementById("sampleAppsOutput");
        let preElement = document.createElement('pre');
        preElement.innerHTML = response;
        outputElement.append(preElement);
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}

$(".table-body").on("click", ".profile-view-button", function (e) {
    var profileName = e.target.getAttribute("profileName");
    if (Object.keys(availableProfiles).includes(profileName)) {
        console.log("view: " + profileName);
        viewProfile(profileName);
    }
});

$("#cloneProfileModal").on("click", ".clone-profile-button", function (e) {
    $("#cloneProfileModal").modal("hide");
    let dstName = $("#cloneProfileToName").val();
    if (!validateProfileName(dstName)) {
        utilsMessageError("Profile name must contain only alphanumeric, '-', '_', '.'");
        return;
    }

    updateCustomProfile(dstName, $("#cloneProfileContent").val());
});

function cloneGetProfile(profileName) {
    var ajaxCall = $.ajax({
        type: "GET",
        url: "/ridehal/getCustomProfile",
        data: { name: profileName},
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            profile = response["result"];
            $("#cloneProfileFromName").val(profileName);
            $("#cloneProfileContent").val(profile);
            $("#cloneProfileModal").modal("show");
        } else {
            utilsMessageError(response["err_msg"]);
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}

$(".table-body").on("click", ".profile-clone-button", function (e) {
    var profileName = e.target.getAttribute("profileName");
    if (Object.keys(availableProfiles).includes(profileName)) {
        console.log("clone: " + profileName);
        cloneGetProfile(profileName);
    }
});

function deployProfile(profileName) {
    $("#sampleAppsOutput").empty();
    var deployProfileCall = $.ajax({
        type: "POST",
        url: "/ridehal/deployCustomProfile",
        data: { name: profileName },
        dataType: "json",
    });

    deployProfileCall.done(function (response) {
        if (response["status"] == "Success") {
            utilsMessageSuccess("Profile deployed.");
        } else {
            utilsMessageError(response["err_msg"]);
        }
        getDeployedProfileMeta();
    });

    deployProfileCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}

$(".table-body").on("click", ".profile-deploy-button", function (e) {
    var profileName = e.target.getAttribute("profileName");
    if (Object.keys(availableProfiles).includes(profileName)) {
        console.log("deploy: " + profileName);
        deployProfile(profileName);
    }
});

function deleteProfile(profileName) {
    $('#waitingResponse').off('shown.bs.modal');
    var deleteProfileCall = $.ajax({
        type: "POST",
        url: "/ridehal/deleteCustomProfile",
        data: { name: profileName },
        dataType: "json",
        timeout: 3000, // milliseconds
    });

    deleteProfileCall.done(function (response) {
        $("#waitingResponse").modal("hide");
        if (response["status"] == "Success") {
            console.log("deleteProfile " + profileName);
            refreshPage();
        }
        else {
            utilsMessageError(response["err_msg"]);
        }
    });

    deleteProfileCall.fail(function (xhr, textStatus, error) {
        $("#waitingResponse").modal("hide");
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}

$(".table-body").on("click", ".profile-delete-button", function (e) {
    var profileName = e.target.getAttribute("profileName");
    var answer = window.confirm("Are you sure?");
    if (answer) {
        $("#waitingResponse").on('shown.bs.modal', function (e) { deleteProfile(profileName); } );
        $("#waitingResponse").modal("show");
    } else {
        return;
    }
});

//Save and update custom profile information
$("#saveCustomProfileButton").on("click", function () {
    let profileName = "";
    if ($("#createConfigProfileHeader").css("display") == "none") {
        profileName = $(".current-profile-name").text();
    }
    else {
        profileName = $("#profileName").val();
        if (!validateProfileName(profileName)) {
            utilsMessageError("Profile name must contain only alphanumeric, '-', '_', '.'");
            return;
        }
    
        if (Object.keys(availableProfiles).includes(profileName)) {
            var answer = utilsMessageError(profileName + " already exists. Please choose a different name!");
            return;
        }
    }
    console.log("save profile " + profileName);
    let jsonConfig = stackSdkConfigCreate();
    updateCustomProfile(profileName, jsonConfig);
});

function validateProfileName(profileName) {
    var regEx = /^[0-9a-zA-Z-_\.]+$/;
    return profileName.match(regEx);
}

function updateCustomProfile(profileName, parmJsonMeta) {
    let parmRhsh = generateRhsh(profileName, parmJsonMeta);

    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/updateCustomProfile",
        data: { name: profileName,
                jsonMeta: parmJsonMeta,
                rhsh: parmRhsh },
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            utilsMessageSuccess("Profile updated.");
        } else {
            utilsMessageError(response["err_msg"]);
        }
        $("#editConfigModal").modal("hide");
        refreshPage();

        // show shell script to user
        let outputElement = document.getElementById("sampleAppsOutput");
        let preElement = document.createElement('pre');
        preElement.innerHTML = parmRhsh;
        outputElement.append(preElement);
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
        $("#editConfigModal").modal("hide");
        refreshPage();
    });
}

function clearPipelineRows() {
    $(".pipelines-table #pipelinesRowsTable").empty();
}

$("#createNewConfigButton").on("click", function () {
    $("#profileName").val("");
    $("#editConfigModal").modal("show");
    $("#createConfigProfileHeader").css("display", "block");
    $("#editConfigProfileHeader").css("display", "none");
    clearPipelineRows();
    addPipelineRow();
    getAvailableNetworks();
    getCustomRemapTables();
});

$(".table-body.config-table-body").on("click", ".profile-name", function () {
    $("#profileName").val("");
    clearPipelineRows();
    getAvailableNetworks();
    getCustomRemapTables();
    let profileName = $(this).text();
    getCustomProfile(profileName);
    $("#editConfigModal").modal("show");
    $("#createConfigProfileHeader").css("display", "none");
    $("#editConfigProfileHeader").css("display", "block");
    $(".current-profile-name").text(profileName);
});

function getCustomProfile(profileName) {
    var ajaxCall = $.ajax({
        type: "GET",
        url: "/ridehal/getCustomProfile",
        data: { name: profileName},
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            profile = response["result"];
            console.log(profile);
            stackSdkConfigParse(profile);
        } else {
            utilsMessageError(response["err_msg"]);
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}

function createSelectOptions(options, selectId) {
    let select = document.getElementById(selectId);
    if (!select) {
        console.log(selectId + " element does not exist!")
        return;
    }
    for (let i = 0; i < options.length; i++) {
        let option = options[i];
        // if response contains null values skip
        if (option == null) {
            continue;
        }
        let selectOption = document.createElement("option");
        selectOption.textContent = option;
        if (option == "") {
            selectOption.textContent = "(disabled)";
        }
        selectOption.value = option;
        select.appendChild(selectOption);
    }
}

function getCustomRemapTables() {
    var getRemapTables = $.ajax({
        type: "GET",
        url: "/ridehal/getCustomRemapTablesList/",
        dataType: "json",
        timeout: 3000, /* milliseconds */
    });

    getRemapTables.done(function (response) {
        if (response) {
            remapTables = response;
            remapTables.unshift(""); // add empty as first option
            document.getElementById("PreprocessUndistortion" +ID_DELIMITER + "undistortionRemapTableX").textContent = "";
            document.getElementById("PreprocessUndistortion" + ID_DELIMITER + "undistortionRemapTableY").textContent = "";
            createSelectOptions(remapTables, "PreprocessUndistortion" + ID_DELIMITER + "undistortionRemapTableX");
            createSelectOptions(remapTables, "PreprocessUndistortion" + ID_DELIMITER + "undistortionRemapTableY");
        } else {
            utilsMessageError("Couldn't retrieve the full list of available remap tables.");
        }
    });

    getRemapTables.fail(function (xhr, textStatus, error) {
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage + ", check rideconsole.log");
        }
    });
}

function getAvailableNetworks() {
    var getQnnNetworksCall = $.ajax({
        type: "GET",
        url: "/config/getQnnNetworkList/",
        dataType: "json",
        timeout: 3000, /* milliseconds */
    });

    getQnnNetworksCall.done(function (response) {
        if (response) {
            qnnNetworks = response;
            networks = qnnNetworks;
            document.getElementById("PluginInference" + ID_DELIMITER + "inferenceModel").textContent = "";
            createSelectOptions(networks, "PluginInference" + ID_DELIMITER + "inferenceModel");
        } else {
            utilsMessageError("Couldn't retrieve the full list of available networks.");
        }
    });

    getQnnNetworksCall.fail(function (xhr, textStatus, error) {
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage + ", check rideconsole.log");
        }
    });
}
