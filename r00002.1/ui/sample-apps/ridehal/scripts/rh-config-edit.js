// Copyright (c) 2023-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// Functions and parameters to edit individual stack sdk custom configurations and the pipelines present in them

// card defines name of plugin (shown on UI), [<default info>]
// <default info> is the default value of that cards info/Type field
// Plugin names must match a modal name after illegal chars removed
cameraPipelinePluginCards = [
    {
        "Plugin Input": [ "Data Reader" ]
    },
    {
        "Plugin PreProc": [ "PreProcessing" ],
        "Plugin EVA OF": [ "Optical Flow" ],
        "Plugin Video Encoder": [ "H.265" ]
    },
    {
        "Plugin Inference": [ "TBD" ]
    },
    {
        "Plugin PostProc": [ "Centernet" ]
    },
];

// modal tree list
// maps from modal id to children modal ids,
// parent modal and prefix for hidden values
modalTreeList = {
    "cameraPipelineModal" : {
        "parent": "",
        "children": ["parameterConfigModalPluginInput", "parameterConfigModalPluginEVAOF", "parameterConfigModalPluginPreProc",
                "parameterConfigModalPluginInference", "parameterConfigModalPluginPostProc", "parameterConfigModalPluginVideoEncoder"],
        "prefixForHidden": "cameraPipeline",
    },
    "parameterConfigModalPluginInput" : {
        "parent": "cameraPipelineModal",
        "children": [],
        "prefixForHidden": "PluginInput",
    },
    "parameterConfigModalPluginEVAOF" : {
        "parent": "cameraPipelineModal",
        "children": [],
        "prefixForHidden": "PluginEVAOF",
    },
    "parameterConfigModalPluginInference" : {
        "parent": "cameraPipelineModal",
        "children": [],
        "prefixForHidden": "PluginInference",
    },
    "parameterConfigModalPluginPostProc" : {
        "parent": "cameraPipelineModal",
        "children": [],
        "prefixForHidden": "PluginPostProc",
    },
    "parameterConfigModalPluginVideoEncoder" : {
        "parent": "cameraPipelineModal",
        "children": [],
        "prefixForHidden": "PluginVideoEncoder",
    },
    "parameterConfigModalPluginPreProc" : {
        "parent": "cameraPipelineModal",
        "children": [/*"modalPreprocessUndistortion",*/ "modalPreprocessROICrop", "modalPreprocessTarget"],
        "prefixForHidden": "PluginPreProc",
    },
    "modalPreprocessUndistortion" : {
        "parent": "parameterConfigModalPluginPreProc",
        "children": [],
        "prefixForHidden": "PreprocessUndistortion",
    },
    "modalPreprocessROICrop" : {
        "parent": "parameterConfigModalPluginPreProc",
        "children": [],
        "prefixForHidden": "PreprocessROICrop",
    },
    "modalPreprocessTarget" : {
        "parent": "parameterConfigModalPluginPreProc",
        "children": [],
        "prefixForHidden": "PreprocessTarget",
    },
}

// replace not legal characters
var REG_EX_PIPELINE = /[^a-zA-Z0-9]/g;

function createPluginCard(pluginName) {
    let prefixForHidden = "cameraPipeline" + ID_DELIMITER;
    var rowBegin =
        '<div class="plugin-row"> \
            <div class="card"> \
                <div class="card-body"> \
                    <div class="form-check form-switch"> \
                        <label class="card-title">' + pluginName.replace("Plugin ", "") +
                        '</label>';
    var pluginCheckBox = '<input class="form-check-input" type="checkbox" role="switch" id="' + prefixForHidden + pluginName.replace(REG_EX_PIPELINE, "") + 'Enabled" ';
    rowBegin = rowBegin + pluginCheckBox;
    // Hide checkbox for plugin input
    if (pluginName == "Plugin Input") {
        rowBegin = rowBegin + 'style="display:none" ';
    }
    rowBegin = rowBegin + '></div>';
    var rowParameters = '<span class="paramGear">' + '<img src="icons/nav/settings_gear.svg" id="parameter' +
            pluginName.replace(REG_EX_PIPELINE, "") + '"></img>' + '</span>';
    var rowOptions = '<div class="plugin-info">' +
                        '<input class="form-control w-75 ms-2" id="' + prefixForHidden + pluginName.replace(REG_EX_PIPELINE, "") + 'Info" disabled>';
    rowOptions = rowOptions + "</input>" + rowParameters + "</div>";
    var rowEnd = '</div></div></div>';
    return rowBegin + rowOptions + rowEnd;
}

// create the main plugins for camera pipeline and append them to qridPlugins element
// this function should be called only by createFieldsForModal
function appendQridePlugin(pluginColumn) {
    $("#qridePlugins").append('<div class="plugin-column">');
    for (var pluginRow in pluginColumn) {
        $("#qridePlugins .plugin-column:last").append(createPluginCard(pluginRow));
    }
}

// srcPipelineId - Id of source pipeline which needs to be copied
// dstPipelineId - Id of dst pipeline into which the values need to be copied
// since all pipeline info is stored with its edit button,
// copy input fields present in source pipeline edit button to dst pipeline edit button
function copyPipelineFields(srcPipelineId, dstPipelineId) {
    let tdField = document.getElementById(srcPipelineId + "." + "editButton").parentNode;
    let fields = tdField.querySelectorAll("input");
    if (fields.length == 0) {
        console.error("Hidden inputs not present for pipeline id - ", srcPipelineId);
        return;
    }
    for (let  i = 0; i < fields.length; i++) {
        let field = fields[i];
        let id = field.id.replace(srcPipelineId + ID_DELIMITER, "");
        document.getElementById(dstPipelineId + ID_DELIMITER + id).value = field.value;
    }
}

// copy from pipelineId.<input> to pipeline.<input>
// pipeline - cameraPipeline for cameraPipelineModal
function copyToPipelineModal(pipelineId, prefixForHidden) {
    console.debug("copy to pipelineModal from " + pipelineId);
    let pipelineInputs = document.getElementById(pipelineId + "." + "editButton").parentNode.querySelectorAll("input");
    for (let inputIndex = 0; inputIndex < pipelineInputs.length; inputIndex++) {
        let pipelineModalInput = pipelineInputs[inputIndex].id.replace(pipelineId, prefixForHidden);
        let inputValue = pipelineInputs[inputIndex].value;
        document.getElementById(pipelineModalInput).value = inputValue;
        if (document.getElementById(pipelineModalInput).type === "checkbox") {
            document.getElementById(pipelineModalInput).checked =
                inputValue == "true" ? true : false;
        }
    }
}

// copy values from scrModalId to dstModalId
function copyToPluginModal(srcModalId, dstModalId) {
    console.debug("copy to pluginModal from " + srcModalId + " to " + dstModalId);
    let srcPrefixForHidden = modalTreeList[srcModalId].prefixForHidden;
    let dstPrefixForHidden = modalTreeList[dstModalId].prefixForHidden;
    let dstModalFields = document.getElementById(dstModalId).querySelectorAll("input, select");
    for (let inputIndex = 0; inputIndex < dstModalFields.length; inputIndex++) {
        let paramId = dstModalFields[inputIndex].id;
        dstModalFields[inputIndex].value =
            document.getElementById(paramId.replace(dstPrefixForHidden, srcPrefixForHidden)).value;
        if (dstModalFields[inputIndex].type === "checkbox") {
            dstModalFields[inputIndex].checked =
                dstModalFields[inputIndex].value == "true" ? true : false;
        }
    }
}

// Add plugin hidden fields for pipeline based on pipeline type and the plugins
// that are present under it
function addPluginHiddenFieldsToPipeline(pipelineId, pipelineType) {
    let input = "";
    if (pipelineType == "cameraPipeline") {
        input = cameraPipelineInputs.replaceAll("${PIPELINE_ID}", pipelineId);
    } else {
        console.error("unknown pipeline type selected");
    }
    return input;
}


/*  Original vision of different pipeline types.  For now seems like can manage with pipeline
 *  input type.  For now don't have need for radically different pipelines so hide the pipeline type.
 *  Consider cleanup later.
 */
function addPipelineRow() {
    let pipelineId = 0;
    pipelineId = document.getElementById("pipelinesRowsTable").childElementCount;
    $(".pipelines-table #pipelinesRowsTable").append(
        $('<tr class="pipeline-table-row-entry" id="'+ pipelineId + '">').append(
            $('<td style="display:none;">').append(
                '<select id="' + pipelineId + '.' + 'pipelineType">' +
                    '<option value="camera" selected>Camera</option>' +
                '</select>'),
            $('<td>').append(
                "<span> " + pipelineId + "</span>"),
            $('<td>').append(
                '<button class="btn button-small" id="' + pipelineId + '.' + 'editButton">' +
                    '<img src="icons/nav/edit.svg" class="edit-pipeline-row-button"></img>' +
                '</button>' + addPluginHiddenFieldsToPipeline(pipelineId, "cameraPipeline")
            ),
            $('<td>').append(
                '<button class="btn button-small" id="' + pipelineId + '.' + 'cloneButton">' +
                    '<img src="icons/nav/add_plus.svg" class="clone-pipeline-row-button"></img>' +
                '</button>'
            ),
            $('<td>').append(
                '<button class="btn button-small" id="' + pipelineId + '.' + 'deleteButton">' +
                    '<img src="icons/nav/delete.svg" class="delete-pipeline-row-button"></img>' +
                    '</button>'
            )
        )
    );
}

// common function to save from modal to parentModal
// copy the values from prefixForHidden.inputs/selects to parentModal's prefixForHidden.inputs.
function saveToParentModal(modalId) {
    const KEY_CLASS_INFO_FIELD = "pluginInfoField";
    let parentPrefixForHidden = "";
    if (modalId == "cameraPipelineModal") {
        let pipelineId = $("#pipelineId").val();
        parentPrefixForHidden = pipelineId;
    } else {
        parentPrefixForHidden = modalTreeList[modalTreeList[modalId].parent].prefixForHidden;
    }

    console.debug("save from " + modalId + " to " + parentPrefixForHidden);

    let prefixForHidden = modalTreeList[modalId].prefixForHidden;

    let modalFields = document.getElementById(modalId).querySelectorAll("input, select");
    modalFields.forEach( function (field) {
        parentFieldId = field.id.replace(prefixForHidden, parentPrefixForHidden);
        let parentElement = document.getElementById(parentFieldId);
        parentElement.value = field.value;
        if (field.type === "checkbox") {
            document.getElementById(parentFieldId).value =
                field.checked;
        }
        // special case for handling pluginInfoField data
        if (field.classList.contains(KEY_CLASS_INFO_FIELD)) {
            let pluginName = "";
            if (modalId.includes("parameterConfigModal")) {
                pluginName = modalId.replace("parameterConfigModal", "");
            } else if (modalId.includes("modalPreprocess")) {
                pluginName = modalId.replace("modalPreprocess", "");
            }
            if ((field instanceof HTMLSelectElement) && (field.length === 0)) {
                console.error("options field is empty " + field.id);
                return;
            }
            let infoElement = document.getElementById(parentPrefixForHidden + ID_DELIMITER + pluginName + "Info");
            if (infoElement) {
                // Originally logic assumed Info came from a <select>.
                // Update allows intermediate level which is hidden placeholder so not sure
                // if now can handle Info from a non <select>
                // Limitation, if Info more than one level away, the <select> text and value must be the same.
                if (field instanceof HTMLSelectElement) {
                    infoElement.value = field.options[field.selectedIndex].text;
                } else {
                    infoElement.value = field.value;
                }
            } else {
                // Info field might be one level higher so make sure parent modal hidden field has class to trigger
                // for next parent level save.
                parentElement.classList.add(KEY_CLASS_INFO_FIELD);
            }
        }
    });
}

// Save pipeline information back to hidden input fields for respective pipeline id
$("#savePipelineButton").on("click", function () {
    let cameraPipelinePrefix = modalTreeList["cameraPipelineModal"].prefixForHidden + ID_DELIMITER;
    let inferenceInfo = cameraPipelinePrefix + "PluginInferenceInfo";
    let inferenceEnabled = cameraPipelinePrefix + "PluginInferenceEnabled";
    if (document.getElementById(inferenceEnabled).checked &&
        document.getElementById(inferenceInfo).value == 'TBD') {
        utilsMessageError("No inference model has been selected. Please select one in Inference Plugin.");
        return;
    }

    if (document.getElementById(cameraPipelinePrefix + "PluginEVAOFEnabled").checked) {
        const evaOfFormats = "nv12,nv12_ubwc";
        const evaOfFormatsSet = new Set(evaOfFormats.split(","));
        let inputFormat = document.getElementById(cameraPipelinePrefix + "inputFormat").value;
        if (!evaOfFormatsSet.has(inputFormat)) {
            utilsMessageError("EVA OF only supports " + evaOfFormats);
            return;
        }
    }

    if (document.getElementById(cameraPipelinePrefix + "PluginVideoEncoderEnabled").checked) {
        const encoderFormats = "nv12,nv12_ubwc";
        const encoderFormatsSet = new Set(encoderFormats.split(","));
        let inputFormat = document.getElementById(cameraPipelinePrefix + "inputFormat").value;
        if (!encoderFormatsSet.has(inputFormat)) {
            utilsMessageError("Video Encoder only supports " + encoderFormats);
            return;
        }
    }

    saveToParentModal("cameraPipelineModal");
    $("#cameraPipelineModal").modal("hide");
});

$("#addNewPipelineButton").on("click", function () {
    addPipelineRow();
});

var paramsCamera = ["cameraChannel", "inputWidth", "inputHeight", "inputFormat", "cameraIspUseCase", "cameraRequestMode"];
var paramsDataReaderTensor = ["dataReaderType", "dataReaderTensorType", "dataReaderTensorDims", "inputFrameRate" ];
var paramsDataReaderImage = ["dataReaderType", "inputFrameRate", "inputWidth", "inputHeight", "inputFormat", 
        "dataReaderVideoFile", "dataReaderVideoFps", "dataReaderVideoStartSec", "dataReaderVideoLenSec" ];

function resetInputParamsLayout() {
    let prefix = "PluginInput" + ID_DELIMITER;
    let modal = "parameterConfigModalPluginInput";
    let hideFields = paramsCamera.concat(paramsDataReaderTensor.concat(paramsDataReaderImage));
    for (let index = 0; index < hideFields.length; index++) {
        $("#" + modal + " #" + prefix + hideFields[index]).parent().parent().css("display","none");
    }
}

function setCameraParamsLayout() {
    let prefix = "PluginInput" + ID_DELIMITER;
    let modal = "parameterConfigModalPluginInput";
    let showFields = paramsCamera;
    for (let index = 0; index < showFields.length; index++) {
        $("#" + modal + " #" + prefix + showFields[index]).parent().parent().css("display","flex");
    }
}

function setDataParamsLayout() {
    let prefix = "PluginInput" + ID_DELIMITER;
    let modal = "parameterConfigModalPluginInput";
    let showFields = paramsDataReaderImage;
    if (document.getElementById(prefix + "dataReaderType").value == "tensor") {
        showFields = paramsDataReaderTensor;
    }
    for (let index = 0; index < showFields.length; index++) {
        $("#" + modal + " #" + prefix + showFields[index]).parent().parent().css("display","flex");
    }
}

function setLayoutPluginInput() {
    resetInputParamsLayout();
    let prefix = modalTreeList["parameterConfigModalPluginInput"].prefixForHidden + ID_DELIMITER;
    if (document.getElementById(prefix + "inputType").value === "datareader") {
        setDataParamsLayout();
    } else {
        setCameraParamsLayout();
    }
}

$('#PluginInput' + ID_DELIMITER + 'inputType').change(function () {
    setLayoutPluginInput();
})

$('#PluginInput' + ID_DELIMITER + 'dataReaderType').change(function () {
    setLayoutPluginInput();
})

// pluginModal : plugin modal Id for the modal to be opened
// prefixForHidden : Id prefix for inputs from which modal input values need to be updated
// function should be called when opening a plugin / sub-plugin modal
function populateModalParams(pluginModal, prefixForHidden) {
    switch(pluginModal) {
        case "parameterConfigModalPluginInput":
            setLayoutPluginInput();
            break;
        default:
            break;
    }
}

// Add click function for settings gear button to open pop up modal window for editing plugin params
function clickPluginsCameraPipeline(pluginName) {
    console.debug("add event listener for " + pluginName + " settings gear button");
    $("#parameter" + pluginName.replace(REG_EX_PIPELINE, "")).on("click", function(e) {
        let pluginId = $(this).attr("id").replace("parameter", "") + "Enabled";
        let prefix = modalTreeList["cameraPipelineModal"].prefixForHidden
                        + ID_DELIMITER;
        if (document.getElementById(prefix + pluginId).checked) {
            // populate popup modal with hidden input values
            if ($("#parameterConfigModal" + pluginName.replace(REG_EX_PIPELINE, "")).length == 0) {
                alert("There are no parameters to be configured here!");
            }
            else {
                $("#parameterConfigModal" + pluginId.replace("Enabled", "")).modal("show");
                // if plugin does not have a subplugin, call populate with pipeline level
                // else copy the fields from pipeline to plugin level
                // call populate with plugin level
                let prefixForHidden = modalTreeList["parameterConfigModal" + pluginId.replace("Enabled", "")].prefixForHidden;
                copyToPluginModal("cameraPipelineModal", "parameterConfigModal" + pluginId.replace("Enabled", ""));
                populateModalParams("parameterConfigModal" + pluginId.replace("Enabled", ""), prefixForHidden);
            }
        }
        else {
            alert("Enable plugin to set parameters!");
        }
    });
}

function eventHandlerForCameraPlugins(pluginColumn, pipelineId, prefixForHidden) {
    for(let pluginRow in pluginColumn) {
        // add the click function for each of the plugin parameter
        clickPluginsCameraPipeline(pluginRow);
    }
}

$("#pipelinesRowsTable").on("click", ".edit-pipeline-row-button", function () {
    var pipelineId = $(this).parent().attr("id").split(".")[0];
    $("#cameraPipelineModal #pipelineId").val(pipelineId);
    var profileName = ($("#editConfigProfileHeader").css('display') == 'none') ?
            $("#profileName").val() : $(".current-profile-name").text();
    $("#cameraPipelineModal #pipelineProfileName").val(profileName);
    $("#cameraPipelineModal").modal("show");
    $("#pipelineId").val(pipelineId);
    document.getElementById("pipelineType").value = document.getElementById(pipelineId + "." + "pipelineType").value;
    copyToPipelineModal(pipelineId, modalTreeList["cameraPipelineModal"].prefixForHidden);
});

$("#pipelinesRowsTable").on("click", ".clone-pipeline-row-button", function () {
    var pipelineId = $(this).parent().attr("id").split(".")[0];
    addPipelineRow();
    let newPipelineId = document.getElementById("pipelinesRowsTable").childElementCount - 1;
    copyPipelineFields(pipelineId, newPipelineId);
});

$("#pipelinesRowsTable").on("click", ".delete-pipeline-row-button", function () {
    let numRows = document.getElementById("pipelinesRowsTable").childElementCount;
    if (numRows <= 1) {
        utilsMessageError("Minimum 1 pipeline required.");
        return;
    }

    let selectedId = $(this).parent().attr("id").split('.')[0];
    // copy shift remaining rows, do not need to copy last row since it will be deleted
    for (let index = selectedId; index < numRows - 1; ++index) {
        // for some reason int and string can interchange other places but not here
        let srcId = parseInt(index) + 1;
        copyPipelineFields(srcId.toString(), index);
    }
    $("#pipelinesRowsTable tr:last").remove();
});

// check saved Target block variables
function checkConditionsTargetBlock() {
    let preprocPrefix = modalTreeList["parameterConfigModalPluginPreProc"].prefixForHidden + ID_DELIMITER;
    let targetWidthVal = parseInt(document.getElementById(preprocPrefix + "targetWidth").value);
    let targetHeightVal = parseInt(document.getElementById(preprocPrefix + "targetHeight").value);
    let targetFormatVal = document.getElementById(preprocPrefix + "targetFormat").value;
    if (isNaN(targetWidthVal) || targetWidthVal <= 0) {
        utilsMessageError("Target width must be > 0.");
        return false;
    }
    if (isNaN(targetHeightVal) || targetHeightVal <= 0) {
        utilsMessageError("Target height must be > 0.");
        return false;
    }
    return true;
}

// check Input block variables
function checkConditionsInputBlock() {
    let inputPluginPrefix = modalTreeList["parameterConfigModalPluginInput"].prefixForHidden + ID_DELIMITER;
    let inputWidthVal = parseInt(document.getElementById(inputPluginPrefix + "inputWidth").value);
    let inputHeightVal = parseInt(document.getElementById(inputPluginPrefix + "inputHeight").value);
    if (isNaN(inputWidthVal) || inputWidthVal <= 0) {
        utilsMessageError("Input width must be > 0.");
        return false;
    }
    if (isNaN(inputHeightVal) || inputHeightVal <= 0) {
        utilsMessageError("Input height must be > 0.");
        return false;
    }

    return true;
}

// check saved ROI block variables
function checkConditionsROIBlock() {
    let preprocPrefix = modalTreeList["parameterConfigModalPluginPreProc"].prefixForHidden + ID_DELIMITER;
    let roiWidthVal = parseInt(document.getElementById(preprocPrefix + "roiWidth").value);
    let roiHeightVal = parseInt(document.getElementById(preprocPrefix + "roiHeight").value);
    let roiXVal = parseInt(document.getElementById(preprocPrefix + "roiX").value);
    let roiYVal = parseInt(document.getElementById(preprocPrefix + "roiY").value);
    if (isNaN(roiWidthVal) || roiWidthVal < 1) {
        utilsMessageError("ROI width must be > 0.");
        return false;
    }
    if (isNaN(roiHeightVal) || roiHeightVal < 1) {
        utilsMessageError("ROI height must be > 0.");
        return false;
    }
    if (isNaN(roiXVal) || roiXVal < 0) {
        utilsMessageError("ROI X must be in range 0-99.");
        return false;
    }
    if (isNaN(roiYVal) || roiYVal < 0) {
        utilsMessageError("ROI Y must be in range 0-99.");
        return false;
    }
    let cameraPipelinePrefix = modalTreeList["cameraPipelineModal"].prefixForHidden + ID_DELIMITER;
    let inputWidth = parseInt(document.getElementById(cameraPipelinePrefix + "inputWidth").value);
    let inputHeight = parseInt(document.getElementById(cameraPipelinePrefix + "inputHeight").value);
    if ((parseInt(roiXVal) + parseInt(roiWidthVal)) > inputWidth) {
        utilsMessageError("ROI X + Width must be <= Input Width");
        return false;
    }
    if ((parseInt(roiYVal) + parseInt(roiHeightVal)) > inputHeight) {
        utilsMessageError("ROI Y + Width must be <= Input Height");
        return false;
    }
    return true;
}

$(".saveParams").on("click", function () {
    var paramModalId = $(this).closest(".edit-config-modal").attr("id");
    if (paramModalId === "parameterConfigModalPluginInput") {
        if (!checkConditionsInputBlock()) {
            return;
        }
    }
    if (paramModalId === "parameterConfigModalPluginPreProc") {
        let preprocPrefix =
            modalTreeList["parameterConfigModalPluginPreProc"].prefixForHidden + ID_DELIMITER;
        if (!checkConditionsTargetBlock()) {
            return;
        }
        if (document.getElementById(preprocPrefix + "ROICropEnabled").checked &&
            !checkConditionsROIBlock()) {
            return;
        }
    }
    saveToParentModal(paramModalId);
    $(this).closest(".edit-config-modal").modal("hide");
});


// set parameter values for the pre processing blocks when paramGear clicked
$("#parameterConfigModalPluginPreProc").on("click", ".preproc-config-params", function (e) {
    let preprocPrefix = modalTreeList["parameterConfigModalPluginPreProc"].prefixForHidden
        + ID_DELIMITER;
    let preprocSubplugin = $(this).attr("id").replace("parameter", "");
    if (document.getElementById(preprocPrefix + preprocSubplugin + "Enabled").checked) {
        let prefixForHidden = "";
        prefixForHidden = modalTreeList["modalPreprocess" + preprocSubplugin].prefixForHidden;
        copyToPluginModal("parameterConfigModalPluginPreProc", "modalPreprocess" + preprocSubplugin);
        populateModalParams("modalPreprocess" + preprocSubplugin, prefixForHidden);
        $("#modalPreprocess" + preprocSubplugin).modal("show");
    } else {
        alert("Enable " + preprocSubplugin + " plugin to set parameters!");
    }
});

var cameraPipelineInputs = "";

// Create the hidden fields for plugins in the modal tree list
// createFieldsForModal function should only be called when page is loaded
function createFieldsForModal(modalId) {
    let input = "";
    let hiddenFieldId = modalTreeList[modalId].prefixForHidden;
    if (modalId == "cameraPipelineModal") {
        //Add plugin cards
        $("#qridePlugins .plugin-column").remove();
        cameraPipelinePluginCards.forEach(pluginColumn => appendQridePlugin(pluginColumn));
        for (let i = 0; i < cameraPipelinePluginCards.length; i++) {
            var pluginColumn = cameraPipelinePluginCards[i];
            for (var pluginRow in pluginColumn) {
                let pluginEnabledValue = 'false';
                // always keep camera plugin enabled!
                if (pluginRow == "Plugin Input") {
                    pluginEnabledValue = 'true';
                }
                pluginRowId = pluginRow.replace(REG_EX_PIPELINE, "");
                cameraPipelineInputs = cameraPipelineInputs + '<input id="' + "${PIPELINE_ID}" + ID_DELIMITER + pluginRowId + 'Enabled"' +
                ' name="' + "${PIPELINE_ID}" + ID_DELIMITER +
                pluginRowId + 'Enabled" value="' + pluginEnabledValue + '" style="display:none"/>';
                cameraPipelineInputs = cameraPipelineInputs + '<input id="' + "${PIPELINE_ID}" + ID_DELIMITER + pluginRowId + 'Info"' +
                ' name="' + "${PIPELINE_ID}" + ID_DELIMITER +
                pluginRowId + 'Info" value="' +  pluginColumn[pluginRow][0] + '" style="display:none"/> ';
            }
        }
    }
    modalTreeList[modalId].children.forEach( function (childModalId) {
        createFieldsForModal(childModalId);
        let modalFields = document.getElementById(childModalId).querySelectorAll("input, select");
        for (let inputIndex = 0; inputIndex < modalFields.length; inputIndex++) {
            let field = modalFields[inputIndex];
            let value = field.value;
            let fieldId = field.id.replace(modalTreeList[childModalId].prefixForHidden + ID_DELIMITER, "");
            if (field.tagName == "SELECT") {
                value = field[0].value;
            }
            if (field.tagName == "INPUT" && field.type == "checkbox") {
                value = "false";
                // always keep target plugin enabled!
                if (field.id == modalTreeList["parameterConfigModalPluginPreProc"].prefixForHidden +
                    ID_DELIMITER + "TargetEnabled") {
                    value = "true";
                }
            }
            input = input + '<input id="' + hiddenFieldId + ID_DELIMITER + fieldId +
            '" name="' + hiddenFieldId
            + ID_DELIMITER + fieldId + '" value="' + value +
            '" class="' + hiddenFieldId + '" style="display:none"/>';
            if (modalId == "cameraPipelineModal") {
                cameraPipelineInputs = cameraPipelineInputs + '<input id="' + "${PIPELINE_ID}" + ID_DELIMITER + fieldId +
                '" name="' + "${PIPELINE_ID}"
                + ID_DELIMITER + fieldId + '" value="' + value + '" style="display:none"/>';
            }

        }
    });
    $("#" + modalId).append(input);
}
