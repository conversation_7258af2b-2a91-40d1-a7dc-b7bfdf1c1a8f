// Copyright (c) 2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// This file contains functions used to generate RideHal config from UI input.
// This file focuses on generating a shell script from meta JSON.


function shCreatePreAmble(profileName, timestamp, logDisplayEnabled, globalLogLevel, componentLogLevels) {
    let result = "#!/bin/sh\n\n";
    result += "# Copyright (c) 2025 Qualcomm Technologies, Inc.\n";
    result += "# All Rights Reserved.\n";
    result += "# Confidential and Proprietary - Qualcomm Technologies, Inc.\n\n";

    result += "# Profile name: " + profileName + "\n";
    result += "# Originally generated by Ride SDK Console\n";
    result += "# " + timestamp + "\n\n";
    
    result += "cd /var/data/ridehal/\n\n";
    
    result += "# For TinyViz\n";
    result += "unset DISPLAY\n\n";
    
    result += "CUSTOM_DIR=/var/data/rh-custom\n";
    result += "export RIDEHAL_LOG_LEVEL=" + globalLogLevel + "\n";
    
    for (const key in componentLogLevels) {
        let logLevel = componentLogLevels[key];
        if (logLevel != "") {
            result += "export " + key + "_RIDEHAL_LOG_LEVEL=" + componentLogLevels[key] + "\n";
        }
    }

    result += "#export RIDEHAL_SYSTRACE=/tmp/ridehal_systrace.bin\n\n";
    
    result += "./bin/rhrun ./bin/RideHalSampleApp \\\n";
    
    if (logDisplayEnabled) {
        result += "  -d \\\n";
    }
    
    return result;
}


// Only test certain stream keys for equality
function compareStreamObjs(stream1, stream2) {
    const fixedFields = [ "isp_use_case", "request_mode" ];
    for (let fieldIndex = 0; fieldIndex < fixedFields.length; ++fieldIndex) {
        let field = fixedFields[fieldIndex];
        if (stream1[field] != stream2[field]) {
            utilsMessageError(stream1["name"] + " pipelines " + stream1["pipeline_id"] + " and " + stream2["pipeline_id"] + " cannot have different " + field);
            return false;
        }
    }

    const compareFields = [ "width", "height", "format" ];
    for (let fieldIndex = 0; fieldIndex < compareFields.length; ++fieldIndex) {
        let field = compareFields[fieldIndex];
        if (stream1[field] != stream2[field]) {
            return false;
        }
    }
    return true;  // streams are equal
}


function sortCamera(sortedCams, componentObj) {
    let channel = componentObj["channel"];
    if (Object.hasOwn(sortedCams, channel)) {
        for (let objIndex = 0; objIndex < sortedCams[channel].length; ++objIndex) {
            if (compareStreamObjs(sortedCams[channel][objIndex], componentObj)) {
                // found duplicate stream don't need to add
                return;
            }
        }
        // no duplicate
        sortedCams[channel].push(componentObj);
    } else {
        let newArray = [ componentObj ];
        sortedCams[channel] = newArray;
    }
}


function shCreateCamera(streamsJson) {
    let result = "";
    if (streamsJson.length == 0) {
        return result;
    }

    result += "  -n " + streamsJson[0]["name"] + " -t Camera \\\n";
    result += "    -k input_id -v " + streamsJson[0]["channel"] + " -k number -v " + streamsJson.length + " \\\n";
    if (streamsJson[0]["isp_use_case"]) {
        result += "    -k isp_use_case -v " + streamsJson[0]["isp_use_case"] + " \\\n";
    }
    result += "    -k request_mode -v " + streamsJson[0]["request_mode"] + " \\\n";

    for (let streamIndex = 0; streamIndex < streamsJson.length; ++streamIndex) {
        let stream = streamsJson[streamIndex];
        let indexNum = "";
        if (streamIndex > 0) {
            indexNum = streamIndex;
        }
        result += "    -k width" + indexNum + " -v " + stream["width"] + " -k height" + indexNum + " -v " + stream["height"]
               + " -k format" + indexNum + " -v " + stream["format"] + " \\\n";

        result += "    -k topic" + indexNum + " -v " + stream["output_topic"] + " \\\n";
    }
    return result;
}


function shCreateCameras(sortedCams) {
    let result = "";
    for (const key in sortedCams) {
        result += shCreateCamera(sortedCams[key]);
    }
    return result;
}


function shCreateDataReader(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t DataReader \\\n";
    result += "    -k type0 -v " + componentObj["data_reader_type"] + " -k fps -v " + componentObj["frame_rate"] + " \\\n";

    // only entry 0 for now
    if (componentObj["data_reader_type"] == "tensor") {
        result += "    -k tensor_type0 -v " + componentObj["tensor_type"] + " -k dims0 -v \"" + componentObj["tensor_dims"] + "\" \\\n";
    } else {
        result += "    -k width0 -v " + componentObj["width"] + " -k height0 -v " + componentObj["height"]
               + " -k format0 -v " + componentObj["format"] + " \\\n";
        if (componentObj["video_file"] != "") {
            result += "    -k data_path0 -v $CUSTOM_DIR/data-reader/" + componentObj["name"] + " \\\n";
        }
    }
    result += "    -k topic -v " + componentObj["output_topic"] + " \\\n";

    return result;
}


function shCreateVideoEncoder(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t VideoEncoder \\\n";
    result += "    -k width -v " + componentObj["width"] + " -k height -v " + componentObj["height"] + " \\\n";
    result += "    -k bitrate -v " + (componentObj["mbps"] * 1000000) + " \\\n";
    result += "    -k input_topic -v " + componentObj["input_topic"] + " \\\n";
    result += "    -k output_topic -v " + componentObj["output_topic"] + " \\\n";
    return result;
}


function shCreateOpticalFlow(componentObj) {
    let result = "";
    
    if (componentObj["extra_preproc"]) {
        result += shCreatePreProcessing(componentObj["extra_preproc"]);
    }

    result += "  -n " + componentObj["name"] + " -t OpticalFlow \\\n";
    result += "    -k width -v " + componentObj["width"] + " -k height -v " + componentObj["height"]
           + " -k format -v " + componentObj["format"] + " \\\n";
    result += "    -k fps -v " + componentObj["frame_rate"] + " -k direction -v " + componentObj["direction"] + " \\\n";
    result += "    -k eva_mode -v " + componentObj["mode"] + " -k step_size -v " + componentObj["step_size"] + " \\\n";
    result += "    -k input_topic -v " + componentObj["input_topic"] + " \\\n";
    result += "    -k output_topic -v " + componentObj["output_topic"] + " \\\n";
    return result;
}


function shCreatePreProcessing(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t " + componentObj["component_type"] + " \\\n";
    result += "    -k input_width0 -v " + componentObj["source_width"] + " -k input_height0 -v " + componentObj["source_height"]
           + " -k input_format0 -v " + componentObj["source_format"] + " \\\n";
    result += "    -k output_width -v " + componentObj["target_width"] + " -k output_height -v " + componentObj["target_height"]
           + " -k output_format -v " + componentObj["target_format"] + " \\\n";

    if (Object.hasOwn(componentObj, "roi_crop") && (componentObj["roi_crop"] === "true")) {
        result += "    -k roi_x0 -v " + componentObj["roi_x"] + " -k roi_y0 -v " + componentObj["roi_y"] + " \\\n";
        result += "    -k roi_width0 -v " + componentObj["roi_width"] + " -k roi_height0 -v " + componentObj["roi_height"] + " \\\n";
    }

    if (Object.hasOwn(componentObj, "enable_undistortion") && (componentObj["enable_undistortion"] === "true")) {
        result += "    -k map_table -v true \\\n";
        let remapArray = componentObj["remap_tables"];
        for (let tableIndex = 0; tableIndex < remapArray.length; ++tableIndex) {
            let xyPair = remapArray[tableIndex];
            result += "    -k mapX_path" + tableIndex + " -v $CUSTOM_DIR/remapTables/" + xyPair[0] + " \\\n";
            result += "    -k mapY_path" + tableIndex + " -v $CUSTOM_DIR/remapTables/" + xyPair[1] + " \\\n";
        }
    }

    result += "    -k input_topic -v " + componentObj["input_topic"] + " \\\n";
    result += "    -k output_topic -v " + componentObj["output_topic"] + " \\\n";
    return result;
}


function shCreateInference(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t Qnn \\\n";
    result += "    -k processor -v htp" + componentObj["backend_core"] + " \\\n";
    result += "    -k model_path -v $CUSTOM_DIR/networks/" + componentObj["model_path"] + "/program.bin \\\n";
    result += "    -k input_topic -v " + componentObj["input_topic"] + " \\\n";
    result += "    -k output_topic -v " + componentObj["output_topic"] + " \\\n";
    return result;
}


function shCreatePostProcCenternet(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t PostProcCenternet \\\n";
    result += "    -k processor -v " + componentObj["processor"] + " \\\n";
    result += "    -k width -v " + componentObj["width"] + " -k height -v " + componentObj["height"] + " \\\n";
    result += "    -k input_topic -v " + componentObj["input_topic"] + " \\\n";
    result += "    -k output_topic -v " + componentObj["output_topic"] + " \\\n";
    return result;
}


function shCreateTinyViz(componentObj) {
    let result = "";
    result += "  -n " + componentObj["name"] + " -t TinyViz \\\n";
    
    let cameras = "";
    let topics = "";

    let entries = componentObj["entries"];
    for (let entryIndex = 0; entryIndex < entries.length; ++entryIndex) {
        let entry = entries[entryIndex];
        if (entryIndex > 0) {
            cameras += ",";
        }
        cameras += entry["name"];
        topics += "    -k cam_topic" + entryIndex + " -v " + entry["cam_topic"] + " \\\n";
        if (Object.hasOwn(entry, "obj_topic")) {
            topics += "    -k obj_topic" + entryIndex + " -v " + entry["obj_topic"] + " \\\n";
        }
    }

    result += "    -k cameras -v " + cameras + " \\\n";
    result += topics;
    return result;
}


function generateRhsh(profileName, jsonMetaString) {
    let errorResult = "";
    let result = errorResult;

    let jsonMeta = JSON.parse(jsonMetaString);
    let componentsArray = jsonMeta["components"];
    
    let logDisplayEnabled = false;
    let globalLogLevel = "ERROR";
    let componentLogLevels = {};
    let sortedCams = {};
    let mainBodySh = "";
    
    for (let componentIndex = 0; componentIndex < componentsArray.length; ++componentIndex) {
        let componentObj = componentsArray[componentIndex];
        let componentType = componentObj["component_type"];
        switch (componentType) {
            case "Camera":
                sortCamera(sortedCams, componentObj);
                break;
            case "DataReader":
                mainBodySh += shCreateDataReader(componentObj);
                break;
            case "VideoEncoder":
                mainBodySh += shCreateVideoEncoder(componentObj);
                break;
            case "OpticalFlow":
                mainBodySh += shCreateOpticalFlow(componentObj);
                break;
            case "CL2DFlex":
            case "GL2DFlex":
            case "Remap":
            case "C2D":
                mainBodySh += shCreatePreProcessing(componentObj);
                break;
            case "Qnn":
                mainBodySh += shCreateInference(componentObj);
                break;
            case "PostProcCenternet":
                mainBodySh += shCreatePostProcCenternet(componentObj);
                break;
            case "TinyViz":
                mainBodySh += shCreateTinyViz(componentObj);
                break;
            case "LogConfig":
                logDisplayEnabled = componentObj["log_display"];
                globalLogLevel = componentObj["log_level"];
                break;
        }
        
        if ((componentType != "LogConfig") && Object.hasOwn(componentObj, "log_level")) {
            let logLevel = componentObj["log_level"];
            if (logLevel != "") {
                componentLogLevels[componentObj["name"]] = componentObj["log_level"];
            }
        }
    }
    
    let preAmbleSh = shCreatePreAmble(profileName, jsonMeta["configTimestamp"], logDisplayEnabled, globalLogLevel, componentLogLevels);
    
    let camerasSh = shCreateCameras(sortedCams);
    
    result = preAmbleSh + camerasSh + mainBodySh;

    console.log("generateRhsh: " + result);
    return result;
}
