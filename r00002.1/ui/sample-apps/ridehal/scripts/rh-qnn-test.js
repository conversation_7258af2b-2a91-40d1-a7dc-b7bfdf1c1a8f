// Copyright (c) 2023-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// Functions and parameters for QNN Test

// Assume other .js loaded to reuse functions.


checkInstalledOnSoc("rh-qnn-test", 1);

// get currently deloyed profile (from rh-config-manage.js)
// assume need to go through Manage to get to Run
document.getElementById("deployedProfileMeta").innerHTML = deployedProfileMeta;

getAvailableNetworks();


function generateQnnJson(profileName, modelName) {
    let componentsArray = [];
    let pipelineIndex = 0;
    assignElementValueById(pipelineIndex, "inferenceModel", modelName);
    let component = createInference(pipelineIndex);
    componentsArray.push(component);

    let jsonObj = {};
    jsonObj["profileName"] = profileName;
    jsonObj["configTimestamp"] = new Date();
    jsonObj["components"] = componentsArray;
    result = JSON.stringify(jsonObj, null, 4);
    console.log("generateQnnJson: " + result);
    return result;
}


function generateQnnRhsh(profileName, modelName, logLevel) {
    let result = "";
    let timestamp = new Date();

    result += "#!/bin/sh\n\n";
    result += "# Copyright (c) 2025 Qualcomm Technologies, Inc.\n";
    result += "# All Rights Reserved.\n";
    result += "# Confidential and Proprietary - Qualcomm Technologies, Inc.\n\n";

    result += "# Profile name: " + profileName + "\n";
    result += "# Originally generated by Ride SDK Console\n";
    result += "# " + timestamp + "\n\n\n";
    
    result += "cd /var/data/ridehal/\n\n";
    
    result += "CUSTOM_DIR=/var/data/rh-custom\n";
    result += "export RIDEHAL_LOG_LEVEL=" + logLevel + "\n";
    result += "#export RIDEHAL_SYSTRACE=/tmp/ridehal_systrace.bin\n\n";
    
    result += "./bin/rhrun ./bin/RideHalQnnTest \\\n";
    result += "  -n QNN0 -m $CUSTOM_DIR/networks/" + modelName + "/program.bin -l 1 \\\n";

    console.log("generateQnnRhsh: " + result);
    return result;
}


function qnnTestSeqDelete(profileName) {
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/deleteCustomProfile",
        data: { name: profileName },
        dataType: "json",
        timeout: 3000, // milliseconds
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            $('#sampleAppsProgressContainer').hide();
            console.log("deleteProfile " + profileName);
            utilsMessageSuccess("QNN Test analyze sequence completed.");
        }
        else {
            $('#sampleAppsProgressContainer').hide();
            utilsMessageError(response["err_msg"]);
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsProgressContainer').hide();
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}


function qnnTestSeqRun(profileName) {
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/startDeployedProfile",
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess("Start App command sent.");
            qnnTestSeqDelete(profileName);
        } else {
            $('#sampleAppsProgressContainer').hide();
            utilsMessageError("Error starting app. " + response["err_msg"] + " Please check rideconsole.log");
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsProgressContainer').hide();
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });    
}


function qnnTestSeqDeploy(profileName) {
    $("#stackSdkDeployOutput").empty();
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/deployCustomProfile",
        data: { name: profileName },
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            utilsMessageSuccess("Profile deployed.");
            qnnTestSeqRun(profileName);
        } else {
            $('#sampleAppsProgressContainer').hide();
            utilsMessageError(response["err_msg"]);
        }
        getDeployedProfileMeta();
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsProgressContainer').hide();
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}


function qnnTestSeqSave(profileName, parmJsonMeta, parmRhsh) {
    var ajaxCall = $.ajax({
        type: "POST",
        url: "/ridehal/updateCustomProfile",
        data: { name: profileName,
                jsonMeta: parmJsonMeta,
                rhsh: parmRhsh },
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        if (response["status"] == "Success") {
            utilsMessageSuccess("Profile updated.");
            qnnTestSeqDeploy(profileName);
        } else {
            $('#sampleAppsProgressContainer').hide();
            utilsMessageError(response["err_msg"]);
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsProgressContainer').hide();
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Error - " + errorMessage);
    });
}


$("#rhQnnTestAnalyzeButton").on("click", function () {
    $('#sampleAppsProgressContainer').css("display", "block");
    $("#sampleAppsOutput").empty();
    let modelName = document.getElementById("PluginInference--inferenceModel").value;
    let profileName = "TEMP-FOR-QNN-TEST-USE";
    let qnnTestJson = generateQnnJson(profileName, modelName);
    let qnnTestRhsh = generateQnnRhsh(profileName, modelName, "DEBUG");

    // Reuse existing APIs.
    // Sequence starts by saving temporary profile to run QNN test.
    // Then deploy the profile.
    // Start/Run the profile, expect it to send on its own so don't need to Stop.
    // Then delete the temporary profile.
    qnnTestSeqSave(profileName, qnnTestJson, qnnTestRhsh);
});
