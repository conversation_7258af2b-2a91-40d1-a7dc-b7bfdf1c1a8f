// Copyright (c) 2023-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// This file contains functions used to parse a profile meta JSON
// to UI hidden storage fields.

var KEY_PIPELINE_ID = "pipeline_id";


function findPipelineIndex(pipelineId) {
    // Assume meta json has pipelines in order.  So assume they will be parsed in order.
    // So can assume the pipeline ID is same as the index.
    let numPipeline = document.getElementById("pipelinesRowsTable").childElementCount;
    if (pipelineId >= numPipeline) {
        addPipelineRow();
    }
    return pipelineId;
}


function assignElementValueById(pipelineIndex, fieldId, value) {
    let elementId = pipelineIndex + ID_DELIMITER + fieldId;
    let element = document.getElementById(elementId);
    if (element == null) {
        console.error("Cannot assign value to " + elementId + ", not found.");
        return;
    }
    
    element.value = value;
//    console.debug("Element " + elementId + " assigned to " + document.getElementById(elementId).value);
}


function parseCamera(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("Camera component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    assignElementValueById(pipelineIndex, "inputType", "camera");
    let element = document.getElementById("PluginInput" + ID_DELIMITER + "inputType");
    element.value = "camera";
    let option = element.options[element.selectedIndex];
    if (typeof option !== 'undefined') {
        assignElementValueById(pipelineIndex, "PluginInputInfo", option.text);
    }
    
    if (Object.hasOwn(component, "channel")) {
        assignElementValueById(pipelineIndex, "cameraChannel", component["channel"]);
    }

    if (Object.hasOwn(component, "width")) {
        assignElementValueById(pipelineIndex, "inputWidth", component["width"]);
    }

    if (Object.hasOwn(component, "height")) {
        assignElementValueById(pipelineIndex, "inputHeight", component["height"]);
    }

    if (Object.hasOwn(component, "format")) {
        assignElementValueById(pipelineIndex, "inputFormat", component["format"]);
    }

    if (Object.hasOwn(component, "isp_use_case")) {
        assignElementValueById(pipelineIndex, "cameraIspUseCase", component["isp_use_case"]);
    }

    if (Object.hasOwn(component, "request_mode")) {
        assignElementValueById(pipelineIndex, "cameraRequestMode", component["request_mode"]);
    }

    if (Object.hasOwn(component, "enable_visualization")) {
        assignElementValueById(pipelineIndex, "inputEnableVisualization", component["enable_visualization"]);
    }
}


function parseDataReader(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("DataReader component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component["pipeline_id"]);

    assignElementValueById(pipelineIndex, "inputType", "datareader");
    let element = document.getElementById("PluginInput" + ID_DELIMITER + "inputType");
    element.value = "datareader";
    let option = element.options[element.selectedIndex];
    if (typeof option !== 'undefined') {
        assignElementValueById(pipelineIndex, "PluginInputInfo", option.text);
    }

    if (Object.hasOwn(component, "format")) {
        assignElementValueById(pipelineIndex, "inputFormat", component["format"]);
    }

    if (Object.hasOwn(component, "data_reader_type")) {
        assignElementValueById(pipelineIndex, "dataReaderType", component["data_reader_type"]);
    }

    if (Object.hasOwn(component, "tensor_type")) {
        assignElementValueById(pipelineIndex, "dataReaderTensorType", component["tensor_type"]);
    }

    if (Object.hasOwn(component, "tensor_dims")) {
        assignElementValueById(pipelineIndex, "dataReaderTensorDims", component["tensor_dims"]);
    }

    if (Object.hasOwn(component, "width")) {
        assignElementValueById(pipelineIndex, "inputWidth", component["width"]);
    }

    if (Object.hasOwn(component, "height")) {
        assignElementValueById(pipelineIndex, "inputHeight", component["height"]);
    }

    if (Object.hasOwn(component, "frame_rate")) {
        assignElementValueById(pipelineIndex, "inputFrameRate", component["frame_rate"]);
    }
    
    if (Object.hasOwn(component, "video_file")) {
        assignElementValueById(pipelineIndex, "dataReaderVideoFile", component["video_file"]);
    }
    
    if (Object.hasOwn(component, "video_fps")) {
        assignElementValueById(pipelineIndex, "dataReaderVideoFps", component["video_fps"]);
    }
    
    if (Object.hasOwn(component, "video_start_sec")) {
        assignElementValueById(pipelineIndex, "dataReaderVideoStartSec", component["video_start_sec"]);
    }
    
    if (Object.hasOwn(component, "video_len_sec")) {
        assignElementValueById(pipelineIndex, "dataReaderVideoLenSec", component["video_len_sec"]);
    }

    if (Object.hasOwn(component, "enable_visualization")) {
        assignElementValueById(pipelineIndex, "inputEnableVisualization", component["enable_visualization"]);
    }
}


function parseVenc(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("VideoEncoder component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    assignElementValueById(pipelineIndex, "PluginVideoEncoderEnabled", "true");

    if (Object.hasOwn(component, "mbps")) {
        assignElementValueById(pipelineIndex, "vencMbps", component["mbps"]);
    }    
}


function parseOpticalFlow(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("OpticalFlow component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    assignElementValueById(pipelineIndex, "PluginEVAOFEnabled", "true");

    if (Object.hasOwn(component, "frame_rate")) {
        assignElementValueById(pipelineIndex, "evaOfFrameRate", component["frame_rate"]);
    }

    if (Object.hasOwn(component, "mode")) {
        assignElementValueById(pipelineIndex, "evaOfMode", component["mode"]);
    }

    if (Object.hasOwn(component, "direction")) {
        assignElementValueById(pipelineIndex, "evaOfDirection", component["direction"]);
    }

    if (Object.hasOwn(component, "step_size")) {
        assignElementValueById(pipelineIndex, "evaOfStepSize", component["step_size"]);
    }
}


function parsePreProc(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("pre-processing component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    // TODO is there a better way to templatize the parsing?

    // Pre Processing, Undistortion

    if (Object.hasOwn(component, "undistortion")) {
        assignElementValueById(pipelineIndex, "PluginPreProcEnabled", "true");
        assignElementValueById(pipelineIndex, "UndistortionEnabled", "true");
    }

    if (Object.hasOwn(component, "remap_tables")) {
        let remapTables = component["remap_tables"];
        if ((typeof remapTables !== "undefined") && (remapTables.length > 0)) {
            let remapEntry = remapTables[0];
            if ((typeof remapEntry !== "undefined") && (remapEntry.length >= 2)) {
                let tokens = remapEntry[0].split("/");
                if (tokens.length < 1) {
                    console.error("pre-processing component illegal remap X value.");
                    return;
                }
                // strip off end of the path
                let tableX = tokens[tokens.length - 1];
                assignElementValueById(pipelineIndex, "undistortionRemapTableX", tableX);

                tokens = remapEntry[1].split("/");
                if (tokens.length < 1) {
                    console.error("pre-processing component illegal remap Y value.");
                    return;
                }
                // strip off end of the path
                let tableY = tokens[tokens.length - 1];
                assignElementValueById(pipelineIndex, "undistortionRemapTableY", tableY);
            }
        }
    }
    
    // Pre Processing, ROI Crop
    if (Object.hasOwn(component, "roi_crop")) {
        assignElementValueById(pipelineIndex, "PluginPreProcEnabled", "true");
        assignElementValueById(pipelineIndex, "ROICropEnabled", "true");
    }

    if (Object.hasOwn(component, "roi_x")) {
        assignElementValueById(pipelineIndex, "roiX", component["roi_x"]);
    }

    if (Object.hasOwn(component, "roi_y")) {
        assignElementValueById(pipelineIndex, "roiY", component["roi_y"]);
    }

    if (Object.hasOwn(component, "roi_width")) {
        assignElementValueById(pipelineIndex, "roiWidth", component["roi_width"]);
    }

    if (Object.hasOwn(component, "roi_height")) {
        assignElementValueById(pipelineIndex, "roiHeight", component["roi_height"]);
    }
    
    // Pre Processing, Target

    let preProcType = component["component_type"];
    assignElementValueById(pipelineIndex, "preProcType", preProcType);

    let element = document.getElementById("PreprocessTarget" + ID_DELIMITER + "preProcType");
    element.value = preProcType;
    let option = element.options[element.selectedIndex];
    if (typeof option !== 'undefined') {
        assignElementValueById(pipelineIndex, "PluginPreProcInfo", option.text);
    }

    if (Object.hasOwn(component, "target_width")) {
        assignElementValueById(pipelineIndex, "PluginPreProcEnabled", "true");
        assignElementValueById(pipelineIndex, "targetWidth", component["target_width"]);
    }

    if (Object.hasOwn(component, "target_height")) {
        assignElementValueById(pipelineIndex, "PluginPreProcEnabled", "true");
        assignElementValueById(pipelineIndex, "targetHeight", component["target_height"]);
    }

    if (Object.hasOwn(component, "target_format")) {
        assignElementValueById(pipelineIndex, "PluginPreProcEnabled", "true");
        assignElementValueById(pipelineIndex, "targetFormat", component["target_format"]);
    }
}


function parseInference(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("Qnn component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    if (Object.hasOwn(component, "model_path")) {
        let tokens = component["model_path"].split("/");
        if (tokens.length < 1) {
            console.error("inference component illegal model_path value.");
            return;
        }
        // strip model off end of the path
        let model = tokens[tokens.length - 1];
        assignElementValueById(pipelineIndex, "inferenceModel", model);
        assignElementValueById(pipelineIndex, "PluginInferenceEnabled", "true");
        assignElementValueById(pipelineIndex, "PluginInferenceInfo", model);
    }

    if (Object.hasOwn(component, "backend_core")) {
        assignElementValueById(pipelineIndex, "inferenceBackendCore", component["backend_core"]);
    }
}


function parsePostProc(component) {
    if (!Object.hasOwn(component, KEY_PIPELINE_ID)) {
        console.error("post-processing component is missing '" + KEY_PIPELINE_ID + "' attribute.");
        return;
    }

    let pipelineIndex = findPipelineIndex(component[KEY_PIPELINE_ID]);

    let postProcType = component["component_type"];
    assignElementValueById(pipelineIndex, "postProcType", postProcType);
    assignElementValueById(pipelineIndex, "postProcProcessor", component["processor"]);
    assignElementValueById(pipelineIndex, "postProcLogLevel", component["log_level"]);
    assignElementValueById(pipelineIndex, "PluginPostProcEnabled", "true");

    let element = document.getElementById("PluginPostProc" + ID_DELIMITER + "postProcType");
    element.value = postProcType;
    let option = element.options[element.selectedIndex];
    if (typeof option !== 'undefined') {
        assignElementValueById(pipelineIndex, "PluginPostProcInfo", option.text);
    }
}


function parseLogConfig(component) {
    document.getElementById("logDisplayEnabled").checked = component["log_display"];
    document.getElementById("logLevel").value = component["log_level"];
}


function clearHtml() {
    document.getElementById("logDisplayEnabled").checked = false;
    document.getElementById("logLevel").value = "ERROR";
    $("#pipelinesRowsTable").empty();
}


function stackSdkConfigParse(configJsonString) {
    clearHtml();
    let configJson = JSON.parse(configJsonString);
    if (!Object.hasOwn(configJson, "components")) {
        utilsMessageError("JSON config missing components field.");
        return;
    }
    
    let allLayers = configJson["components"];

    for (let componentIndex = 0; componentIndex < allLayers.length; ++componentIndex) {
        let component = allLayers[componentIndex];
        if (Object.hasOwn(component, "component_type")) {
            let componentType = component["component_type"];
            switch (componentType) {
                case "Camera":
                    parseCamera(component);
                    break;
                case "DataReader":
                    parseDataReader(component);
                    break;
                case "VideoEncoder":
                    parseVenc(component);
                    break;
                case "OpticalFlow":
                    parseOpticalFlow(component);
                    break;
                case "CL2DFlex":
                case "GL2DFlex":
                case "Remap":
                case "C2D":
                    parsePreProc(component);
                    break;
                case "Qnn":
                    parseInference(component);
                    break;
                case "PostProcCenternet":
                    parsePostProc(component);
                    break;
                case "TinyViz":
                    // currently nothing need to parse
                    break;
                case "LogConfig":
                    parseLogConfig(component);
                    break;
            }
        }
    }
}
