<script type="text/javascript">
    addBundleStylesheetToElement("ui/sample-apps/ridehal/styles/rh-config.css", "#rh-config-manage-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-config-manage.js", "#rh-config-manage-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-config-create.js", "#rh-config-manage-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-config-parse.js", "#rh-config-manage-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-config-edit.js", "#rh-config-manage-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-gen-sh.js", "#rh-config-manage-tab");
</script>

<div class="manage-config-container">
    <div class="banner banner-config-perception">
        <table class="banner-container">
            <tr>
                <td><img src="icons/nav/info.svg" class="banner-icon deployed-icon" /></td>
                <td class="banner-message deployed-message" id="deployedProfileMeta">No config detected on platform.
                    Please deploy a profile first.  Only one profile can be deployed at a time.
                </td>
            </tr>
        </table>
    </div>

    <div class="button create-new-config-button">
        <button type="button" class="btn button-small" id="createNewConfigButton">
            <img src="icons/nav/add_plus.svg" class="button-small-icon" />
            <span class="button-small-label">Create New Configuration</span>
        </button>
    </div>
    <div class="table-responsive available-config-table">
        <table class="table accordian">
            <thead class="table-header config-table-header">
                <tr class="config-table-header-row">
                    <th scope="col" class="config-name-header">Configuration Name</th>
                    <th scope="col" class="text-center">Date Modified</th>
                    <th scope="col"></th>
                </tr>
            </thead>
            <tbody class="table-body config-table-body scrollbar">
                <tr class="table-row custom-config-profile-table-row" style="display:none">
                    <td class="text profile-name">profile name</td>
                    <td class="text text-center date">date</td>
                    <td>
                        <div class="button profile-button-group">
                            <button class="btn button-small profile-deploy-button" profilename="">
                                <span class="button-small-label" profilename="">Deploy</span>
                            </button>
                            <button class="btn button-small profile-delete-button" profilename="">
                                <span class="button-small-label" profilename="">Delete</span>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="kpi-output pt-3">
        <div class="text-heading">
            <span>Output</span>
        </div>
        <div class="log-container">
            <div class="log-box">
                <div id="sampleAppsOutput" class="log-output text-wrap scrollbar"></div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade edit-config-modal" id="cloneProfileModal" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>Clone Profile</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <label for="cloneProfileFromName" class="col-auto col-form-label">From Profile Name</label>
                    <div class="col-auto form-input">
                        <input class="form-control" id="cloneProfileFromName" name="cloneProfileFromName" value="" disabled required />
                    </div>
                </div>
                <div class="row">
                    <label for="cloneProfileToName" class="col-auto col-form-label">To Profile Name</label>
                    <div class="col-auto form-input">
                        <input class="form-control" id="cloneProfileToName" name="cloneProfileToName" value="" required />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="If this profile name already exists, it will be overwritten." />
                </div>
                <div class="row">
                    <pre id="cloneProfileContent" name="cloneProfileContent" value="" style="display:none" required />
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small clone-profile-button" type="button">
                        <span class="button-small-label">Clone</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="editConfigModal" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content scrollbar">
            <div aria-hidden="true" id="editConfigProfileHeader">
                <div class="modal-header">
                    <span>Edit config for profile: <span class="current-profile-name"></span></span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>

            <div aria-hidden="true" id="createConfigProfileHeader">
                <div class="modal-header">
                    <span>Create Profile</span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <span class="create-profile-subtext">Create configurable perception profile</span>
                <div class="row mt-3">
                    <label for="profileName" class="col-auto col-form-label">Profile Name</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="text" class="form-control" id="profileName" name="profileName" required />
                    </div>
                </div>
            </div>
            <div class="row">
                <label for="logDisplayEnabled" class="col-auto col-form-label">Enable Log Display</label>
                <div class="col-auto form-input">
                    <input class="form-check-input" type="checkbox" role="switch" id="logDisplayEnabled">
                </div>
            </div>
            <div class="row">
                <label for="logLevel" class="col-auto col-form-label">Log Level</label>
                <div class="col-auto form-input">
                    <select class="form-control log-level" id="logLevel" name="logLevel">
                            <option>ERROR</option>
                            <option>WARN</option>
                            <option>INFO</option>
                            <option>DEBUG</option>
                            <option>VERBOSE</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <label for="pipelines" class="col-auto col-form-label">Pipelines</label>
                <div class="col-auto" colspan="4">
                    <table class="table pipelines-table">
                        <thead class="table-header">
                            <tr>
                                <th style="display:none;">
                                    <span class="table-heading">Pipeline Type</span>
                                </th>
                                <th>
                                    <span class="table-heading">Pipeline ID</span>
                                </th>
                                <th>
                                    <span class="table-heading">Edit</span>
                                </th>
                                <th>
                                    <span class="table-heading">Clone</span>
                                </th>
                                <th>
                                    <span class="table-heading">Delete</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body" id="pipelinesRowsTable"></tbody>
                    </table>
                    <div class="button">
                        <button class="btn button-small add-new-pipeline-button" id="addNewPipelineButton" type="button" pipelinename="" numofpipelines="">
                            <img src="icons/nav/add_plus.svg" class="button-small-icon">
                            <span pipelinename="" class="button-small-label">Add Pipeline</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal" id="cancelCustomProfileButton">
                    <span class="button-small-label">Cancel</span>
                </button>
                <button class="btn button-small" type="button" id="saveCustomProfileButton">
                    <span class="button-small-label">Save</span>
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-pipeline-modal" id="cameraPipelineModal" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <span>Edit pipeline configuration</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="row">
                <div class="pipeline-profile-name col-sm-4">
                    <label>Profile</label>
                    <input class="form-control" class="form-control" id="pipelineProfileName" name="pipelineProfileName" value="" required disabled>
                </div>
                <div class="pipeline-id col-sm-4">
                    <label>Pipeline ID</label>
                    <input class="form-control" class="form-control" type="number" id="pipelineId" name="pipelineId" value="0" required disabled>
                </div>
                <div class="pipeline-type col-sm-4" style="display:none;">
                    <label>Pipeline Type</label>
                    <select class="form-control" id="pipelineType" disabled >
                        <option value="camera" selected>Camera</option>
                    </select>
                </div>
            </div>
            <div class="create-config-pipeline-div scrollbar modal-body" id="editConfigPipeline">
                <form id="pluginForm">
                    <div class="plugin-cards container-plugin-select-bg" id="qridePlugins">
                        <div class="row">
                            <span class="qrideTitle">Ride Platform</span>
                        </div>
                    </div>
                    <div class="button pipeline-button-group gap-2 d-flex justify-content-md-start me-4 mt-2">
                        <button class="btn button-small" type="button" id="cancelPipelineButton" data-bs-dismiss="modal">
                            <span class="button-small-label">Cancel</span>
                        </button>
                        <button class="btn button-small" type="button"  id="savePipelineButton">
                            <span class="button-small-label">Save</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="parameterConfigModalPluginInput" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>Input</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <label for="PluginInput--inputType" class="col-auto col-form-label">Input Type</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginInput--inputType" name="inputType">
                            <option value="datareader">Data Reader</option>
                            <option value="camera">Camera</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderType" class="col-auto col-form-label">Data Reader Type</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginInput--dataReaderType" name="dataReaderType">
                            <option value="image">Image</option>
                            <option value="tensor">Tensor</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderTensorType" class="col-auto col-form-label">Tensor Type</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginInput--dataReaderTensorType" name="dataReaderTensorType">
                            <option value="int8">int8</option>
                            <option value="int16">int16</option>
                            <option value="int32">int32</option>
                            <option value="int64">int64</option>
                            <option value="uint8">uint8</option>
                            <option value="uint16">uint16</option>
                            <option value="uint32">uint32</option>
                            <option value="uint64">uint64</option>
                            <option value="float16">float16</option>
                            <option value="float32">float32</option>
                            <option value="float64">float64</option>
                            <option value="sfixed_point8">sfixed_point8</option>
                            <option value="sfixed_point16">sfixed_point16</option>
                            <option value="sfixed_point32">sfixed_point32</option>
                            <option value="ufixed_point8">ufixed_point8</option>
                            <option value="ufixed_point16">ufixed_point16</option>
                            <option value="ufixed_point32">ufixed_point32</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderTensorDims" class="col-auto col-form-label">Tensor Dimensions</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="text" id="PluginInput--dataReaderTensorDims" name="dataReaderTensorDims" />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="In format N,H,W,C, N,S,C, N,C, or N depending on number of dimensions." />
                </div>
                <div class="row">
                    <label for="PluginInput--cameraChannel" class="col-auto col-form-label">Channel</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginInput--cameraChannel" name="cameraChannel" value="0" required />
                    </div>
                </div>
                <div class="row">
                    <label for="PluginInput--inputWidth" class="col-auto col-form-label">Width</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginInput--inputWidth" name="inputWidth" value="1920" required />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Image width in pixels." />
                </div>
                <div class="row">
                    <label for="PluginInput--inputHeight" class="col-auto col-form-label">Height</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginInput--inputHeight" name="inputHeight" value="1020" required />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Image height in pixels." />
                </div>
                <div class="row">
                    <label for="PluginInput--inputFormat" class="col-auto col-form-label">Format</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginInput--inputFormat" name="inputFormat">
                            <option value="nv12">nv12</option>
                            <option value="uyvy">uyvy</option>
                            <option value="p010">p010</option>
                            <option value="rgb">rgb</option>
                            <option value="bgr">bgr</option>
                            <option value="nv12_ubwc">nv12_ubwc</option>
                        </select>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Image format." />
                </div>
                <div class="row">
                    <label for="PluginInput--cameraIspUseCase" class="col-auto col-form-label">ISP Use Case</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="PluginInput--cameraIspUseCase" name="cameraIspUseCase" value="3" />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Optional." />
                </div>
                <div class="row">
                    <label for="PluginInput--cameraRequestMode" class="col-auto col-form-label">Request Mode</label>
                    <div class="col-auto form-input">
                        <input class="form-check-input" type="checkbox" id="PluginInput--cameraRequestMode" name="cameraRequestMode" />
                    </div>
                </div>
                <div class="row">
                    <label for="PluginInput--inputFrameRate" class="col-auto col-form-label">Frame Rate</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginInput--inputFrameRate" name="inputFrameRate" value="30" required />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The desired frame rate (fps)." />
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderVideoFile" class="col-auto col-form-label">Video File</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="text" id="PluginInput--dataReaderVideoFile" name="dataReaderVideoFile" />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Optional source .mp4 video file (from $RUNTIME/datareader/video/) that will be processed and used for the simulated camera." />
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderVideoFps" class="col-auto col-form-label">Video Frame Rate (fps)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="PluginInput--dataReaderVideoFps" name="dataReaderVideoFps" value="30" min="1" max="99" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The frame rate (fps) of the video to process." />
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderVideoStartSec" class="col-auto col-form-label">Video Start Offset (sec)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="PluginInput--dataReaderVideoStartSec" name="dataReaderVideoStartSec" value="0" min="0" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Skip this many seconds into the video before start processing.  Zero indicates start from the beginning." />
                </div>
                <div class="row">
                    <label for="PluginInput--dataReaderVideoLenSec" class="col-auto col-form-label">Video Length (sec)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="PluginInput--dataReaderVideoLenSec" name="dataReaderVideoLenSec" value="30" min="1" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The length in seconds of video to process, produce from the specified Start." />
                </div>
                <div class="row">
                    <label for="PluginInput--inputEnableVisualization" class="col-auto col-form-label">Enable Visualization</label>
                    <div class="col-auto form-input">
                        <input class="form-check-input" type="checkbox" id="PluginInput--inputEnableVisualization" name="inputEnableVisualization" />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Camera or Data Reader with a Video File can be visualized on display connected to platform." />
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal preproc-modal" id="parameterConfigModalPluginPreProc" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar" id="preProcModalContent">
            <div class="modal-header">
                <span>PreProcessing </span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="plugin-cards container-plugin-select-md scrollbar" id="preprocPluginOptions">
                    <!--
                    <div class="plugin-column">
                        <div class="plugin-row">
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-check form-switch">
                                        <label class="card-title">Undistortion</label>
                                        <input class="form-check-input" type="checkbox" role="switch" id="PluginPreProc--UndistortionEnabled">
                                    </div>
                                    <div style="display:flex">
                                        <span class="paramGear">
                                            Configure Parameters
                                            <img src="icons/nav/settings_gear.svg" id="parameterUndistortion" class="preproc-config-params" />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    -->
                    <div class="plugin-column">
                        <div class="plugin-row">
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-check form-switch">
                                        <label class="card-title">ROI Crop</label>
                                        <input class="form-check-input" type="checkbox" role="switch" id="PluginPreProc--ROICropEnabled">
                                    </div>
                                    <div style="display:flex">
                                        <span class="paramGear">
                                            Configure Parameters
                                            <img src="icons/nav/settings_gear.svg" id="parameterROICrop" class="preproc-config-params" />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="plugin-column">
                        <div class="plugin-row">
                            <div class="card">
                                <div class="card-body">
                                    <div class="form-check form-switch">
                                        <label class="card-title">Target</label>
                                        <input class="form-check-input" type="checkbox" role="switch" id="PluginPreProc--TargetEnabled" checked style="display:none" >
                                    </div>
                                    <div style="display:flex">
                                        <span class="paramGear">
                                            Configure Parameters
                                            <img src="icons/nav/settings_gear.svg" id="parameterTarget" class="preproc-config-params" />
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="modalPreprocessUndistortion" data-bs-backdrop="static" data-bs-keyboard="false"
tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-sub-dialog">
    <div class="modal-content scrollbar">
        <div class="modal-header">
            <span> Undistortion </span>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
            <div class="row">
                <label for="PreprocessUndistortion--undistortionRemapTableX" class="col-auto col-form-label">Remap Table X</label>
                <div class="col-auto form-input">
                    <select class="form-control" id="PreprocessUndistortion--undistortionRemapTableX" name="undistortionRemapTableX">
                        <option value="">(disabled)</option>
                    </select>
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Remap table X from /runtime/config/ridehal/remap-tables/" />
            </div>
            <div class="row">
                <label for="PreprocessUndistortion--undistortionRemapTableY" class="col-auto col-form-label">Remap Table Y</label>
                <div class="col-auto form-input">
                    <select class="form-control" id="PreprocessUndistortion--undistortionRemapTableY" name="undistortionRemapTableY">
                        <option value="">(disabled)</option>
                    </select>
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Remap table Y from /runtime/config/ridehal/remap-tables/" />
            </div>
            <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                    <span class="button-small-label">Cancel</span>
                </button>
                <button class="btn button-small saveParams" type="button">
                    <span class="button-small-label">Save</span>
                </button>
            </div>
        </div>
    </div>
</div>
</div>

<div class="modal fade edit-config-modal" id="modalPreprocessROICrop" data-bs-backdrop="static" data-bs-keyboard="false"
tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-sub-dialog">
    <div class="modal-content scrollbar">
        <div class="modal-header">
            <span> ROI Crop </span>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
            <div class="row">
                <label for="PreprocessROICrop--roiX" class="col-auto col-form-label">ROI X</label>
                <div class="col-auto form-input">
                    <input class="form-control" type="number" min="0" id="PreprocessROICrop--roiX" name="roiX" value="0" required />
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Relative to input." />
            </div>
            <div class="row">
                <label for="PreprocessROICrop--roiY" class="col-auto col-form-label">ROI Y</label>
                <div class="col-auto form-input">
                    <input class="form-control" type="number" min="0" id="PreprocessROICrop--roiY" name="roiY" value="0" required />
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Relative to input." />
            </div>
            <div class="row">
                <label for="PreprocessROICrop--roiWidth" class="col-auto col-form-label">ROI Width</label>
                <div class="col-auto form-input">
                    <input class="form-control" type="number" min="1" id="PreprocessROICrop--roiWidth" name="roiWidth" value="1920" required />
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Relative to input." />
            </div>
            <div class="row">
                <label for="PreprocessROICrop--roiHeight" class="col-auto col-form-label">ROI Height</label>
                <div class="col-auto form-input">
                    <input class="form-control" type="number" min="1" id="PreprocessROICrop--roiHeight" name="roiHeight" value="1020" required />
                </div>
                <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                    data-bs-toggle="tooltip" title="Relative to input." />
            </div>
            <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                    <span class="button-small-label">Cancel</span>
                </button>
                <button class="btn button-small saveParams" type="button">
                    <span class="button-small-label">Save</span>
                </button>
            </div>
        </div>
    </div>
</div>
</div>

<div class="modal fade edit-config-modal" id="modalPreprocessTarget" data-bs-backdrop="static" data-bs-keyboard="false"
tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-sub-dialog">
<div class="modal-content scrollbar">
    <div class="modal-header">
        <span>Target</span>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
    </div>
    <div class="modal-body">
        <div class="row">
            <label for="PreprocessTarget--preProcType" class="col-auto col-form-label">PreProc Type</label>
            <div class="col-auto form-input">
                <select class="form-control pluginInfoField" id="PreprocessTarget--preProcType" name="preProcType">
                    <option value="CL2DFlex">CL2DFlex</option>
                    <!-- Not currently stable.
                    <option value="GL2DFlex">GL2DFlex</option>
                    -->
                    <option value="Remap">Remap</option>
                    <option value="C2D">C2D</option>
                </select>
            </div>
            <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                data-bs-toggle="tooltip" title="PreProc library to use.  Different library have different capability and limitations." />
        </div>
        <div class="row">
            <label for="PreprocessTarget--targetWidth" class="col-auto col-form-label">Width</label>
            <div class="col-auto form-input">
                <input class="form-control" type="number" min="0" id="PreprocessTarget--targetWidth" name="targetWidth" value="1920" required />
            </div>
            <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                data-bs-toggle="tooltip" title="Target image width in pixels." />
        </div>
        <div class="row">
            <label for="PreprocessTarget--targetHeight" class="col-auto col-form-label">Height</label>
            <div class="col-auto form-input">
                <input class="form-control" type="number" min="0" id="PreprocessTarget--targetHeight" name="targetHeight" value="1020" required />
            </div>
            <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                data-bs-toggle="tooltip" title="Target image height in pixels." />
        </div>
        <div class="row">
            <label for="PreprocessTarget--targetFormat" class="col-auto col-form-label">Format</label>
            <div class="col-auto form-input">
                <select class="form-control" id="PreprocessTarget--targetFormat" name="targetFormat">
                    <option value="rgb">rgb</option>
                    <option value="uint8">uint8</option>
                    <option value="nv12">nv12</option>
                    <option value="p010">p010</option>
                    <option value="nv21">nv21</option>
                    <option value="uyvy">uyvy</option>
                </select>
            </div>
            <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                data-bs-toggle="tooltip" title="Target image format type" />
        </div>
        <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
            <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                <span class="button-small-label">Cancel</span>
            </button>
            <button class="btn button-small saveParams" type="button">
                <span class="button-small-label">Save</span>
            </button>
        </div>
    </div>
</div>
</div>
</div>

<div class="modal fade edit-config-modal" id="parameterConfigModalPluginInference" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>Inference</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <label for="PluginInference--inferenceModel" class="col-auto col-form-label">Model</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginInference--inferenceModel" name="inferenceModel" required>
                            <option></option>
                        </select>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Place compiled neural networks in $RUNTIME_DIR/networks/deploy/qnn/" />
                </div>
                <div class="row">
                    <label for="PluginInference--inferenceBackendCore" class="col-auto col-form-label">Backend Core</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginInference--inferenceBackendCore" name="inferenceBackendCore">
                            <option value="0">HTP 1</option>
                            <option value="1">HTP 2</option>
                        </select>
                    </div>
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="parameterConfigModalPluginPostProc" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>Post Processing</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <label for="PluginPostProc--postProcType" class="col-auto col-form-label">postProcType</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginPostProc--postProcType" name="postProcType">
                            <option value="PostProcCenternet">Centernet</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginPostProc--postProcProcessor" class="col-auto col-form-label">postProcProcessor</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginPostProc--postProcProcessor" name="postProcProcessor">
                            <option value="cpu">CPU</option>
                            <option value="gpu">GPU</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="postProcLogLevel" class="col-auto col-form-label">Log Level</label>
                    <div class="col-auto form-input">
                        <select class="form-control" id="PluginPostProc--postProcLogLevel" name="postProcLogLevel">
                                <option></option>
                                <option>ERROR</option>
                                <option>WARN</option>
                                <option>INFO</option>
                                <option>DEBUG</option>
                                <option>VERBOSE</option>
                        </select>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Optional.  If not blank, sets component instance log level overriding the global log level." />
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="parameterConfigModalPluginVideoEncoder" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>VIDC</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row" style="display:none">
                    <label for="PluginVideoEncoder--vencType" class="col-auto col-form-label">Video Encoder Type</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginVideoEncoder--vencType" name="vencType">
                            <option>H.265</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginVideoEncoder--vencMbps" class="col-auto col-form-label">Video Encoder Rate (Mbps)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginVideoEncoder--vencMbps" name="vencMbps" value="8" required />
                    </div>
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade edit-config-modal" id="parameterConfigModalPluginEVAOF" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content scrollbar">
            <div class="modal-header">
                <span>EVA Optical Flow</span>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <label for="PluginEVAOF--evaOfFrameRate" class="col-auto col-form-label">Frame Rate</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" min="0" id="PluginEVAOF--evaOfFrameRate" name="evaOfFrameRate" value="30" />
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The frame rate (fps)." />
                </div>
                <div class="row">
                    <label for="PluginEVAOF--evaOfMode" class="col-auto col-form-label">EVA Mode</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginEVAOF--evaOfMode" name="evaOfMode">
                            <option value="dsp">DSP</option>
                            <option value="cpu">CPU</option>
                            <option value="disable">Disabled</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginEVAOF--evaOfDirection" class="col-auto col-form-label">Direction</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginEVAOF--evaOfDirection" name="evaOfDirection">
                            <option value="forward">Forward</option>
                            <option value="backward">Backward</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <label for="PluginEVAOF--evaOfStepSize" class="col-auto col-form-label">Step Size</label>
                    <div class="col-auto form-input">
                        <select class="form-control pluginInfoField" id="PluginEVAOF--evaOfStepSize" name="evaOfStepSize">
                            <option>0</option>
                            <option>1</option>
                            <option>2</option>
                        </select>
                    </div>
                </div>
                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small saveParams" type="button">
                        <span class="button-small-label">Save</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
