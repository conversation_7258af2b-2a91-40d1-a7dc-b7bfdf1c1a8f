<script type="text/javascript">
    addBundleStylesheetToElement("ui/sample-apps/ridehal/styles/rh-config.css", "#rh-qnn-test-tab");
    addBundleScriptToElement("ui/sample-apps/ridehal/scripts/rh-qnn-test.js", "#rh-qnn-test-tab");
</script>

<div class="manage-config-container">
    <div class="banner banner-config-perception">
        <table class="banner-container">
            <tr>
                <td><img src="icons/nav/info.svg" class="banner-icon deployed-icon" /></td>
                <td class="banner-message deployed-message" id="deployedProfileMeta">No config detected on platform.
                    Please deploy a profile first.  Only one profile can be deployed at a time.
                </td>
            </tr>
        </table>
    </div>

    <div class="row qnn-test-input-row">
        <label for="PluginInference--inferenceModel" class="col-auto col-form-label">Model</label>
        <div class="col-auto form-input">
            <select class="form-control pluginInfoField" id="PluginInference--inferenceModel" name="inferenceModel" required>
                <option></option>
            </select>
        </div>
        <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
            data-bs-toggle="tooltip" title="Place compiled neural networks in $RUNTIME_DIR/networks/deploy/qnn/" />
    </div>
    <div class="qnn-test-analyze-button">
        <button type="button" class="btn run-profile-button" id="rhQnnTestAnalyzeButton">
            <img src="icons/av/play.svg" class="run-profile-icon" />
            <span class="button-label">Analyze</span>
        </button>
    </div>

    <div class="row">
        <!-- enough dummy entries to reuse create a profile to deploy the network to analyze -->
        <input id="0--inputType" name="0--inputType" value="datareader" style="display:none">
        <input id="0--dataReaderType" name="0--dataReaderType" value="image" style="display:none">
        <input id="0--PluginPreProcEnabled" name="0--PluginPreProcEnabled" value="false" style="display:none">
        <input id="0--inferenceModel" name="0--inferenceModel" value="" style="display:none">
        <input id="0--inferenceBackendCore" name="0--inferenceBackendCore" value="0" style="display:none">
    </div>

    <div class="kpi-output pt-3">
        <div class="text-heading">
            <span>Output</span>
        </div>

        <div class="progress-container" id="sampleAppsProgressContainer" style="display: none;">
            <div class="progress-banner" id="progressBanner">
                <div class="progress-banner-container">
                    <div class="progress-bar-and-message">
                        <div class="progress">
                            <div class="progress-bar indeterminate" role="progressbar" aria-valuenow="25" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <div class="log-box">
                <div id="sampleAppsOutput" class="log-output text-wrap scrollbar"></div>
            </div>
        </div>
    </div>
</div>
