/* manage-config */

.create-new-config-button {
  display: flex;
  justify-content: end;
  margin: 1rem 0;
  height: 32px;
}

.deployed-message {
  line-height: 18px;
}

.deployed-icon {
  margin: 0.1rem 0.5rem 0.1rem 0.75rem;
}

/* adds a scrollbar to the config list */
.available-config-table tbody.config-table-body {
  display: block;
  max-height: 50vh;
  overflow-y: scroll;
}

.available-config-table thead.config-table-header,
tbody.config-table-body,
tr.config-table-header-row,
tr.custom-config-profile-table-row {
  display: table;
  width: 100%;
  table-layout: fixed;
  vertical-align: middle;
}

.table-body.config-table-body .text {
  font-size: 14px;
  vertical-align: middle;
}

.create-config-profile-header {
  color: #222222;
  font-family: "qcnext";
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.3px;
  line-height: 25px;
  border-bottom: 0px;
}

.create-config-profile-subtext {
  color: #677284;
  font-family: "qcnext";
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  margin-bottom: 1rem;
}

/* adds a scrollbar to the profile list */
.available-profiles-table tbody.profile-table-body {
  display: block;
  max-height: 50vh;
  overflow-y: scroll;
}

.config-name-header {
  padding-left: 2.5rem !important;
}

.profile-name {
  cursor: pointer;
}

.profile-button-group {
  gap: 0.5rem;
  display: flex;
  justify-content: end;
  margin-right: 1rem;
}

.profile-button-group .button-small-label {
  padding: 0rem 1.1rem;
}

.profile-button-group .profile-clone-button {
  border: 1px solid #000000;
  background-color: #ffffff;
}

.profile-clone-button .button-small-label {
  color: #000000;
}

.profile-button-group .profile-delete-button {
  border: 1px solid #e71324;
  background-color: #ffffff;
}

.profile-delete-button .button-small-label {
  color: #e11529;
  font-weight: 600;
}

.log-level {
    width: 100px;
}

.create-config-pipeline-div {
  max-height: 85vh;
  max-width: 85vw;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 0px;
}

.create-config-pipeline-div .plugin-cards, .preproc-modal .plugin-cards {
  display: flex;
  margin-left: 0.25rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.create-config-pipeline-div .plugin-cards .plugin-column, .preproc-modal .plugin-cards .plugin-column {
  display: flex;
  flex-direction: column;
  margin-bottom: auto;
  position: relative;
  top: 50px;
  left: -50px;
  margin-right: 1.7rem;
}

.create-config-pipeline-div .plugin-cards .plugin-row, .preproc-modal .plugin-cards .plugin-row {
  display: flex;
  flex-direction: row;
  align-items: start;
  width: 230px;
  margin-bottom: 1rem;
}

.plugin-row .card {
  width: inherit;
  height: inherit;
  margin: 0 !important;
  padding: 1.3rem 0.5rem;
}

/* create arrow head before plugin cards */
.plugin-row div.card::before {
  content: '';
  position:absolute;
  width: 0px;
  height: 0px;
  top: 50%;
  left: -10px;
  border-style: solid;
  border-width: 5px 0px 5px 10px;
  border-color:  transparent transparent transparent #979797;
  transform: translateY(-50%);
}

/* skip first plugin column in pipeline modal
 * skip first plugin column in prepoc modal
*/
.container-plugin-select-bg .plugin-column:nth-child(2) .plugin-row .card::before,
.container-plugin-select-md .plugin-column:first-child .plugin-row .card::before {
  border: 0px !important;
  border-color: transparent !important;
}

.plugin-column:not(:last-child) .plugin-row:first-child div.card::after {
  content: '';
  height: 1px;
  width: 20px;
  background-color: #979797;
  position: absolute;
  top: 50%;
  left: 230px;
}

/* general arrow body, skip first row */
.plugin-row:nth-child(n+2)::before {
  height: 142px;
  width: 14px;
  content: '';
  position: absolute;
  left: -15px;
  border-style: solid;
  border-width: 1px;
  border-color: transparent transparent #979797 #979797;
  transform: translateY(-54%);
}

/* arrow body vertical position for row 2 */
.plugin-row:nth-child(2)::before {
  top: 33%;
}

/* arrow body vertical position for row 3 */
.plugin-row:nth-child(3)::before {
  top: 66%;
}

.container-plugin-select-bg {
  box-sizing: border-box;
  height: 500px;
  width: 1028px;
  border: 1px solid #DAE2E5;
  border-radius: 4px;
  background-color: #FFFFFF;
}

.container-plugin-select-md {
  box-sizing: border-box;
  height: 280px;
  width: 1028px;
  border: 1px solid #DAE2E5;
  border-radius: 4px;
  background-color: #FFFFFF;
  overflow-x:hidden;
  overflow-y: hidden;
  padding: 1rem;
}

.qrideTitle {
  height: 30px;
  width: 89px;
  color: #677284;
  font-family: "Qualcomm Next";
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  line-height: 24px;
  white-space: nowrap;
}

.form-switch .form-check-input {
  height: 24px;
  width: 48px;
}

.plugin-row .card-title {
  height: 25px;
  width: 150px;
  color: #222222;
  font-family: "Qualcomm Next";
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.2px;
  line-height: 20px;
  margin-left: 1rem;
}

.edit-config-pipeline-modal .modal-dialog {
  max-width: 95vw;
}

.preproc-modal .modal-dialog {
  max-width: 95vw;
}

.edit-config-pipeline-modal .modal-dialog .modal-content {
  padding: 1.5rem;
  width: fit-content;
  margin: auto;
}

.preproc-modal .modal-dialog .modal-content {
  width: fit-content;
  margin: auto;
}

.preproc-modal .plugin-cards .plugin-column{
  top: 10px !important;
  left: 0px !important; 
}

.edit-config-pipeline-modal .modal-dialog .modal-content .modal-header {
  color: #222222;
  font-family: "qcnext";
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.3px;
  line-height: 25px;
  border-bottom: 0px;
  padding: 0px;
}

.edit-config-pipeline-modal .modal-dialog .modal-content .create-pipeline-subtext,
.edit-config-modal .modal-dialog .modal-content .create-pipeline-subtext{
  /* height: 24px; */
  /* width: 708px; */
  color: #677284;
  font-family: "qcnext";
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  margin-bottom: 1rem;
}

.edit-config-pipeline-modal .modal-dialog .modal-content .modal-body {
  color: #677284;
  font-family: "qcnext";
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  border-bottom: 0px;
}

.edit-config-pipeline-modal .modal-dialog .modal-content {
  border-radius: 6px;
  background-color: #ffffff;
}

.edit-config-modal .modal-dialog .modal-content.scrollbar {
  max-height: 80vh;
  overflow-y: scroll;
}
.edit-config-modal .pipelines-table {
  width: 45rem;
}

.edit-config-pipeline-modal .button-small-label {
  padding: 0rem 1.1rem;
}

.paramGear {
  margin: 10px;
}

.pipeline-id, .pipeline-type {
  width: 180px;
}

.pipeline-id input, .pipeline-type select {
  width: 150px;
}

.pipeline-profile-name {
  width: 330px;
}

.pipeline-profile-name input {
  width: 300px;
}

.edit-config-modal {
  background-color: rgb(0,0,0,0.5);
}

.plugin-row .card .card-body .plugin-info {
  display: flex;
}

.run-profile-button .button-label {
    width: fit-content;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.5px;
    line-height: 18px;
    color: #ffffff;
    padding: 0rem 1rem 0 0.3rem;
}

.qnn-test-input-row {
    /* allow analyze button to be on the same row */
    display: inline-flex;
}

.qnn-test-analyze-button {
    /* allow analyze button to be on the same row */
    display: inline-block;
    padding-left: 50px;
}
