<!-- SAMPLE APPS DOCUMENTATION PAGE TEMPLATE -->
<script type="text/javascript">
    addBundleScriptToElement("ui/sample-apps/scripts/sample-apps-documentation.js", "#documentation-tab");
</script>

<div class="sample-app-documentation">
    <div id="documentationText">
    </div>
    <div class="sample-code">
        <span class="text-heading">Sample Code</span>
        <div class="module-example-code text">
            <table class="table dir-content-table">
                <thead class="example-code-current-location">
                    <tr>
                        <th class="example-code-select-header">
                            <select class="form-select example-code-select" id="sampleCode"></select>
                        </th>
                        <th scope="col" class="current-location-path" id="currLocation"></th>
                    </tr>
                </thead>
                <tbody id="dirContent"></tbody>
            </table>
            <pre class="file-content code" id="fileContent" style="display: none"></pre>
        </div>
    </div>
</div>
