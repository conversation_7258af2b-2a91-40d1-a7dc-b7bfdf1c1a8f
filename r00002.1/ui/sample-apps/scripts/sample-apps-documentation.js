// Copyright (c) 2022 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.


function fillCodeTab() {
    $("#sampleCode").empty();
    var sampleCode = document.getElementById("sampleCode");
    if (!sampleCode) {
        return;
    }

    sampleApps[sampleAppId]["sampleCodeDirectory"].forEach((sampleCodeDir, index) => {
        var option = document.createElement("option");
        option.textContent = "Sample Code " + (index + 1);
        option.value = sampleCodeDir;
        sampleCode.appendChild(option);
    });

    let sampleCodeDir = $("#sampleCode").val();
    browseDirSampleCode(sampleCodeDir);

    $('#sampleCode').change(function () {
        sampleCodeDir = ($(this).val());
        browseDirSampleCode(sampleCodeDir);
    })
}

$(document).ready(function () {
    $("#documentationText").append(sampleApps[sampleAppId]["documentation"]);
    fillCodeTab();
});

