// Copyright (c) 2022-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// Extend index.js tables without requiring a Core update.
tabDisplayNameDict["rh-config-manage-tab"] = "Manage Configuration";
tabDisplayNameDict["rh-run-tab"] = "Run Profile on Platform";
tabDisplayNameDict["rh-qnn-test-tab"] = "QNN Test";
tabDisplayNameDict["rh-documentation-tab"] = "Documentation";
tabDisplayNameDict["download-tab"] = "Download";

tabHtmlPageDict["rh-config-manage-tab"] = KEY_BUNDLE_PATH + "/ui/sample-apps/ridehal/rh-config-manage.html";
tabHtmlPageDict["rh-qnn-test-tab"] = KEY_BUNDLE_PATH + "/ui/sample-apps/ridehal/rh-qnn-test.html";
tabHtmlPageDict["rh-run-tab"] = KEY_BUNDLE_PATH + "/ui/sample-apps/ridehal/rh-run.html";
tabHtmlPageDict["rh-documentation-tab"] = KEY_BUNDLE_PATH + "/ui/sample-apps/ridehal/rh-documentation.html";
tabHtmlPageDict["download-tab"] = KEY_BUNDLE_PATH + "/ui/sample-apps/sample-apps-download.html";

sectionIdTabTree["sample-apps"]["qnnCamPer1"] = ["demo-tab", "documentation-tab"];
sectionIdTabTree["sample-apps"]["dataReader4"] = ["demo-tab", "documentation-tab"];
sectionIdTabTree["sample-apps"]["customJSON"] = ["demo-tab", "documentation-tab"];
sectionIdTabTree["sample-apps"]["customCameraPipeline"] = ["rh-config-manage-tab", "rh-qnn-test-tab", "rh-run-tab", "rh-documentation-tab"];
sectionIdTabTree["sample-apps"]["staticReader"] = ["demo-tab", "documentation-tab"];
sectionIdTabTree["sample-apps"]["downloadApp"] = ["download-tab"];

// Extend utils.js table without requiring a Core update.
checkInstalledMap["ridehal"] = {
        description: "RideHal app",
        targetCmd: "ls /var/data/ridehal/bin",
        expected: "rhrun",
        referTo: "Please see Platform Toolkit/ Install Apps"
    };
checkInstalledMap["rh-qnn-test"] = {
        description: "RideHal QNN Test app",
        targetCmd: "ls /var/data/ridehal/bin",
        expected: "RideHalQnnTest",
        referTo: "Please see Platform Toolkit/ Install Apps"
    };


/*
- To add a new sample app, define a new app card with introduction text on the sample-apps.html page.
When clicked, the id used there is matched in the sampleApps dictionary below to get
description and preparation steps (More fields can be added here). These fields' values
are then added to the rendered sample app demo tab.
*/

sampleApps = {
    qnnCamPer1: {
        description:
            "This sample provides necessary software modules to perform tasks that assist autonomous driving stack using QNN API."
            + " Key modules: pre processing, computer vision related tasks - cv includes both legacy and ML based "
            + "- post processing of ML inference results to generate results including vehicle detection and classification,"
            + " road and traffic sign detection.",
        preparation:
            "Prerequisites to run this application are:"
            + "<ul>"
                + "<li>One 1H1 camera connected to the QDrive platform.</li>"
                + "<li>QDrive platform connected to the Ubuntu box via USB as per HW setup instructions.</li>"
                + "<li>Console running on the Ubuntu box connected to the QDrive target.</li>"
                + "<li>Application is installed on Target via the “Install Apps” tab on the platform toolkit.</li>"
            + "</ul>",
        configFilename:
            "1qcx-centernet.json",
        availableCommands:
            '<div class="col-auto button gap-2 d-flex">'
                + '<button type="button" class="btn sample-app-button" id="adasStackSdkStart">'
                    + '<img src="icons/av/play.svg" class="sample-app-icon" />'
                    + '<span class="button-label">Start</span>'
                + '</button>'
                + '<button type="button" class="btn sample-app-button" id="adasStackSdkStop">'
                    + '<img src="icons/av/stop.svg" class="sample-app-icon" />'
                    + '<span class="button-label">Stop</span>'
                + '</button>'
            + '</div>',
        sampleCodeDirectory: [
            "adas-sdk:samples/cam-per-qnn",
        ],
        documentation: "",
    },
};

sampleAppId = null;
sampleAppTab = null;

$(".sample-app").on("click", function () {
    // find great grandparent node id - this tells which card was clicked
    sampleAppId = $(this).parents().eq(2).attr("id");
    sampleAppTab = $(this).attr("id");

    // Extract section title from the sample apps card
    let sectionTitle = $("#" + sampleAppId + " #sampleAppName").text();
    $(".section-title").html(sectionTitle);

    // activate go back link
    $(".go-back-link").text("<  Back to Sample Apps");
    $(".go-back-link").on("click", function () {
        $("#sample-apps").trigger("click");
    });
    $(".go-back-link").css("display", "block");

    // add platform controls at the top right corner
    $(".section-button-group").empty();
    $(".section-button-group").load("platform-controls.html");

    // populate sample-apps tabs
    if ($(".no-tabs-content-box").length > 0) {
        addTabsContentBox();
    }
    populateTabs("sample-apps", sampleAppId);
});
