// Copyright (c) 2022-2025 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

checkInstalledOnSoc("adasSample", 1);

if (sampleAppId == "dataReader4") {
    checkInstalledOnSoc(sampleAppId, 1);
}


/*
This file:
- Loads description and preparation steps based on Sample App Id clicked on the sample-apps.html page
- Binds methods to start and stop buttons on the sample apps demo tab , once the page has been loaded.
Since demo tab is the default page opened, this also redirects to the documentation tab.
*/


// documentation tab link clicked
if (sampleAppTab === "documentation") {
    $("#pill-documentation-tab").trigger("click");
    $("#pill-demo-tab").removeClass("active");
    $("#pill-documentation-tab").addClass("active");
    sampleAppTab = null;
}

// demo tab link clicked
$(document).ready(function () {
    $(".sample-app-description p").html(sampleApps[sampleAppId]["description"]);
    $(".preparation-steps").html(sampleApps[sampleAppId]["preparation"]);
    $(".available-commands").html(sampleApps[sampleAppId]["availableCommands"]);

    // only difference of starting each ADAS Stack SDK app should be the config filename
    $("#adasStackSdkStart").on("click", function () {
        adasStackSdkStart(sampleAppId);
        /*
        if (sampleAppId == 'vepp') {
            veppWindow = window.open("http://" + window.location.hostname + ":5001", "_blank");
        }
        */
    });

    // Stopping ADAS Stack SDK should be same regardless of app
    $("#adasStackSdkStop").on("click", function () {
        adasStackSdkStop();
        /*
        if ((typeof veppWindow !== 'undefined') && (veppWindow != null)) {
            veppWindow.close();
        }
        */
    });

    $("#editDeployDataReader4").on("click", function () {
        $('#editVideoDeployModal').modal('show');
    });

    $("#deployDataReader4").on("click", function () {
        $("#editVideoDeployModal").modal("hide");
        deployDataset("DataReader4");
    });

    $("#deployLidarDataset").on("click", function () {
        // TODO switch to publish feedback like DataReader4
        deployDataset("LiDAR");
    });

    $("#deployVeppDataset").on("click", function () {
        // TODO switch to publish feedback like DataReader4
        deployDataset("VEPP");
    });

});


function adasStackSdkStart(sampleAppId) {
    $('#sampleAppsDemoProgressContainer').css("display", "block");
    $("#sampleAppsOutput").empty();
    var startAppsCall = $.ajax({
        type: "POST",
        url: "/adas/startStackSdk/" + sampleApps[sampleAppId]["configFilename"],
        dataType: "json",
        timeout: 120000, // milliseconds
    });

    startAppsCall.done(function (response) {
        $('#sampleAppsDemoProgressContainer').hide();
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess("Started ADAS Stack SDK on platform.");
        } else {
            utilsMessageError(response["err_msg"]);
        }
    });

    startAppsCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsDemoProgressContainer').hide();
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}


function adasStackSdkStop() {
    $('#sampleAppsDemoProgressContainer').css("display", "block");
    $("#sampleAppsOutput").empty();
    var stopAppsCall = $.ajax({
        type: "POST",
        url: "/adas/stopStackSdk",
        dataType: "json",
        timeout: 90000, // milliseconds
    });

    stopAppsCall.done(function (response) {
        $('#sampleAppsDemoProgressContainer').hide();
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess("Stopped ADAS Stack SDK on platform.");
        } else {
            utilsMessageError("Error stopping app. " + response["err_msg"] + " Please check rideconsole.log");
        }
    });

    stopAppsCall.fail(function (xhr, textStatus, error) {
        $('#sampleAppsDemoProgressContainer').hide();
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}


function deployDataset(usecase) {
    let parameters = {};
    parameters["usecase"] = usecase;
    $("#sampleAppsOutput").empty();

    if (usecase === "DataReader4") {
        // TODO refactor this?
        let value = $('#videoFps').val();
        if (value == "") {
            utilsMessageError("Error parameter fps is missing.");
            return;
        }
        parameters["videoFps"] = value;
        
        value = $('#videoStartSec').val();
        if (value == "") {
            utilsMessageError("Error parameter Start Sec is missing.");
            return;
        }
        parameters["videoStartSec"] = value;
        
        value = $('#videoLenSec').val();
        if (value == "") {
            utilsMessageError("Error parameter Length is missing.");
            return;
        }
        parameters["videoLenSec"] = value;
    }

    var deployCall = $.ajax({
        type: "POST",
        url: "/adas/deployDataset/",
        data: parameters,
        dataType: "json",
    });

    deployCall.done(function (response) {
        console.log(response);
        if (response["status"] == "Success") {
            utilsMessageSuccess(usecase + " dataset deployed to platform.");
        } else {
            utilsMessageError("Error deploying dataset: " + response["err_msg"]);
        }
    });

    deployCall.fail(function (xhr, textStatus, error) {
        if (error === "timeout") {
            utilsMessageError("Request timed out. No response from Server");
        } else {
            var errorMessage = xhr.status + ": " + xhr.statusText;
            utilsMessageError("Error - " + errorMessage);
        }
    });
}
