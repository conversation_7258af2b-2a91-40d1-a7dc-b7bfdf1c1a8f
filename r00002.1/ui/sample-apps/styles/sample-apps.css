.sample-apps-container {
	min-height: calc(100vh - 16rem);
}

.sample-apps-card-row {
  /* display: flex; */
  padding: 0 1rem 1rem 1rem;
  /* justify-content: space-between; */
}


.sample-app-demo .log-output {
  height: 30vh!important;
}

.sample-app-documentation .user-guide {
  color: var(--color-blue);
}

.sample-app-documentation .sample-code {
  padding-top: 2.5rem;
}

.sample-app-documentation .sample-code .text {
  padding-top: 1rem;
}

.sample-app-button {
  padding: 6px;
  display: flex;
  border-radius: 24px;
  background-color: var(--color-blue);
}
.sample-app-icon {
  padding: 0.05rem 0.1rem 0 0.1rem;
  margin-left: 0.5rem;
  width: 19px;
  filter: brightness(0) invert(1);
}

.sample-app-button .button-label {
  width: fit-content;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  line-height: 18px;
  padding: 0rem 1rem 0 0.3rem;
}
