<!-- SAMPLE APPS DEMO PAGE TEMPLATE -->
<script type="text/javascript" src="scripts/browse-dir.js"></script>
<script type="text/javascript">
    addBundleScriptToElement("ui/sample-apps/scripts/sample-apps-demo.js", "#demo-tab");
</script>


<div class="sample-app-demo" id="sampleAppDemo">
    <div class="sample-app-description text">
        <!-- Description text -->
        <p></p>
    </div>
    <div class="preparation">
        <div class="text-heading">
            <span>Preparation</span>
        </div>
        <div class="preparation-steps text">
            <!-- Preparation steps -->
            <p></p>
        </div>
    </div>
    <div class="available-commands-container">
        <div class="text-heading">
            <span>Available Commands</span>
        </div>
        <div class="available-commands"></div>
    </div>
    <div class="kpi-output pt-3">
        <div class="text-heading">
            <span>Output</span>
        </div>
    
        <div class="progress-container" id="sampleAppsDemoProgressContainer" style="display: none;">
            <div class="progress-banner" id="progressBanner">
                <div class="progress-banner-container">
                    <div class="progress-bar-and-message">
                        <div class="progress">
                            <div class="progress-bar indeterminate" role="progressbar" aria-valuenow="25" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <div class="log-box">
                <div id="sampleAppsOutput" class="log-output text-wrap scrollbar"></div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade edit-config-modal" id="editVideoDeployModal" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sub-dialog">
        <div class="modal-content">
            <div>
                <div class="modal-header">
                    <span>Edit video parameters for deployment.</span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>

            <form class="modal-body manage-profiles scrollbar" id="videoInputForm">

                <div class="row">
                    <label for="videoFps" class="col-auto col-form-label">Video Frame Rate (fps)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="videoFps" name="videoFps" value="30" min="1" max="99" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The frame rate (fps) of the video to process." />
                </div>

                <div class="row">
                    <label for="videoStartSec" class="col-auto col-form-label">Start Offset (sec)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="videoStartSec" name="videoStartSec" value="0" min="0" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="Skip this many seconds into the video before start processing.  Zero indicates start from the beginning." />
                </div>

                <div class="row">
                    <label for="videoLenSec" class="col-auto col-form-label">Length (sec)</label>
                    <div class="col-auto form-input">
                        <input class="form-control" type="number" id="videoLenSec" name="videoLenSec" value="30" min="1" required/>
                    </div>
                    <img src="icons/nav/help.svg" class="col-auto vc-tooltip-icon"
                        data-bs-toggle="tooltip" title="The length in seconds of video to process, produce from the specified Start." />
                </div>

                <div class="button gap-2 d-flex justify-content-md-end me-4 mt-2">
                    <button class="btn button-small button-outline" type="button" data-bs-dismiss="modal">
                        <span class="button-small-label">Cancel</span>
                    </button>
                    <button class="btn button-small" type="button" id="deployDataReader4">
                        <span class="button-small-label">Deploy Dataset</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
