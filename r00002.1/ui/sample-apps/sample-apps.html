<script type="text/javascript">
    addBundleStylesheetToElement("ui/sample-apps/styles/sample-apps.css", ".no-tabs-content-box");
    addBundleScriptToElement("ui/sample-apps/scripts/sample-apps.js", ".no-tabs-content-box");
</script>


<div class="no-tabs-content-box">
    <div class="sample-apps-container">
        <div class="row sample-apps-card-row">
            <!-- Sample apps card -->
            
            <!-- Reference example -->
            <div class="card" id="qnnCamPer1" style="display:none">
                <div class="card-body">
                    <div class="card-title" id="sampleAppName">1 Camera Perception</div>
                    <p class="card-text">
                        This sample provides necessary software modules to perform tasks that assist autonomous driving stack using QNN API
                        with Centernet ML network and 1 camera input.
                        Key results generated include vehicle detection and classification.
                    </p>
                    <div class="card-links">
                        <a href="#" class="sample-app" id="demo">Demo</a>
                        <a href="#" class="sample-app ms-2" id="documentation">Documentation</a>
                    </div>
                </div>
            </div>

            <div class="card" id="customCameraPipeline">
                <div class="card-body">
                    <div class="card-title" id="sampleAppName">Custom Camera Pipeline</div>
                    <p class="card-text">
                        GUI to assist creating a custom configuration of pipeline(s), allowing configurable image source,
                        pre processing, ml network and post processing.
                    </p>
                    <div class="card-links">
                        <a href="#" class="sample-app" id="manage">Manage</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
