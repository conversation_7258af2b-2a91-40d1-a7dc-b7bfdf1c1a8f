<!-- PLATFORM SDK EXAMPLE PAGE TEMPLATE -->
<script type="text/javascript" src="scripts/browse-dir.js"></script>
<script type="text/javascript">
    addBundleScriptToElement("ui/platform-sdk/scripts/platform-sdk-module-examples.js", "#examples-tab");
</script>

<div class="platform-sdk-module-example" id="sdkModuleExample">
    <div class="module-description text">
        <!-- Description text -->
        <p></p>
    </div>

    <div class="platform-sdk-module-tabs-container">
        <div class="tabs-content-box">
            <ul class="nav nav-pills tabs mb-3" id="tab-pills">
                <li class="nav-item">
                    <a href="#codeTab" class="nav-link tab-name active" id="pillCodeTab" data-bs-toggle="pill">Code</a>
                </li>
                <li class="nav-item">
                    <a href="#commandsTab" class="nav-link tab-name" id="pillCommandsTab"
                        data-bs-toggle="pill">Commands</a>
                </li>
            </ul>
            <div class="tab-content scrollbar" id="tabContent">
                <div class="tab-pane show active" id="codeTab">
                    <div class="module-example-code text">
                        <table class="table dir-content-table">
                            <thead class="example-code-current-location">
                                <tr>
                                    <th class="example-code-select-header">
                                        <select class="form-select example-code-select" id="sampleCode"></select>
                                    </th>
                                    <th scope="col" class="current-location-path" id="currLocation"></th>
                                </tr>
                            </thead>
                            <tbody id="dirContent"></tbody>
                        </table>
                        <pre class="file-content code" id="fileContent" style="display: none"></pre>
                    </div>
                </div>
                <div class="tab-pane" id="commandsTab"></div>
            </div>
        </div>
    </div>
</div>
