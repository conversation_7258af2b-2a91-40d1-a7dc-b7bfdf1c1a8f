<link rel="stylesheet" href="/styles/platform-sdk.css" />
<script type="text/javascript">
    addBundleScriptToElement("ui/platform-sdk/scripts/platform-sdk.js", ".no-tabs-content-box");
</script>

<div class="no-tabs-content-box">
    <div class="modules-container">
        <div class="row modules-card-row">
            <div class="card" id="eva">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">EVA</div>
                    <p class="card-text">
                        Engine for Visual Analytics (EVA) generates motion vector outputs
                        from two input images.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>

            <div class="card" id="fadas">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">FADAS</div>
                    <p class="card-text">
                        FastADAS is a computer vision (CV) library developed by QTI for ADAS
                        applications. It is aimed at providing CV application programming
                        interfaces (API) tuned for QTI automotive chipsets.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>

            <div class="card" id="hogl">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">HOGL</div>
                    <p class="card-text">
                        HOGL stands for 'HOly Grail of Logging'. It is a scalable, high
                        performance and low overhead logging library for C++ and C
                        applications.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>

            <div class="card" id="qcx">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">QCX</div>
                    <p class="card-text">
                        The qcxserver provides frame images from attached cameras. qcarcam_test
                        is a sample client that reads images from qcxserver and relays them
                        to the display if one is connected to the platform.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>

            <div class="card" id="qnn">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">QNN</div>
                    <p class="card-text">
                        Qualcomm Neural Network (QNN), is a software architecture for Artificial
                        Intelligence (AI) on QTIs chipsets and AI acceleration cores.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>

            <div class="card" id="vdds">
                <div class="card-body">
                    <div class="card-title" id="sdkModuleName">vDDS</div>
                    <p class="card-text">
                        Virtual DDS layer on top of real DDS (FastDDS, RTI DDS) with much
                        more efficient intra-process pubsub.
                    </p>
                    <div class="row card-links">
                        <span class="platform-sdk-module-link" id="examples">Examples</span>
                        <span class="platform-sdk-module-link" id="module-documentation">Documentation</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
