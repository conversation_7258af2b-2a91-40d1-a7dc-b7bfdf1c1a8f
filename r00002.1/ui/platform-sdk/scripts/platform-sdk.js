// Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

var KEY_CAM_XML = "{CAM_XML}";

/*
- To add a new sdk module, define a new module card with introduction text on the platform-sdk.html page.
When clicked, the id used there is matched in the sdkModules dictionary below to get
description and preparation steps (More fields can be added here). These fields' values
are then added to the rendered sdk module tabs.
*/

/*
  sdkModules = {
    module-name: {
        description: "",
        sample-code-directory: "[]",
        commands: {
            command-id: [
                command-name,
                command
            ]
        },
        documentation: {
            text:
            external-links: {
                doc-name-text: doc-url,
            }
        }
    }
 }
*/


sdkModules = {
    eva: {
        description:
            "Engine for Visual Analytics (EVA) generates motion vector outputs from two input images.",
        sampleCodeDirectory: [
            "platform:apps/qnx_ap/AMSS/multimedia/eva/test/eva_simple_test",
        ],
        commands: {
            "depthFromStereo": [
                "Depth From Stereo",
                "cd /var/data/evaSimpleTest; evaSimpleTest dfs.xml",
            ],
            "opticalFlow": [
                "Optical Flow",
                "cd /var/data/evaSimpleTest; evaSimpleTest of.xml",
            ],
        },
        documentation: {
            text: "",
            externalLinks: {
                "EVA Programming Guide": "http://agiledocument.qualcomm.com/AgileDocument?itemno=80-PF777-95&rev=C966202",
            }
        },
    },
    fadas: {
        description:
            "FastADAS is a computer vision (CV) library developed by QTI for ADAS applications. It is aimed at providing CV application programming interfaces (API) tuned for QTI automotive chipsets.",
        sampleCodeDirectory: [
            "platform:apps/qnx_ap/AMSS/multimedia/fadas/nnpp/tst/nn_pre_post_proc_app",
            "platform:apps/qnx_ap/AMSS/multimedia/fadas/fadas/tst/app_cpu/one",
            "platform:apps/qnx_ap/AMSS/multimedia/fadas/fadas/tst/app_cpu/p3",
        ],
        commands: {
            "fadasCommand1": [
                "Sample Command 1 (This may take a minute.)",
                "cd /mnt/bin/nnpp; ./nn_pre_post_proc 1 0 1",
            ],
            "fadasCommand2": [
                "Sample Command 2 (API on CPU)",
                "cd /mnt/bin/fadas; ./one",
            ],
            "fadasCommand3": [
                "Sample Command 3 (Processing Pipeline on CPU)",
                "cd /mnt/bin/fadas; ./p3 -i 01_fisheye_1280x1086_uyvy422.yuv -w 1280 -h 1086 --tw 512 --th 512 -c cam_props.csv -o /tmp/test-fadas-p3-output",
            ],
        },
        documentation: {
            text: "",
            externalLinks: {
                "FADAS User Guide": "http://agiledocument.qualcomm.com/AgileDocument?itemno=80-18179-1&rev=C953248",
                "FADAS API": "http://agiledocument.qualcomm.com/AgileDocument?itemno=80-18179-3&rev=C937986",
            }

        },
    },
    hogl: {
        description:
            "HOGL stands for 'HOly Grail of Logging'. It is a scalable, high performance and low overhead logging library for C++ and C applications.",
        sampleCodeDirectory: [
            "https://github.com/maxk-org/hogl",
            "https://github.com/maxk-org/vdds/tree/master/tests",
        ],
        commands: {
            
        },
        documentation: {
            text: "",
            externalLinks: {
                "HOGL User Guide": "https://github.com/maxk-org/hogl/blob/master/README.md",
            }
        },
    },
    qcx: {
        description:
            "The qcxserver provides frame images from attached cameras. qcarcam_test is a sample client that reads images from qcxserver and relays them to the display if one is connected to the platform.",
        sampleCodeDirectory: [
            "platform:apps/qnx_ap/AMSS/multimedia/qcamera/camera_qcx/test/qcarcam_test",
        ],
        commands: {
            "qcxReset": [
                "Reset QCX Server",
                "slay qcxserver; sleep 3; qcxserver &",
            ],
            "qcxCam": [
                "<table class='command-options'><tr>"
                + "<td>Test Camera</td>"
                + "<td><select id='camXml' class='form-control' name='camXml'>"
                    + "<option selected>1cam_isp_usecase_3.xml</option>"
                    + "</select></td>"
                + "<td><input id='camDisplay' class='form-check-input' type='checkbox' role='switch'> Display</td>"
                + "</tr></table>",
                "/mnt/bin/camera/qcarcam_test/qcarcam_test -config=/mnt/bin/camera/qcarcam_test/" + KEY_CAM_XML + " -seconds=15 -nomenu",
            ],
        },
        documentation: {
            text: "",
            externalLinks: {
                "QCX Customization Guide": "http://agiledocument.qualcomm.com/AgileDocument?itemno=80-42846-16&rev=C958034",
            }

        },
    },
    qnn: {
        description:
            "Qualcomm Neural Network (QNN), is a software architecture for Artificial Intelligence (AI) on QTIs chipsets and AI acceleration cores."
            + " The QNN architecture is designed to provide a unified API and modular and extensible per-accelerator libraries which form a reusable basis"
            + " for full stack AI solutions, both QTI’s own and third party frameworks.",
        sampleCodeDirectory: [
            "qnn-sdk:examples/QNN/SampleApp",
        ],
        commands: {
            "qnn-net-run": [
                "qnn-net-run",
                ". /var/data/qnn/qnn-sdk/bin/qnn_env.sh; cd /var/data/qnn/sampleNetwork; cat network_info.txt; $TGT_QNN_ROOT/bin/qnn-net-run --backend $TGT_QNN_ROOT/lib/libQnnHtp.so --retrieve_context program.bin --profiling_level=basic --input_list input.txt; $TGT_QNN_ROOT/bin/qnn-profile-viewer --input_log output/qnn-profiling-data.log; sleep 1",
            ],
        },
        documentation: {
            text: "",
            externalLinks: {
                "QNN SDK Reference Guide": "{QNN_PATH}/qnn/qnn-sdk/docs/QNN/index.html",
                "Release Notes": "{QNN_PATH}/qnn/qnn-sdk/QNN_ReleaseNotes.txt",
            }
        },
    },
    vdds: {
        description:
            "Virtual DDS layer on top of real DDS (FastDDS, RTI DDS) with much more efficient intra-process pubsub.",
        sampleCodeDirectory: [
            "https://github.com/maxk-org/vdds",
            "https://github.com/maxk-org/vdds/tree/master/tests",
        ],
        commands: {
            "ping-test": [
                "Sample Command 1",
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/var/opt/qride/lib/runtime; /var/opt/qride/tests/vdds/ping-test",
            ],
            "domain-test": [
                "Sample Command 2",
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/var/opt/qride/lib/runtime; /var/opt/qride/tests/vdds/domain-test",
            ],
            "multi-test": [
                "Sample Command 3",
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/var/opt/qride/lib/runtime; /var/opt/qride/tests/vdds/multi-test",
            ],
            "topic-test": [
                "Sample Command 4",
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/var/opt/qride/lib/runtime; /var/opt/qride/tests/vdds/topic-test",
            ],
        },
        documentation: {
            text: "",
            externalLinks: {
                "vDDS User Guide": "https://github.com/maxk-org/vdds/blob/master/README.md",
            }
        },
    },
};

sdkModuleId = null;
sdkModuleTab = null;

$(".platform-sdk-module-link").on("click", function () {
    // find great grandparent node id - this tells which card was clicked
    sdkModuleId = $(this).parents().eq(2).attr("id");
    sdkModuleTab = $(this).attr("id");

    // Extract section title from the Platform SDK module card
    let sectionTitle = $("#" + sdkModuleId + " #sdkModuleName").text();
    $(".section-title").html(sectionTitle);

    // activate go back link
    $(".go-back-link").text("<  Back to Platform SDK");
    $(".go-back-link").on("click", function () {
        $("#platform-sdk").trigger("click");
    });
    $(".go-back-link").css("display", "block");

    // populate platform-sdk-modules tabs
    if ($(".no-tabs-content-box").length > 0) {
        addTabsContentBox();
    }
    populateTabs("platform-sdk", sdkModuleId);
});


function updateLayout() {
    let allCards = document.getElementsByClassName("card");
    for (index = 0; index < allCards.length; ++index) {
        let cardId = allCards.item(index).id;
        let key = "platform-sdk-" + cardId;
        let displayValue = "none";
        if (indexFeatures.hasOwnProperty(key) && indexFeatures[key]) {
            // card is enabled
            displayValue = "block";
        }
        $("#" + cardId).css("display", displayValue);
    }
}

updateLayout();

