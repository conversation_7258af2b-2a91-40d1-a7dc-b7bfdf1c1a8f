// Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

// The module selected determines if need to check if corresponding file(s) installed on platform.
switch (sdkModuleId) {
    case "eva":
    case "fadas":
    case "qcx":
    case "qnn":
    case "vdds":
        checkInstalledOnSoc(sdkModuleId, 1);
        break;
            
    default:
        // nothing to verify
}


// documentation tab link clicked
if (sdkModuleTab === "module-documentation") {
    $("#pill-module-documentation-tab").trigger("click");
    $("#pill-examples-tab").removeClass("active");
    $("#pill-module-documentation-tab").addClass("active");

    sdkModuleTab = null;
// compile tab link clicked from QNN
}else if (sdkModuleTab === "compile") {
    $("#pill-compile-tab").trigger("click");
    $("#pill-examples-tab").removeClass("active");
    $("#pill-compile-tab").addClass("active");

    sdkModuleTab = null;
}


function getCameraXmlList() {
    var ajaxCall = $.ajax({
        type: "GET",
        url: "/platform/execShellCmd/" + "1",
        data: { "shellCmd" : "cd /mnt/bin/camera/qcarcam_test; ls *.xml" },
        dataType: "json",
    });

    ajaxCall.done(function (response) {
        console.log(response);
        if (response) {
            if ( (response["status"] == "Success")  ) {
                var select = document.getElementById("camXml");
                let saveValue = select.value;
                $("#camXml").empty();
                let camXmlList = response["result"].split("\n");

                for (var index = 0; index < camXmlList.length; ++index) {
                    let camXml = camXmlList[index];
                    if (camXml != "") {
                        let option = document.createElement("option");
                        option.textContent = camXml;
                        option.value = camXml;
                        select.appendChild(option);
                    }
                }
                select.value = saveValue;
            } else {
                utilsMessageError("Unable to get camera xml list - " + response["err_msg"]);
            }
        }
    });

    ajaxCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        utilsMessageError("Unable to get camera xml list - " + errorMessage);
    });
}


// update command with the selected xml filename
function updateTestCamCmd() {
    let camXml = document.getElementById("camXml").value;
    let cmd = sdkModules[sdkModuleId]["commands"]["qcxCam"][1].replace(KEY_CAM_XML, camXml);
    
    if (!document.getElementById("camDisplay").checked) {
        cmd += " -noDisplay";
    }

    document.getElementById("qcxCamCmd").textContent = cmd;
}


function fillCodeTab() {
    $("#sampleCode").empty();
    var sampleCode = document.getElementById("sampleCode");
    if (!sampleCode) {
        return;
    }

    sdkModules[sdkModuleId]["sampleCodeDirectory"].forEach((sampleCodeDir, index) => {
        var option = document.createElement("option");
        option.textContent = "Sample Code " + (index + 1);
        option.value = sampleCodeDir;
        sampleCode.appendChild(option);
    });

    let sampleCodeDir = $("#sampleCode").val();
    browseDirSampleCode(sampleCodeDir);

    $('#sampleCode').change(function () {
        sampleCodeDir = ($(this).val());
        browseDirSampleCode(sampleCodeDir);
    })
}

$(document).ready(function () {
    $(".module-description p").text(sdkModules[sdkModuleId]["description"]);
    let activeTab = null;
    // default tab
    fillCodeTab();

    // when tabs switched
    $(".nav-pills")
        .find("a")
        .on("show.bs.tab", function (e) {
            activeTab = e.target.id;
            if (activeTab === "pillCommandsTab") {
                $("#commandsTab").empty();
                for (let commandId in sdkModules[sdkModuleId]["commands"]) {
                    $("#commandsTab").append(
                        addCommand(
                            commandId,
                            sdkModules[sdkModuleId]["commands"][commandId][0],
                            sdkModules[sdkModuleId]["commands"][commandId][1]
                        )
                    );

                    if (commandId == "qcxCam") {
                        updateTestCamCmd();
                        $('#camXml').change(function () {
                            updateTestCamCmd();
                        });
                        $('#camDisplay').change(function () {
                            updateTestCamCmd();
                        });
                    }
                }
                // only append output window if commands defined
                if (
                    Object.keys(sdkModules[sdkModuleId]["commands"]).length !== 0
                ) {
                    $("#commandsTab").append(addOutputWindow());
                // HOGL and vDDS link to github repositories
                } else if (["hogl", "vdds"].includes(sdkModuleId)) {
                    $("#commandsTab").append(
                        '<div class="command-text-container">' +
                            '<span class="command-text">Not included in platform build. Build steps: Refer to toolchain/target/aarch64-qos220.</span>' +
                        '</div>'
                    );
                }

                if (sdkModuleId == "qcx") {
                    getCameraXmlList();
                }
            } else if (activeTab === "pillCodeTab") {
                fillCodeTab();
            }
        });

    for (let commandId in sdkModules[sdkModuleId]["commands"]) {
        if (commandId == "qcxCam") {

            $("#commandsTab").on("click", "#" + commandId, function () {
                $("#waitingResponse").on('shown.bs.modal', function (e) {
                    let cmd = document.getElementById("qcxCamCmd").textContent;
                    commandQcxCam(cmd);
                } );
                $("#waitingResponse").modal("show");
            });
            
        } else {

            // generic default handler for most commands that don't need unique handling
            $("#commandsTab").on("click", "#" + commandId, function () {
                $("#waitingResponse").on('shown.bs.modal', function (e) {
                    commandGeneric(sdkModules[sdkModuleId]["commands"][commandId][1]);
                } );
                $("#waitingResponse").modal("show");
            });
            
        }
    }

});


function commandGeneric(shellCmdString) {
    $('#waitingResponse').off('shown.bs.modal');
    $("#commandResult").text("");
    var commandCall = $.ajax({
        type: "GET",
        url: "/platform/execShellCmd/" + 1,
        data: { shellCmd: shellCmdString },
        dataType: "json",
    });

    commandCall.done(function (response) {
        console.log(response);
        $("#waitingResponse").modal("hide");
        if (response["status"] == "Success") {
            $("#commandResult").text(response["result"]);
            utilsMessageSuccess("Sample command sent.");
        } else {
            utilsMessageError(response["err_msg"]);
        }
    });

    commandCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        $("#waitingResponse").modal("hide");
        utilsMessageError("Error - " + errorMessage);
    });
}


function commandQcxCam(shellCmdString) {
    $('#waitingResponse').off('shown.bs.modal');
    $("#commandResult").text("");
    var commandCall = $.ajax({
        type: "GET",
        url: "/platform/execShellCmd/" + 1,
        data: { shellCmd: shellCmdString },
        dataType: "json",
    });

    commandCall.done(function (response) {
        console.log(response);
        $("#waitingResponse").modal("hide");
        if (response) {
            if (response["status"] == "ServerError") {
                utilsMessageError(response["err_msg"]);
            } else if (response["result"].includes("due to an error")) {
                $("#commandResult").text(response["result"]);
                utilsMessageError("Error detected.  Try restart QCX server.");
            } else {
                $("#commandResult").text(response["result"]);
                utilsMessageSuccess("Start Sample command sent.");
            }
        }
    });

    commandCall.fail(function (xhr, textStatus, error) {
        var errorMessage = xhr.status + ": " + xhr.statusText;
        $("#waitingResponse").modal("hide");
        utilsMessageError("Error - " + errorMessage);
    });
}


// command tab
function addCommand(commandId, commandName, command) {
    return (
        '<div class="command-name-button-wrapper">' +
            '<span class="text command-name">' + commandName + "</span>" +
            '<button type="button" class="btn button command-button" id="' + commandId + '">' +
                '<img src="icons/av/play.svg" class="command-icon" />' +
                '<span class="button-label">Run</span>' +
            "</button>" +
        "</div>" +
        '<div class="command-text-container">' +
            '<span class="command-text" id="' + commandId + 'Cmd">' + command + "</span>" +
        "</div>"
    );
}

function addOutputWindow() {
    return (
        '<span class="text output-window-label">Output Window</span>' +
        '<div class="command-output-window">' +
            '<pre class="command-output-text" id="commandResult">Output from Run command appears here.</pre>' +
        "</div>"
    );
}
