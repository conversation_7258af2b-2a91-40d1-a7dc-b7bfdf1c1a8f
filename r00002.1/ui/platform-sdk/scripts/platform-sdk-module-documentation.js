// Copyright (c) 2022-2024 Qualcomm Technologies, Inc.
// All Rights Reserved. Qualcomm Technologies, Inc. Confidential and Proprietary.

$(document).ready(function () {
    let documentationText = sdkModules[sdkModuleId]["documentation"]["text"];
    if (documentationText) {
        $("#sdkModuleDocumentation").html(documentationText);
    }
    let links = sdkModules[sdkModuleId]["documentation"]["externalLinks"]
    for (var linkName in links) {
        $("#sdkModuleDocumentationLinks").append(
            addLink(
                linkName,
                adjustQnnPath(links[linkName]),
            )
        );
    }
});


function addLink(linkName, linkUrl) {
    let adjustedUrl = adjustBundlePath(linkUrl);
    return (
        "<li>" +
            '<a target="_blank" class="module-doc-links" href="' + adjustedUrl + '">' + linkName + "</a>" +
        "</li>"
    );
}
