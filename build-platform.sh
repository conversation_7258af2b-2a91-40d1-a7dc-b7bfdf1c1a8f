#!/bin/bash -x

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

CHIPCODE_TAG="r00002.1"
BUILD_DIR="/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/${CHIPCODE_TAG}"

# QNX Stuff
QNX_DIR="${HOME}/qnx" # Dir which has qnxsoftwarecenter
QNX_LICENSE_DIR="${HOME}/.qnx/license" 

# Create our own QOS800 prebuilt dir then just mount it to the container
# Once the QNX toolchain issues are resolved, we can remove this and let the download-compile-build.sh script handle it
PREBUILT_DIR="${SCRIPT_DIR}/qnx-patches/prebuilt_sdp800"
BUNDLE_DIR="${SCRIPT_DIR}/${CHIPCODE_TAG}"

if [ ! -d "$PREBUILT_DIR" ]; then
    echo "Prebuilt not found at $PREBUILT_DIR, run qnx-patches/setup-qnx.sh to generate it"
    exit 1
fi

if [ ! -d "$BUNDLE_DIR" ]; then
    echo "Bundle not found at $BUNDLE_DIR"
    exit 1
fi

# Check if we have the docker container necessary for the bundle build
cd "$BUNDLE_DIR/toolchain"
if [ ! -f "./build-build.sh" ]; then
    echo "Error: build-build.sh not found in $BUNDLE_DIR/toolchain"
    exit 1
fi
source ./build-build.sh # Docker image for that bundle build


VOLUME_MAP="--volume /dev:/dev:rw \
--volume /lib/modules:/lib/modules:rw \
--volume ${BUNDLE_DIR}:/opt/qcom/qrtek/mybundle:rw \
--volume /local/mnt/rideconsole/runtime:/runtime:rw \
--volume ${QNX_DIR}:/qnx:rw \
--volume ${QNX_LICENSE_DIR}:/root/.qnx/license:rw \
--volume ${PREBUILT_DIR}:/qnx/my_prebuilt_sdp800:rw"

# Verify if we have enough disk space to build the platform
min_required_space_gb=860
available_space_kb=$(df --output=avail -k "/local/mnt/rideconsole/runtime" | tail -n 1)
available_space_gb=$(awk "BEGIN {printf \"%.2f\n\", $available_space_kb / 1024 / 1024}")

if [ "$(echo "$available_space_gb <= $min_required_space_gb" | bc -l)" -eq 1 ]; then
    echo "Error: Not enough disk space to build the platform"
    echo "Available: ${available_space_gb}GB, Required: ${min_required_space_gb}GB"
    exit 1
fi

QNX_SWC_URL="http://anvil.bts.rim.net"
GITORG="research-in-motion-limited"

if [ -n "$MYQNX_USERNAME" ] && [ -n "$MYQNX_PASSWORD" ] && [ -n "$QC_USER" ] && [ -n "$QC_PASSWORD" ] && [ -n "$CODELINARO_ACCESS_TOKEN" ]; then
    echo "Using environment variables for credentials"
else
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        echo "Error: Required environment variables not found and .env file not found."
        echo "Either set the following environment variables:"
        echo "  MYQNX_USERNAME, MYQNX_PASSWORD, QC_USER, QC_PASSWORD, CODELINARO_ACCESS_TOKEN"
        echo "Or create .env file from .env.example"
        exit 1
    fi
    echo "Loading credentials from .env file"
    source $SCRIPT_DIR/.env
fi

# Qualcomm Script parameters (for download-compile-build.sh)
SCRIPT_ARGS=" --debug \
--buildpath /runtime/build/8255_4890_hqxla/ \
--qcuser ${QC_USER} \
--qcpassword ${QC_PASSWORD} \
--qnx_swc_url ${QNX_SWC_URL} \
--qnxuser ${MYQNX_USERNAME} \
--qnxpassword ${MYQNX_PASSWORD} \
--gitorg ${GITORG} \
--codelinarotoken ${CODELINARO_ACCESS_TOKEN} \
--qnx_prebuilt_dir /qnx/my_prebuilt_sdp800"


DOCKER_CMD="docker run \
-e PYTHONDONTWRITEBYTECODE=1 \
--ipc=host --net=host \
${VOLUME_MAP} ${QRIDE_TOOLCHAIN} \
/opt/qcom/qrtek/mybundle/toolkit/download-compile-build.sh ${SCRIPT_ARGS}"

$DOCKER_CMD

export BUILD_DIR
echo "BUILD_DIR=${BUILD_DIR}"
echo "BUILD_DIR=${BUILD_DIR}" >> "$SCRIPT_DIR/runtime.vars"