#!/bin/bash

# Generates CDT (Configuration Data Table) packages for both SX and MX configurations
# for the SA8255P platform. Run this after build-platform.sh and build-flatten.sh.
# Inspiried from r00002.1/toolkit/flash.sh
#
# This script creates two separate CDT packages:
# - CDT_PACKAGE_SX: For single SoC (LEMANSAU_QAM_1.1.0.bin)
# - CDT_PACKAGE_MX: For dual SoC (LEMANSAU_QAM_2.1.0.bin)
#
# Each package contains all necessary files for CDT provisioning:
# - CDT binary file (LEMANSAU_QAM_*.bin)
# - Firehose programmer (prog_firehose_ddr.elf)
# - UFS configuration files (gpt_*.bin, rawprogram*.xml, patch*.xml)
# - Partition and provisioning XML files
#
# Usage: ./build-cdt-packages.sh
# No arguments required - paths are determined automatically.
set -e


SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

if [ -f "$SCRIPT_DIR/runtime.vars" ]; then
    source "$SCRIPT_DIR/runtime.vars"
fi

BUILD_DIR="${BUILD_DIR:-/local/mnt/rideconsole/runtime/build/8255_4890_hqxla/r00002.1}"

source "$SCRIPT_DIR/r00002.1/toolkit/bsp_config"

FLAT_BUILD_DIR="$BUILD_DIR/FLAT_BUILD"
UFS_DIR="$BUILD_DIR/common/build/ufs/$PRODUCT_FLAVOR"

PROG_FILE="$BUILD_DIR/boot/BIN_${AU_TYPE}_AU/boot_images/boot/QcomPkg/SocPkg/$AU_TYPE/Bin/AU/DEBUG/prog_firehose_ddr.elf"
if [ ! -f "$PROG_FILE" ]; then
    PROG_FILE="$BUILD_DIR/boot/boot_images/boot/QcomPkg/SocPkg/$AU_TYPE/Bin/AU/DEBUG/prog_firehose_ddr.elf"
fi

PARTITION_XML=$(find "$BUILD_DIR/common/config/ufs/$PRODUCT_FLAVOR" -name "partition_ext.xml" | head -1)
UFS_XML=$(find "$BUILD_DIR/common" -name "provision_default.xml" | head -1)

# Create CDT packages for both SX and MX configurations
for config in SX:LEMANSAU_QAM_1.1.0.bin MX:LEMANSAU_QAM_2.1.0.bin; do
    config_type=${config%:*}
    cdt_filename=${config#*:}
    package_dir="$FLAT_BUILD_DIR/CDT_$config_type"

    mkdir -p "$package_dir"

    cp "$UFS_DIR"/gpt_*3.bin "$package_dir/"
    cp "$UFS_DIR"/patch3.xml "$package_dir/"
    cp "$UFS_DIR"/rawprogram3.xml "$package_dir/"
    cp "$PROG_FILE" "$package_dir/"
    cp "$PARTITION_XML" "$package_dir/"
    cp "$UFS_XML" "$package_dir/"
    cp "$BUILD_DIR/common/cdt/$cdt_filename" "$package_dir/"

    sed -i '/label="cdt"/ s/filename=""/filename="'"$cdt_filename"'"/' "$package_dir/rawprogram3.xml"

    echo "Created $config_type package: $package_dir"
done